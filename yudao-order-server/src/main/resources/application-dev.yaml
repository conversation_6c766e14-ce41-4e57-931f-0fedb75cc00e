server:
  port: 38080

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: *************************************************************************************************************************************************************************** # MySQL Connector/J 8.X 连接的示例
          username: root
          password: 123456

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: 127.0.0.1 # 地址
      port: 6379 # 端口
      database: 1 # 数据库索引
#    password: 123456 # 密码，建议生产环境开启

--- #################### 定时任务相关配置 ####################

# Quartz 配置项，对应 QuartzProperties 配置类
spring:
  quartz:
    auto-startup: false # 测试环境，需要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

--- #################### 芋道相关配置 ####################

# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  captcha:
    enable: false
  access-log: # 访问日志的配置项
    enable: true
  demo: false # 开启演示模式

supplier:
  fulu:
    api:
      version: '2.0'
      app-key: i4esv1l+76l/7NQCL3QudG90Fq+YgVfFGJAWgT+7qO1Bm9o/adG/1iwO2qXsAXNB
      app-auth-token: ""
      charset: utf-8
      sign-type: md5
      domain: http://pre.openapi.fulu.com/api/getway
      format: json
      app-secret: 0a091b3aa4324435aab703142518a8f7
      interface-class-name: FuluGateway
  changsheng:
    api:
      version: '2.0'
      app-key: ""
      app-auth-token: ""
      charset: utf-8
      sign-type: md5
      domain: ""
      format: json
      app-secret: ""
      interface-class-name: ChangshengGateway
  aiyiqi:
    api:
      interface-class-name: AiqiyiGateway
  jiazhiyi:
    api:
      interface-class-name: JiazhiyiGateway
      callback-url: http://192.168.50.60:38080/app-api/callback/jiazhiyi/order-recharge-result
  yiqida:
    api:
      interface-class-name: YiqidaGateway
      callback-url: http://192.168.50.60:38080/app-api/callback/yiqida/order-recharge-result
  youkayun:
    api:
      interface-class-name: YoukayunGateway
      callback-url: http://192.168.50.60:38080/app-api/callback/youkayun/order-recharge-result
  jingyin:
    api:
      interface-class-name: JingYinGateway
      callback-url: http://192.168.50.60:38080/app-api/callback/jingyin/order-recharge-result
  wenyue:
    api:
      interface-class-name: WenYueGateway
      callback-url: http://192.168.50.60:38080/app-api/callback/wenyue/order-recharge-result
operate:
  log:
    clean-retain-days: 15