package cn.iocoder.yudao.order.server;

import cn.iocoder.yudao.framework.operatelog.config.YudaoOperateLogConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.retry.annotation.EnableRetry;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${yudao.info.base-package}
@SpringBootApplication(exclude = YudaoOperateLogConfiguration.class)
@ComponentScan(
        basePackages = {"${yudao.info.base-package}.order.server", "${yudao.info.base-package}.module"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "cn.iocoder.yudao.module.infra.controller.*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "cn.iocoder.yudao.module.system.controller.*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "cn.iocoder.yudao.module.system.controller.*"),
        }
)
@EnableRetry
@EnableFeignClients(basePackages = "cn.iocoder.yudao.module.integration.client")
public class YudaoOrderServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(YudaoOrderServerApplication.class, args);
    }

}
