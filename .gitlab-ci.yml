stages:
  - build_dev
  - deploy_dev
  - build_stage
  - deploy_stage
  - build_prod
  - deploy_prod

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  REGISTRY_URL: "registry.xiaobao.cool"
  DEV_REMOTE_USER: "xia<PERSON><PERSON>@*************"
  SSH_PASSWORD: "Ld,Nl0vptzp44xUacflz"
  SSH_OPTS: "-o StrictHostKeyChecking=no"
  ADMIN_IMAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:admin-backend-dev-$CI_COMMIT_SHORT_SHA"
  ADMIN_IMAGE_STAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:admin-backend-stage-$CI_COMMIT_SHORT_SHA"
  ADMIN_IMAGE_PROD_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:admin-backend-prod-$CI_COMMIT_SHORT_SHA"
  SHOP_IMAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:shop-backend-dev-$CI_COMMIT_SHORT_SHA"
  SHOP_IMAGE_STAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:shop-backend-stage-$CI_COMMIT_SHORT_SHA"
  SHOP_IMAGE_PROD_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:shop-backend-prod-$CI_COMMIT_SHORT_SHA"
  ORDER_IMAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:order-api-dev-$CI_COMMIT_SHORT_SHA"
  ORDER_IMAGE_STAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:order-api-stage-$CI_COMMIT_SHORT_SHA"
  ORDER_IMAGE_PROD_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:order-api-prod-$CI_COMMIT_SHORT_SHA"
  VOUCHER_IMAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:voucher-api-dev-$CI_COMMIT_SHORT_SHA"
  VOUCHER_IMAGE_STAGE_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:voucher-api-stage-$CI_COMMIT_SHORT_SHA"
  VOUCHER_IMAGE_PROD_TAG: "$REGISTRY_URL/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:voucher-api-prod-$CI_COMMIT_SHORT_SHA"

cache:
  paths:
    - .m2/repository/
    - target/

build-admin-dev-job:
  stage: build_dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep admin-backend-dev | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$ADMIN_IMAGE_TAG" --target server . -f Dockerfile
    - nerdctl push $ADMIN_IMAGE_TAG
    - echo "Compile complete."

build-shop-dev-job:
  stage: build_dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep shop-backend-dev | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$SHOP_IMAGE_TAG" --target shopServer . -f Dockerfile
    - nerdctl push $SHOP_IMAGE_TAG
    - echo "Compile complete."

build-order-dev-job:
  stage: build_dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep order-api-dev | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$ORDER_IMAGE_TAG" --target orderServer . -f Dockerfile
    - nerdctl push $ORDER_IMAGE_TAG
    - echo "Compile complete."

build-voucher-dev-job:
  stage: build_dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep voucher-api-dev | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$VOUCHER_IMAGE_TAG" --target voucherServer . -f Dockerfile
    - nerdctl push $VOUCHER_IMAGE_TAG
    - echo "Compile complete."

deploy-admin-dev-job:
  stage: deploy_dev
  environment: dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  needs: ["build-admin-dev-job"]
  before_script:
    - sudo apt-get update && sudo apt-get install -y sshpass
    - |
      function sudo_exec() {
        sshpass -p "${SSH_PASSWORD}" ssh ${SSH_OPTS} ${DEV_REMOTE_USER} "echo ${SSH_PASSWORD} | sudo -S sh -c '$1'"
      }
  script:
    - |
      echo "Deploying admin backend application..."
      echo $ADMIN_IMAGE_TAG
      echo "Copying docker compose files..."
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} docker-compose-admin.yml ${DEV_REMOTE_USER}:/data
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} docker-compose-infra-dev.yml ${DEV_REMOTE_USER}:/data
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} env/dev.env ${DEV_REMOTE_USER}:/data
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$ADMIN_IMAGE_TAG" > quanyi-admin-backend.tar
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} quanyi-admin-backend.tar ${DEV_REMOTE_USER}:~
      sudo_exec "nerdctl load < quanyi-admin-backend.tar"
      sudo_exec "cd /data && sed -i \"/container_name: admin-backend/,/^[^ ]/  s|image:.*|image: ${ADMIN_IMAGE_TAG}|\" docker-compose-admin.yml"
      echo "Starting containers..."
      sudo_exec "cd /data &&  nerdctl compose -f docker-compose-infra-dev.yml up -d"
      sudo_exec "cd /data &&  nerdctl compose -f docker-compose-admin.yml --env-file dev.env up -d"
      sudo_exec "nerdctl image prune -af"
      rm -rf /tmp/quanyi-admin-backend.tar
      echo "Application successfully deployed."

deploy-shop-dev-job:
  stage: deploy_dev
  environment: dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  needs: ["build-shop-dev-job"]
  before_script:
    - sudo apt-get update && sudo apt-get install -y sshpass
    - |
      function sudo_exec() {
        sshpass -p "${SSH_PASSWORD}" ssh ${SSH_OPTS} ${DEV_REMOTE_USER} "echo ${SSH_PASSWORD} | sudo -S sh -c '$1'"
      }
  script:
    - |
      echo "Deploying shop backend application..."
      echo $SHOP_IMAGE_TAG
      echo "Copying docker compose files..."
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} docker-compose-shop.yml ${DEV_REMOTE_USER}:/data
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} env/dev.env ${DEV_REMOTE_USER}:/data
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$SHOP_IMAGE_TAG" > quanyi-shop-backend.tar
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} quanyi-shop-backend.tar ${DEV_REMOTE_USER}:~
      sudo_exec "nerdctl load < quanyi-shop-backend.tar"
      sudo_exec "cd /data && sed -i \"/container_name: shop-backend/,/^[^ ]/  s|image:.*|image: ${SHOP_IMAGE_TAG}|\" docker-compose-shop.yml"
      echo "Starting containers..."
      sudo_exec "cd /data &&  nerdctl compose -f docker-compose-shop.yml --env-file dev.env up -d"
      sudo_exec "nerdctl image prune -af"
      rm -rf /tmp/quanyi-shop-backend.tar
      echo "Application successfully deployed."

deploy-order-dev-job:
  stage: deploy_dev
  environment: dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  needs: ["build-order-dev-job"]
  before_script:
    - sudo apt-get update && sudo apt-get install -y sshpass
    - |
      function sudo_exec() {
        sshpass -p "${SSH_PASSWORD}" ssh ${SSH_OPTS} ${DEV_REMOTE_USER} "echo ${SSH_PASSWORD} | sudo -S sh -c '$1'"
      }
  script:
    - |
      echo "Deploying order api..."
      echo $ORDER_IMAGE_TAG
      echo "Copying docker compose files..."
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} docker-compose-order.yml ${DEV_REMOTE_USER}:/data
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} env/dev.env ${DEV_REMOTE_USER}:/data
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$ORDER_IMAGE_TAG" > quanyi-order-api.tar
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} quanyi-order-api.tar ${DEV_REMOTE_USER}:~
      sudo_exec "nerdctl load < quanyi-order-api.tar"
      sudo_exec "cd /data && sed -i \"/container_name: order-api/,/^[^ ]/  s|image:.*|image: ${ORDER_IMAGE_TAG}|\" docker-compose-order.yml"
      echo "Starting containers..."
      sudo_exec "cd /data &&  nerdctl compose -f docker-compose-order.yml --env-file dev.env up -d"
      sudo_exec "nerdctl image prune -af"
      rm -rf /tmp/quanyi-order-api.tar
      echo "Application successfully deployed."

deploy-voucher-dev-job:
  stage: deploy_dev
  environment: dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  needs: ["build-voucher-dev-job"]
  before_script:
    - sudo apt-get update && sudo apt-get install -y sshpass
    - |
      function sudo_exec() {
        sshpass -p "${SSH_PASSWORD}" ssh ${SSH_OPTS} ${DEV_REMOTE_USER} "echo ${SSH_PASSWORD} | sudo -S sh -c '$1'"
      }
  script:
    - |
      echo "Deploying voucher api..."
      echo $VOUCHER_IMAGE_TAG
      echo "Copying docker compose files..."
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} docker-compose-voucher.yml ${DEV_REMOTE_USER}:/data
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} env/dev.env ${DEV_REMOTE_USER}:/data
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$VOUCHER_IMAGE_TAG" > quanyi-voucher-api.tar
      sshpass -p "${SSH_PASSWORD}" scp ${SSH_OPTS} quanyi-voucher-api.tar ${DEV_REMOTE_USER}:~
      sudo_exec "nerdctl load < quanyi-voucher-api.tar"
      sudo_exec "cd /data && sed -i \"/container_name: voucher-api/,/^[^ ]/  s|image:.*|image: ${VOUCHER_IMAGE_TAG}|\" docker-compose-voucher.yml"
      echo "Starting containers..."
      sudo_exec "cd /data &&  nerdctl compose -f docker-compose-voucher.yml --env-file dev.env up -d"
      sudo_exec "nerdctl image prune -af"
      rm -rf /tmp/quanyi-voucher-api.tar
      echo "Application successfully deployed."

build-admin-stage-job:
  stage: build_stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep admin-backend-stage | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$ADMIN_IMAGE_STAGE_TAG" --target server . -f Dockerfile
    - nerdctl push $ADMIN_IMAGE_STAGE_TAG
    - echo "Compile complete."

build-shop-stage-job:
  stage: build_stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep shop-backend-stage | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$SHOP_IMAGE_STAGE_TAG" --target shopServer . -f Dockerfile
    - nerdctl push $SHOP_IMAGE_STAGE_TAG
    - echo "Compile complete."

build-order-stage-job:
  stage: build_stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep order-api-stage | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$ORDER_IMAGE_STAGE_TAG" --target orderServer . -f Dockerfile
    - nerdctl push $ORDER_IMAGE_STAGE_TAG
    - echo "Compile complete."

build-voucher-stage-job:
  stage: build_stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep voucher-api-stage | awk '{print $3}') || true
    - echo "Compiling the code..."
    - nerdctl build -t "$VOUCHER_IMAGE_STAGE_TAG" --target voucherServer . -f Dockerfile
    - nerdctl push $VOUCHER_IMAGE_STAGE_TAG
    - echo "Compile complete."

deploy-admin-stage-job:
  stage: deploy_stage
  environment: stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs: ["build-admin-stage-job"]
  script:
    - |
      echo "Deploying admin backend application..."
      echo $ADMIN_IMAGE_STAGE_TAG
      echo "Copying docker compose files..."
      sudo miaocai_scp ************ docker-compose-admin-stage.yml 
      sudo miaocai_scp ************ env/stage.env 
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$ADMIN_IMAGE_STAGE_TAG" > quanyi-admin-stage-backend.tar
      sudo miaocai_scp ************ quanyi-admin-stage-backend.tar
      sudo miaocai_ssh ************ "sudo docker load < quanyi-admin-stage-backend.tar"
      sudo miaocai_ssh ************ "sudo sed -i \"/container_name: admin-backend/,/^[^ ]/  s|image:.*|image: ${ADMIN_IMAGE_STAGE_TAG}|\" docker-compose-admin-stage.yml"
      echo "Starting containers..." 
      sudo miaocai_ssh ************ "sudo docker compose -f docker-compose-admin-stage.yml --env-file stage.env up -d"
      sudo miaocai_ssh ************ "sudo docker image prune -af"
      rm -rf /tmp/quanyi-admin-stage-backend.tar
      echo "Application successfully deployed."

deploy-shop-stage-job:
  stage: deploy_stage
  environment: stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs: ["build-shop-stage-job"]
  script:
    - |
      echo "Deploying shop backend application..."
      echo $SHOP_IMAGE_STAGE_TAG
      echo "Copying docker compose files..."
      sudo miaocai_scp ************ docker-compose-shop-stage.yml
      sudo miaocai_scp ************ env/stage.env
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$SHOP_IMAGE_STAGE_TAG" > quanyi-shop-stage-backend.tar
      sudo miaocai_scp ************ quanyi-shop-stage-backend.tar
      sudo miaocai_ssh ************ "sudo docker load < quanyi-shop-stage-backend.tar"
      sudo miaocai_ssh ************ "sudo sed -i \"/container_name: shop-backend/,/^[^ ]/  s|image:.*|image: ${SHOP_IMAGE_STAGE_TAG}|\" docker-compose-shop-stage.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************ "sudo docker compose -f docker-compose-shop-stage.yml --env-file stage.env up -d"
      sudo miaocai_ssh ************ "sudo docker image prune -af"
      rm -rf /tmp/quanyi-shop-stage-backend.tar
      echo "Application successfully deployed."

deploy-order-stage-job:
  stage: deploy_stage
  environment: stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs: ["build-order-stage-job"]
  script:
    - |
      echo "Deploying order api..."
      echo $ORDER_IMAGE_STAGE_TAG
      echo "Copying docker compose files..."
      sudo miaocai_scp ************ docker-compose-order-stage.yml
      sudo miaocai_scp ************ env/stage.env
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$ORDER_IMAGE_STAGE_TAG" > quanyi-order-api-stage.tar
      sudo miaocai_scp ************ quanyi-order-api-stage.tar
      sudo miaocai_ssh ************ "sudo docker load < quanyi-order-api-stage.tar"
      sudo miaocai_ssh ************ "sudo sed -i \"/container_name: order-api/,/^[^ ]/  s|image:.*|image: ${ORDER_IMAGE_STAGE_TAG}|\" docker-compose-order-stage.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************ "sudo docker compose -f docker-compose-order-stage.yml --env-file stage.env up -d"
      sudo miaocai_ssh ************ "sudo docker image prune -af"
      rm -rf /tmp/quanyi-order-api-stage.tar
      echo "Application successfully deployed."

deploy-voucher-stage-job:
  stage: deploy_stage
  environment: stage
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs: ["build-voucher-stage-job"]
  script:
    - |
      echo "Deploying voucher api..."
      echo $VOUCHER_IMAGE_STAGE_TAG
      echo "Copying docker compose files..."
      sudo miaocai_scp ************ docker-compose-voucher-stage.yml
      sudo miaocai_scp ************ env/stage.env
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$VOUCHER_IMAGE_STAGE_TAG" > quanyi-voucher-api-stage.tar
      sudo miaocai_scp ************ quanyi-voucher-api-stage.tar
      sudo miaocai_ssh ************ "sudo docker load < quanyi-voucher-api-stage.tar"
      sudo miaocai_ssh ************ "sudo sed -i \"/container_name: voucher-api/,/^[^ ]/  s|image:.*|image: ${VOUCHER_IMAGE_STAGE_TAG}|\" docker-compose-voucher-stage.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************ "sudo docker compose -f docker-compose-voucher-stage.yml --env-file stage.env up -d"
      sudo miaocai_ssh ************ "sudo docker image prune -af"
      rm -rf /tmp/quanyi-voucher-api-stage.tar
      echo "Application successfully deployed."

build-admin-prod-job:
  stage: build_prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs:
    - ["deploy-admin-stage-job"]
  when: manual
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep admin-backend-prod | awk '{print $3}') || true
    - echo "Building production admin image..."
    - nerdctl build -t "$ADMIN_IMAGE_PROD_TAG" --target server . -f Dockerfile
    - nerdctl push $ADMIN_IMAGE_PROD_TAG

build-shop-prod-job:
  stage: build_prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs:
    - [ "deploy-shop-stage-job" ]
  when: manual
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep shop-backend-prod | awk '{print $3}') || true
    - echo "Building production shop image..."
    - nerdctl build -t "$SHOP_IMAGE_PROD_TAG" --target shopServer . -f Dockerfile
    - nerdctl push $SHOP_IMAGE_PROD_TAG

build-order-prod-job:
  stage: build_prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs:
    - [ "deploy-order-stage-job" ]
  when: manual
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep order-api-prod | awk '{print $3}') || true
    - echo "Building production order image..."
    - nerdctl build -t "$ORDER_IMAGE_PROD_TAG" --target orderServer . -f Dockerfile
    - nerdctl push $ORDER_IMAGE_PROD_TAG

build-voucher-prod-job:
  stage: build_prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  needs:
    - [ "deploy-voucher-stage-job" ]
  when: manual
  script:
    - echo "Clear Old Images..."
    - nerdctl rmi -f $(nerdctl image ls | grep voucher-api-prod | awk '{print $3}') || true
    - echo "Building production voucher image..."
    - nerdctl build -t "$VOUCHER_IMAGE_PROD_TAG" --target voucherServer . -f Dockerfile
    - nerdctl push $VOUCHER_IMAGE_PROD_TAG

deploy-admin-prod-job:
  stage: deploy_prod
  environment: prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  when: manual
  needs: ["build-admin-prod-job"]
  script:
    - |
      echo "Deploying admin backend..."
      echo $ADMIN_IMAGE_PROD_TAG
      echo "Copying docker compose file..."
      sudo miaocai_scp ************* docker-compose-admin.yml 
      sudo miaocai_scp ************* env/prod.env  
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$ADMIN_IMAGE_PROD_TAG" > quanyi-admin-backend.tar
      sudo miaocai_scp ************* quanyi-admin-backend.tar 
      sudo miaocai_ssh ************* "sudo docker load < quanyi-admin-backend.tar"
      sudo miaocai_ssh ************* "sudo sed -i \"/container_name: admin-backend/,/^[^ ]/  s|image:.*|image: ${ADMIN_IMAGE_PROD_TAG}|\" docker-compose-admin.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************* "sudo docker compose -f docker-compose-admin.yml --env-file prod.env up -d"
      sudo miaocai_ssh ************* "sudo docker image prune -af"
      rm -rf /tmp/quanyi-admin-backend.tar
      echo "Application successfully deployed."

deploy-shop-prod-job:
  stage: deploy_prod
  environment: prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  when: manual
  needs: ["build-shop-prod-job"]
  script:
    - |
      echo "Deploying shop backend..."
      echo $SHOP_IMAGE_PROD_TAG
      echo "Copying docker compose file..."
      sudo miaocai_scp ************* docker-compose-shop.yml
      sudo miaocai_scp ************* env/prod.env  
      echo "Loading docker image..."
      cd /tmp && nerdctl save "$SHOP_IMAGE_PROD_TAG" > quanyi-shop-backend.tar
      sudo miaocai_scp ************* quanyi-shop-backend.tar 
      sudo miaocai_ssh ************* "sudo docker load < quanyi-shop-backend.tar"
      sudo miaocai_ssh ************* "sudo sed -i \"/container_name: shop-backend/,/^[^ ]/  s|image:.*|image: ${SHOP_IMAGE_PROD_TAG}|\" docker-compose-shop.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************* "sudo docker compose -f docker-compose-shop.yml --env-file prod.env up -d"
      sudo miaocai_ssh ************* "sudo docker image prune -af"
      rm -rf /tmp/quanyi-shop-backend.tar
      echo "Application successfully deployed."

deploy-order-prod-job:
  stage: deploy_prod
  environment: prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  when: manual
  needs: ["build-order-prod-job"]
  script:
    - |
      echo "Deploying order api to prod"
      echo $ORDER_IMAGE_PROD_TAG
      echo "Copy docker compose file to remote server"
      sudo miaocai_scp ************* docker-compose-order-prod.yml
      sudo miaocai_scp ************* env/prod.env  
      echo "Load docker image to remote server"
      cd /tmp && nerdctl save "$ORDER_IMAGE_PROD_TAG" > quanyi-order-backend.tar
      sudo miaocai_scp ************* quanyi-order-backend.tar 
      sudo miaocai_ssh ************* "sudo docker load < quanyi-order-backend.tar"
      sudo miaocai_ssh ************* "sudo sed -i \"/container_name: order-api-38080/,/^[^ ]/ s|image:.*|image: ${ORDER_IMAGE_PROD_TAG}|; /container_name: order-api-38031/,/^[^ ]/ s|image:.*|image: ${ORDER_IMAGE_PROD_TAG}|\" docker-compose-order-prod.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************* "sudo docker compose -f docker-compose-order-prod.yml --env-file prod.env up -d"
      sudo miaocai_ssh ************* "sudo docker image prune -af"
      rm -rf /tmp/quanyi-order-backend.tar
      echo "Application successfully deployed."

deploy-voucher-prod-job:
  stage: deploy_prod
  environment: prod
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  when: manual
  needs: ["build-voucher-prod-job"]
  script:
    - |
      echo "Deploying voucher api to prod"
      echo $VOUCHER_IMAGE_PROD_TAG
      echo "Copy docker compose file to remote server"
      sudo miaocai_scp ************* docker-compose-voucher-prod.yml
      sudo miaocai_scp ************* env/prod.env  
      echo "Load docker image to remote server"
      cd /tmp && nerdctl save "$VOUCHER_IMAGE_PROD_TAG" > quanyi-voucher-backend.tar
      sudo miaocai_scp ************* quanyi-voucher-backend.tar 
      sudo miaocai_ssh ************* "sudo docker load < quanyi-voucher-backend.tar"
      sudo miaocai_ssh ************* "sudo sed -i \"/container_name: voucher-api-58080/,/^[^ ]/ s|image:.*|image: ${VOUCHER_IMAGE_PROD_TAG}|; /container_name: voucher-api-58031/,/^[^ ]/ s|image:.*|image: ${VOUCHER_IMAGE_PROD_TAG}|\" docker-compose-voucher-prod.yml"
      echo "Starting containers..."
      sudo miaocai_ssh ************* "sudo docker compose -f docker-compose-voucher-prod.yml --env-file prod.env up -d"
      sudo miaocai_ssh ************* "sudo docker image prune -af"
      rm -rf /tmp/quanyi-voucher-backend.tar
      echo "Application successfully deployed."