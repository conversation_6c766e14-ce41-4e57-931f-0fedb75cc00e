name: quanyi-admin-stage

services:
  quanyi-mysql:
      container_name: quanyi-mysql-stage
      image: mysql:8
      ports:
        - "3306:3306"
      restart: always
      networks:
        - quanyi_network
      environment:
        MYSQL_DATABASE: ${MYSQL_DATABASE:-quanyi_system}
        MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
      volumes:
        - mysql:/var/lib/mysql/
      mem_limit: 512m

  quanyi-redis:
      container_name: quanyi-redis-stage
      image: redis:6-alpine
      ports:
        - "6379:6379"
      restart: always
      networks:
        - quanyi_network
      volumes:
        - redis:/data
      mem_limit: 512m

  admin-backend:
    container_name: admin-backend-stage
    image: admin-backend
    ports:
      - "48080:48080"
    restart: always
    networks:
      - quanyi_network
    environment:
      TOPIC_ID: ${ADMIN_TOPIC_ID:-2785deb1-8abb-4543-a96e-9613e14f7db3}
      SPRING_PROFILES_ACTIVE: ${ACTIVE_PROFILES:-stage}
      JAVA_OPTS: |
        -Xms512m
        -Xmx512m
        -Djava.security.egd=file:/dev/./urandom
      ARGS:
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.flyway.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.flyway.user=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.flyway.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-quanyi-redis}
        ${REDIS_PASSWORD_ARG}
    logging:
      driver: "json-file"
      options:
        max-size: "2g"
        max-file: "3"

volumes:
  mysql:
    driver: local
  redis:
    driver: local

networks:
  quanyi_network:
    name: quanyi_network
    driver: bridge