name: quanyi-shop-stage

services:
  shop-backend:
    container_name: shop-backend-stage
    image: admin-backend
    ports:
      - "58080:58080"
    restart: always
    networks:
      - quanyi_network
    environment:
      TOPIC_ID: ${SHOP_TOPIC_ID:-101cf52c-a10c-4178-a4e4-315d89ac8fdd}
      SPRING_PROFILES_ACTIVE: ${ACTIVE_PROFILES:-stage}
      JAVA_OPTS: |
        -Xms512m
        -Xmx512m
        -Djava.security.egd=file:/dev/./urandom
      ARGS: |
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-quanyi-redis}
        ${REDIS_PASSWORD_ARG}
    logging:
      driver: "json-file"
      options:
        max-size: "2g"
        max-file: "3"

networks:
  quanyi_network:
    name: quanyi_network
    external: true
