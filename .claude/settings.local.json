{"permissions": {"allow": ["Bash(java:*)", "<PERSON><PERSON>(mvn:*)", "Bash(.specify/scripts/create-new-feature.sh:*)", "Bash(.specify/scripts/setup-plan.sh:*)", "Bash(.specify/scripts/check-task-prerequisites.sh:*)", "Bash(.specify/scripts/bash/create-new-feature.sh:*)", "Bash(git branch:*)", "Bash(.specify/scripts/bash/setup-plan.sh:*)", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "mcp__serena__list_dir", "mcp__serena__find_symbol", "mcp__serena__get_symbols_overview", "Bash(.specify/scripts/bash/check-task-prerequisites.sh:*)", "<PERSON><PERSON>(./mvnw clean:*)", "Bash(bash:*)", "Bash(/Users/<USER>/.local/share/mise/installs/maven/3.9.6/bin/mvn --version)"], "deny": [], "ask": []}, "enabledMcpjsonServers": ["serena"]}