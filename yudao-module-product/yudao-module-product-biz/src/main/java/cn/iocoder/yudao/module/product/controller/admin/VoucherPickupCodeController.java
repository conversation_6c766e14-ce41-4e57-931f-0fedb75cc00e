package cn.iocoder.yudao.module.product.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.product.controller.admin.vo.*;
import cn.iocoder.yudao.module.product.service.VoucherPickupCodeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.product.utils.VoucherLogRecordConstants.*;

/**
 * 提货码管理 Controller
 */
@Tag(name = "管理后台 - 提货码管理")
@RestController
@RequestMapping("/product/voucher-pickup-code")
@Validated
@RequiredArgsConstructor
public class VoucherPickupCodeController {

    private final VoucherPickupCodeService voucherPickupCodeService;

    @GetMapping("/page")
    @Operation(summary = "获得提货码分页")
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:query')")
    public CommonResult<PageResult<PickupCodeRespVO>> getCodePage(@Valid PickupCodePageReqVO pageVO) {
        PageResult<PickupCodeRespVO> pageResult = voucherPickupCodeService.getCodePage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获得提货码详情")
    @Parameter(name = "id", description = "提货码ID", required = true)
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:query')")
    public CommonResult<PickupCodeRespVO> getCode(@PathVariable("id") Long id) {
        PickupCodeRespVO code = voucherPickupCodeService.getCode(id);
        return success(code);
    }

    @PostMapping("/validate")
    @Operation(summary = "验证提货码")
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:validate')")
    @LogRecord(type = VOUCHER_PICKUP_CODE, subType = VOUCHER_PICKUP_CODE_VALIDATE, bizNo = "{{#validateReqVO.code}}",
            success = VOUCHER_PICKUP_CODE_DATA_VALIDATE_SUCCESS)
    public CommonResult<PickupCodeRespVO> validateCode(@Valid @RequestBody PickupCodeValidateReqVO validateReqVO) {
        PickupCodeRespVO code = voucherPickupCodeService.validateCode(validateReqVO);
        return success(code);
    }

    @PostMapping("/outbound")
    @Operation(summary = "出库提货码")
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:outbound')")
    @LogRecord(type = VOUCHER_PICKUP_CODE, subType = VOUCHER_PICKUP_CODE_OUTBOUND, bizNo = "{{#outboundReqVO.code}}",
            success = VOUCHER_PICKUP_CODE_DATA_OUTBOUND_SUCCESS)
    public CommonResult<Boolean> outboundCode(@Valid @RequestBody PickupCodeOutboundReqVO outboundReqVO) {
        Boolean result = voucherPickupCodeService.outboundCode(outboundReqVO);
        return success(result);
    }

    @PostMapping("/{id}/void")
    @Operation(summary = "作废提货码")
    @Parameter(name = "id", description = "提货码ID", required = true)
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:update')")
    @LogRecord(type = VOUCHER_PICKUP_CODE, subType = VOUCHER_PICKUP_CODE_VOID, bizNo = "{{#id}}",
            success = VOUCHER_PICKUP_CODE_DATA_VOID_SUCCESS)
    public CommonResult<Boolean> voidCode(@PathVariable("id") Long id) {
        voucherPickupCodeService.voidCode(id);
        return success(true);
    }
    @DeleteMapping("/{id}")
    @Operation(summary = "删除提货码")
    @Parameter(name = "id", description = "提货码ID", required = true)
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:delete')")
    @LogRecord(type = VOUCHER_PICKUP_CODE, subType = VOUCHER_PICKUP_CODE_DELETE, bizNo = "{{#id}}",
            success = VOUCHER_PICKUP_CODE_DATA_DELETE_SUCCESS)
    public CommonResult<Boolean> deleteCode(@PathVariable("id") Long id) {
        voucherPickupCodeService.deleteCode(id);
        return success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "更新提货码")
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:update')")
    @LogRecord(type = VOUCHER_PICKUP_CODE, subType = VOUCHER_PICKUP_CODE_UPDATE, bizNo = "{{#updateReqVO.id}}",
            success = VOUCHER_PICKUP_CODE_DATA_UPDATE_SUCCESS)
    public CommonResult<Boolean> updateCode(@Valid @RequestBody PickupCodeUpdateReqVO updateReqVO) {
        voucherPickupCodeService.updateCode(updateReqVO);
        return success(true);
    }

    @PostMapping("/batch-outbound")
    @Operation(summary = "批量出库提货码")
    @PreAuthorize("@ss.hasPermission('product:voucher-pickup-code:outbound')")
    @LogRecord(type = VOUCHER_PICKUP_CODE, subType = VOUCHER_PICKUP_CODE_OUTBOUND, bizNo = "{{#reqVO.batchId}}",
            success = VOUCHER_PICKUP_CODE_DATA_OUTBOUND_SUCCESS)
    public CommonResult<PickupCodeBatchOutboundRespVO> batchOutbound(@Valid @RequestBody PickupCodeBatchOutboundReqVO reqVO) {
        PickupCodeBatchOutboundRespVO result = voucherPickupCodeService.batchOutbound(reqVO);
        return success(result);
    }
}
