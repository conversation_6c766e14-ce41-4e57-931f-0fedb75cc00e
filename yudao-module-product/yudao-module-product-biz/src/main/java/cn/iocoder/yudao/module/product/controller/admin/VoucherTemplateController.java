package cn.iocoder.yudao.module.product.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.product.controller.admin.vo.*;
import cn.iocoder.yudao.module.product.service.VoucherTemplateService;
import cn.iocoder.yudao.module.merchant.service.MerchantService;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_NOT_EXISTS;

@Tag(name = "提货券模板管理")
@RestController
@RequestMapping("/product/voucher-template")
@Validated
public class VoucherTemplateController {

    @Resource
    private VoucherTemplateService voucherTemplateService;

    @Resource
    private MerchantService merchantService;

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('product:voucher-template:create')")
    @Operation(summary = "创建提货券模板")
    public CommonResult<Long> createVoucherTemplate(@Valid @RequestBody VoucherTemplateSaveVO createReqVO) {
        Long id = voucherTemplateService.createVoucherTemplate(createReqVO);
        return CommonResult.success(id);
    }

    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('product:voucher-template:update')")
    @Operation(summary = "更新提货券模板")
    public CommonResult<Boolean> updateVoucherTemplate(@Valid @RequestBody VoucherTemplateSaveVO updateReqVO) {
        voucherTemplateService.updateVoucherTemplate(updateReqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('product:voucher-template:delete')")
    @Operation(summary = "删除提货券模板")
    public CommonResult<Boolean> deleteVoucherTemplate(@RequestParam("id") Long id) {
        voucherTemplateService.deleteVoucherTemplate(id);
        return CommonResult.success(true);
    }

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('product:voucher-template:query')")
    @Operation(summary = "获取提货券模板分页")
    public CommonResult<PageResult<VoucherTemplateResVO>> getVoucherTemplatePage(VoucherTemplateQueryReqVO reqVO) {
        return CommonResult.success(voucherTemplateService.getVoucherTemplatePage(reqVO));
    }

    @GetMapping("/available/list")
    @PreAuthorize("@ss.hasPermission('merchant:product:bind')")
    @Operation(summary = "获取可用提货券模板列表", description = "用于商户绑定提货券时选择")
    public CommonResult<PageResult<VoucherTemplateResVO>> getAvailableVoucherTemplateList(VoucherTemplateQueryReqVO reqVO) {
        MerchantDO merchant = merchantService.getMerchant(reqVO.getMerchantId());
        if (merchant == null) {
            throw exception(MERCHANT_NOT_EXISTS);
        }

        reqVO.setExcludeIdList(merchant.getVoucherTemplateIdList());

        var result = voucherTemplateService.getVoucherTemplatePage(reqVO);
        return CommonResult.success(result);
    }

    @GetMapping("/list-all")
    @PreAuthorize("@ss.hasPermission('product:voucher-template:query')")
    @Operation(summary = "获取提货券模板分页")
    public CommonResult<List<VoucherTemplateResVO>> getVoucherTemplatePage() {
        return CommonResult.success(voucherTemplateService.getAllVoucherTemplate());
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('product:voucher-template:query')")
    @Operation(summary = "获取提货券模板详情")
    public CommonResult<VoucherTemplateResVO> getVoucherTemplate(@RequestParam("id") Long id) {
        return CommonResult.success(voucherTemplateService.getVoucherTemplate(id));
    }
}