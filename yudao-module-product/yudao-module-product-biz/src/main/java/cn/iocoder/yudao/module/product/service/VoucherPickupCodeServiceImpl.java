package cn.iocoder.yudao.module.product.service;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.RechargeReqVO;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantDO;
import cn.iocoder.yudao.module.merchant.enums.RechargeType;
import cn.iocoder.yudao.module.merchant.service.MerchantService;
import cn.iocoder.yudao.module.product.controller.admin.vo.*;
import cn.iocoder.yudao.module.product.controller.app.pickup.AppPickupCodeDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.pickup.AppPickupCodeProductsRespVO;
import cn.iocoder.yudao.module.product.convert.VoucherPickupCodeConvert;
import cn.iocoder.yudao.module.product.dal.dataobject.PickupCodeBatchDO;
import cn.iocoder.yudao.module.product.dal.dataobject.PickupCodeDO;
import cn.iocoder.yudao.module.product.dal.dataobject.VoucherTemplateDO;
import cn.iocoder.yudao.module.product.dal.dataobject.VoucherTemplatePlatformProductDO;
import cn.iocoder.yudao.module.product.dal.mysql.PickupCodeMapper;
import cn.iocoder.yudao.module.product.dal.mysql.PickupCodeBatchMapper;
import cn.iocoder.yudao.module.product.dal.mysql.PlatformProductMapper;
import cn.iocoder.yudao.module.product.dal.mysql.VoucherTemplateMapper;
import cn.iocoder.yudao.module.product.dal.mysql.VoucherTemplatePlatformProductMapper;
import cn.iocoder.yudao.module.product.utils.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.*;

/**
 * 提货码 Service 实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VoucherPickupCodeServiceImpl implements VoucherPickupCodeService {

    private final PickupCodeMapper pickupCodeMapper;
    private final PickupCodeBatchMapper pickupCodeBatchMapper;
    private final VoucherTemplateMapper voucherTemplateMapper;
    private final MerchantService merchantService;
    private final VoucherTemplatePlatformProductMapper voucherTemplatePlatformProductMapper;
    private final PlatformProductMapper platformProductMapper;

    // Validation predicates
    private static final Predicate<PickupCodeDO> IS_VALID_STATUS = code -> code.getCodeStatus() == 1;
    private static final Predicate<PickupCodeDO> IS_NOT_REDEEMED = any -> true;
    private static final Predicate<PickupCodeDO> IS_NOT_EXPIRED = code ->
        Optional.ofNullable(code.getExpireTime())
            .map(time -> time.isAfter(LocalDateTime.now()))
            .orElse(true);

    @Override
    public PageResult<PickupCodeRespVO> getCodePage(PickupCodePageReqVO pageReqVO) {
        PageResult<PickupCodeDO> pageResult = pickupCodeMapper.selectPage(pageReqVO,
            new LambdaQueryWrapperX<PickupCodeDO>()
                .eqIfPresent(PickupCodeDO::getBatchNumber, pageReqVO.getBatchNumber())
                .eqIfPresent(PickupCodeDO::getVoucherTemplateId, pageReqVO.getVoucherTemplateId())
                .eqIfPresent(PickupCodeDO::getOutboundStatus, pageReqVO.getOutboundStatus())
                .eqIfPresent(PickupCodeDO::getCodeStatus, pageReqVO.getCodeStatus())
                .likeIfPresent(PickupCodeDO::getCode, pageReqVO.getCode())
                .orderByDesc(PickupCodeDO::getId));

        List<PickupCodeRespVO> list = convertList(pageResult.getList(), this::convertCodeToRespVO);
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public PickupCodeRespVO getCode(Long id) {
        return Optional.ofNullable(pickupCodeMapper.selectById(id))
            .map(this::convertCodeToRespVO)
            .orElseThrow(() -> new ServiceException(PICKUP_CODE_NOT_EXISTS));
    }

    @Override
    public PickupCodeRespVO validateCode(PickupCodeValidateReqVO validateReqVO) {
        return Optional.ofNullable(pickupCodeMapper.selectByCode(validateReqVO.getCode()))
            .map(this::convertCodeToRespVO)
            .orElseThrow(() -> new ServiceException(PICKUP_CODE_NOT_EXISTS));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean redeemCode(PickupCodeRedeemReqVO redeemReqVO) {
        PickupCodeDO code = Optional.ofNullable(pickupCodeMapper.selectByCode(redeemReqVO.getCode()))
            .orElseThrow(() -> new ServiceException(PICKUP_CODE_NOT_EXISTS));

        // Validate code status using predicates
        validatePickupCode(code);

        String userId = Optional.ofNullable(redeemReqVO.getUserId())
            .orElseGet(() -> String.valueOf(getLoginUserId()));

        //todo @zhexu.ma
//        int updated = pickupCodeMapper.updateRedemptionStatus(code.getId(), 1, userId);
//        if (updated == 0) {
//            throw new ServiceException(PICKUP_CODE_ALREADY_REDEEMED);
//        }

        // todo @zhexu.ma
        // pickupCodeBatchMapper.incrementRedeemedQuantity(code.getBatchId(), 1);
        log.info("[redeemCode][提货码({})被用户({})核销]", redeemReqVO.getCode(), userId);
        return true;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean outboundCode(PickupCodeOutboundReqVO outboundReqVO) {
        PickupCodeDO code = pickupCodeMapper.selectByCode(outboundReqVO.getCode());
        if (code == null) {
            throw new ServiceException(PICKUP_CODE_NOT_EXISTS);
        }

        if (code.getCodeStatus() != 1) {
            throw new ServiceException(PICKUP_CODE_INVALID);
        }

        if (code.getOutboundStatus() == 1) {
            throw new ServiceException(PICKUP_CODE_ALREADY_OUTBOUND);
        }

        if (code.getExpireTime() != null && code.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException(PICKUP_CODE_EXPIRED);
        }

        int updated = pickupCodeMapper.updateOutboundStatus(code.getId(), 1, outboundReqVO.getOutboundOrderNumber());
        if (updated == 0) {
            throw new ServiceException(PICKUP_CODE_ALREADY_OUTBOUND);
        }

        pickupCodeBatchMapper.incrementOutboundQuantity(code.getBatchId(), 1);
        log.info("[outboundCode][提货码({})已出库，订单号({})]", outboundReqVO.getCode(), outboundReqVO.getOutboundOrderNumber());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void voidCode(Long id) {
        PickupCodeDO code = Optional.ofNullable(pickupCodeMapper.selectById(id))
            .filter(IS_NOT_REDEEMED)
            .orElseThrow(() -> {
                PickupCodeDO existingCode = pickupCodeMapper.selectById(id);
                return existingCode == null ?
                    new ServiceException(PICKUP_CODE_NOT_EXISTS) :
                    new ServiceException(PICKUP_CODE_ALREADY_REDEEMED);
            });

        pickupCodeMapper.updateById(code.setCodeStatus(2));
        pickupCodeBatchMapper.incrementVoidedQuantity(code.getBatchId(), 1);
        log.info("[voidCode][提货码({})被作废]", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCode(Long id) {
        PickupCodeDO code = Optional.ofNullable(pickupCodeMapper.selectById(id))
            .filter(IS_NOT_REDEEMED)
            .orElseThrow(() -> {
                PickupCodeDO existingCode = pickupCodeMapper.selectById(id);
                return existingCode == null ?
                    new ServiceException(PICKUP_CODE_NOT_EXISTS) :
                    new ServiceException(PICKUP_CODE_ALREADY_REDEEMED);
            });

        // 检查是否已出库，已出库的提货码不能删除
        if (code.getOutboundStatus() == 1) {
            throw new ServiceException(PICKUP_CODE_ALREADY_OUTBOUND);
        }

        // 删除提货码
        pickupCodeMapper.deleteById(id);

        // Update batch statistics if code was in normal status
        Optional.of(code)
            .filter(IS_VALID_STATUS)
            .ifPresent(c -> pickupCodeBatchMapper.decrementTotalQuantity(c.getBatchId(), 1));

        log.info("[deleteCode][提货码({})被删除]", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCode(PickupCodeUpdateReqVO updateReqVO) {
        // 校验提货码存在
        PickupCodeDO code = pickupCodeMapper.selectById(updateReqVO.getId());
        if (code == null) {
            throw new ServiceException(PICKUP_CODE_NOT_EXISTS);
        }

        // 检查是否已出库，已出库的提货码不能更新
        if (code.getOutboundStatus() == 1) {
            throw new ServiceException(PICKUP_CODE_ALREADY_OUTBOUND);
        }

        // 检查是否已作废，已作废的提货码不能更新
        if (code.getCodeStatus() == 2) {
            throw new ServiceException(PICKUP_CODE_INVALID);
        }

        // 更新提货码信息
        PickupCodeDO updateCode = new PickupCodeDO();
        updateCode.setId(updateReqVO.getId());
        updateCode.setCode(updateReqVO.getCode());
        updateCode.setVoucherTemplateId(updateReqVO.getVoucherTemplateId());
        updateCode.setBatchId(updateReqVO.getBatchId());
        updateCode.setBatchNumber(updateReqVO.getBatchNumber());
        updateCode.setExpireTime(updateReqVO.getExpireTime());
        updateCode.setPickupRule(updateReqVO.getPickupRule());
        updateCode.setOutboundStatus(updateReqVO.getOutboundStatus());
        updateCode.setOutboundOrderNumber(updateReqVO.getOutboundOrderNumber());
        updateCode.setOutboundTime(updateReqVO.getOutboundTime());
        updateCode.setCodeStatus(updateReqVO.getCodeStatus());
        updateCode.setRemark(updateReqVO.getRemark());

        pickupCodeMapper.updateById(updateCode);
        log.info("[updateCode][提货码({})被更新]", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PickupCodeBatchOutboundRespVO batchOutbound(PickupCodeBatchOutboundReqVO reqVO) {
        // 1. 校验批次存在
        PickupCodeBatchDO batch = pickupCodeBatchMapper.selectById(reqVO.getBatchId());
        if (batch == null) {
            throw new ServiceException(PICKUP_CODE_BATCH_NOT_EXISTS);
        }

        // 2. 查询批次下所有未出库的有效提货码
        List<PickupCodeDO> availableCodes = pickupCodeMapper.selectList(
            new LambdaQueryWrapperX<PickupCodeDO>()
                .eq(PickupCodeDO::getBatchId, reqVO.getBatchId())
                .eq(PickupCodeDO::getCodeStatus, 1) // 正常状态
                .eq(PickupCodeDO::getOutboundStatus, 0) // 未出库
        );

        if (availableCodes.isEmpty()) {
            return PickupCodeBatchOutboundRespVO.builder()
                .successCount(0)
                .failCount(0)
                .totalCount(0)
                .orderNumbers(new ArrayList<>())
                .message("批次中没有可出库的提货码")
                .build();
        }

        // 3. 如果需要关联商户，检查余额并扣款
        MerchantDO merchant = null;
        if (reqVO.getMerchantId() != null) {
            merchant = merchantService.getMerchant(reqVO.getMerchantId());
            if (merchant == null) {
                throw new ServiceException(MERCHANT_NOT_EXISTS);
            }

            // 计算总金额（这里假设每个提货码的价值为批次中设定的价值，实际可能需要从模板获取）
            BigDecimal totalAmount = calculateBatchAmount(batch, availableCodes.size());
            BigDecimal availableBalance = merchant.getBalance().add(merchant.getCreditLimit());

            if (availableBalance.compareTo(totalAmount) < 0) {
                throw new ServiceException(MERCHANT_BALANCE_NOT_ENOUGH);
            }

            // 执行扣款
            RechargeReqVO rechargeReqVO = new RechargeReqVO();
            rechargeReqVO.setMerchantId(reqVO.getMerchantId());
            rechargeReqVO.setRechargeType(RechargeType.PICKUP_CODE_OUTBOUND);
            rechargeReqVO.setAmountOfMoney(totalAmount.negate());
            rechargeReqVO.setRemark("提货码批量出库扣款 - 批次ID: " + reqVO.getBatchId());
            merchantService.recharge(rechargeReqVO);
        }

        // 4. 批量出库处理
        List<String> orderNumbers = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        LocalDateTime outboundTime = LocalDateTime.now();

        for (PickupCodeDO code : availableCodes) {
            try {
                //todo: need impl this logic
                String orderNumber = Utils.getRandomOrderNo();

                // 根据参数决定是否更新过期时间
                LocalDateTime newExpireTime = null;
                if (Boolean.TRUE.equals(reqVO.getUpdateExpireTime())) {
                    VoucherTemplateDO template = voucherTemplateMapper.selectById(code.getVoucherTemplateId());
                    if (template != null && template.getRule() != null && template.getRule().getValidDays() != null) {
                        newExpireTime = outboundTime.plusDays(template.getRule().getValidDays());
                    }
                }

                // 更新提货码状态
                int updated = pickupCodeMapper.updateOutboundStatus(
                    code.getId(), 1, orderNumber, outboundTime, newExpireTime, reqVO.getRemark());

                if (updated > 0) {
                    orderNumbers.add(orderNumber);
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("[batchOutbound][提货码({})出库失败]", code.getId(), e);
                failCount++;
            }
        }

        // 5. 更新批次出库数量
        if (successCount > 0) {
            pickupCodeBatchMapper.incrementOutboundQuantity(reqVO.getBatchId(), successCount);
        }

        log.info("[batchOutbound][批次({})批量出库完成，成功{}个，失败{}个]",
            reqVO.getBatchId(), successCount, failCount);

        return PickupCodeBatchOutboundRespVO.builder()
            .successCount(successCount)
            .failCount(failCount)
            .totalCount(availableCodes.size())
            .orderNumbers(orderNumbers)
            .message(String.format("批量出库完成，成功%d个，失败%d个", successCount, failCount))
            .build();
    }

    private BigDecimal calculateBatchAmount(PickupCodeBatchDO batch, int codeCount) {
        // 根据提货券模板获取单价
        VoucherTemplateDO template = voucherTemplateMapper.selectById(batch.getVoucherTemplateId());
        if (template == null) {
            throw new ServiceException(VOUCHER_TEMPLATE_NOT_EXISTS);
        }

        // 使用模板的市场价作为单价，为空时按0处理
        BigDecimal unitAmount = template.getMarketPrice();
        if (unitAmount == null) {
            unitAmount = BigDecimal.ZERO;
        }

        // 计算总金额 = 单价 × 数量
        return unitAmount.multiply(BigDecimal.valueOf(codeCount));
    }

    private PickupCodeRespVO convertCodeToRespVO(PickupCodeDO code) {
        PickupCodeRespVO respVO = VoucherPickupCodeConvert.INSTANCE.doToRespVO(code);
        VoucherTemplateDO template = voucherTemplateMapper.selectById(code.getVoucherTemplateId());
        if (template != null) {
            respVO.setVoucherTemplateName(template.getName());
        }

        // Enrich with template name
        Optional.ofNullable(code.getVoucherTemplateId())
            .map(voucherTemplateMapper::selectById)
            .map(VoucherTemplateDO::getName)
            .ifPresent(respVO::setVoucherTemplateName);

        // Set validity status
        boolean valid = IS_VALID_STATUS.test(code) && IS_NOT_REDEEMED.test(code) && IS_NOT_EXPIRED.test(code);
        respVO.setValid(valid);

        return respVO;
    }

    @Override
    public AppPickupCodeDetailRespVO getPickupCodeDetail(String code) {
        // Query pickup code with Optional
        PickupCodeDO pickupCode = Optional.ofNullable(pickupCodeMapper.selectOne(new LambdaQueryWrapperX<PickupCodeDO>().eq(PickupCodeDO::getCode, code)))
            .orElseThrow(() -> new ServiceException(PICKUP_CODE_NOT_EXISTS));

        // Query template
        VoucherTemplateDO template = Optional.ofNullable(pickupCode.getVoucherTemplateId())
            .map(voucherTemplateMapper::selectById)
            .orElseThrow(() -> new ServiceException(VOUCHER_TEMPLATE_NOT_EXISTS));

        // Convert using MapStruct
        return VoucherPickupCodeConvert.INSTANCE.toDetailRespVO(pickupCode, template);
    }

    @Override
    public AppPickupCodeProductsRespVO getRedeemableProducts(String code) {
        // Query and validate pickup code
        PickupCodeDO pickupCode = Optional.ofNullable(pickupCodeMapper.selectOne(new LambdaQueryWrapperX<PickupCodeDO>().eq(PickupCodeDO::getCode, code)))
            .orElseThrow(() -> new ServiceException(PICKUP_CODE_NOT_EXISTS));

        // Validate code status
        validatePickupCode(pickupCode);

        // Query template
        VoucherTemplateDO template = Optional.ofNullable(pickupCode.getVoucherTemplateId())
            .map(voucherTemplateMapper::selectById)
            .orElseThrow(() -> new ServiceException(VOUCHER_TEMPLATE_NOT_EXISTS));

        // Build response using converter
        AppPickupCodeProductsRespVO respVO = VoucherPickupCodeConvert.INSTANCE.toProductsRespVO(pickupCode, template);

        // Query and convert products
        List<AppPickupCodeProductsRespVO.RedeemableProduct> products = queryRedeemableProducts(template.getId());
        respVO.setProducts(products);

        return respVO;
    }

    private List<AppPickupCodeProductsRespVO.RedeemableProduct> queryRedeemableProducts(Long templateId) {
        // Query template products
        List<VoucherTemplatePlatformProductDO> templateProducts = voucherTemplatePlatformProductMapper
            .selectList(new LambdaQueryWrapperX<VoucherTemplatePlatformProductDO>()
                .eq(VoucherTemplatePlatformProductDO::getVoucherTemplateId, templateId));

        if (templateProducts == null || templateProducts.isEmpty()) {
            return new ArrayList<>();
        }

        // Extract product IDs
        List<Long> productIds = templateProducts.stream()
            .map(VoucherTemplatePlatformProductDO::getPlatformProductId)
            .collect(Collectors.toList());

        // Query and convert products using Stream API
        return platformProductMapper.selectBatchIds(productIds).stream()
            .map(VoucherPickupCodeConvert.INSTANCE::toRedeemableProduct)
            .collect(Collectors.toList());
    }

    /**
     * Validate pickup code using predicates
     */
    private void validatePickupCode(PickupCodeDO code) {
        if (!IS_VALID_STATUS.test(code)) {
            throw new ServiceException(PICKUP_CODE_INVALID);
        }
        if (!IS_NOT_REDEEMED.test(code)) {
            throw new ServiceException(PICKUP_CODE_ALREADY_REDEEMED);
        }
        if (!IS_NOT_EXPIRED.test(code)) {
            throw new ServiceException(PICKUP_CODE_EXPIRED);
        }
    }
}