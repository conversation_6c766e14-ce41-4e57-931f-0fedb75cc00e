package cn.iocoder.yudao.module.product.utils;

import cn.hutool.core.util.RandomUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 提货码生成器
 */
public class PickupCodeGenerator {

    /**
     * 日期格式
     */
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 提货码可用字符（排除易混淆字符 0/O, 1/I/L）
     */
    private static final String CODE_CHARS = "23456789ABCDEFGHJKMNPQRSTUVWXYZ";

    /**
     * 36进制字符集（0-9, A-Z）
     */
    private static final String BASE36_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /**
     * 提货码长度
     */
    private static final int CODE_LENGTH = 8;

    /**
     * 生成批次号
     * 格式：BATCH-YYYYMMDDHHmmss-XXXX
     *
     * @return 批次号
     */
    public static String generateBatchNumber() {
        String datetime = LocalDateTime.now().format(DATETIME_FORMATTER);
        String random = RandomUtil.randomString(CODE_CHARS, 4);
        return String.format("%s-%s", datetime, random);
    }

    /**
     * 生成单个提货码
     * 格式：6位36进制字符
     *
     * @return 提货码
     */
    public static String generateCode() {
        // 生成一个随机的长整数
        long randomValue = ThreadLocalRandom.current().nextLong(0, (long) Math.pow(36, CODE_LENGTH));

        // 转换为36进制字符串
        StringBuilder code = new StringBuilder(Long.toString(randomValue, 36).toUpperCase());

        // 补齐到指定长度（左侧补0）
        while (code.length() < CODE_LENGTH) {
            code.insert(0, "0");
        }

        return code.toString();
    }

    /**
     * 批量生成不重复的提货码
     *
     * @param count 生成数量
     * @return 提货码集合
     */
    public static Set<String> generateCodes(int count) {
        Set<String> codes = new HashSet<>(count);

        // 生成指定数量的不重复提货码
        while (codes.size() < count) {
            String code = generateCode();
            codes.add(code);
        }

        return codes;
    }

    /**
     * 验证提货码格式
     *
     * @param code 提货码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        if (code == null || code.length() != CODE_LENGTH) {
            return false;
        }

        // 检查是否只包含36进制字符（0-9, A-Z）
        for (char c : code.toCharArray()) {
            if (BASE36_CHARS.indexOf(c) == -1) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证批次号格式
     *
     * @param batchNumber 批次号
     * @return 是否有效
     */
    public static boolean isValidBatchNumber(String batchNumber) {
        if (batchNumber == null || batchNumber.length() != 25) {
            return false;
        }

        // 检查格式：YYYYMMDDHHmmss-XXXX
        String[] parts = batchNumber.split("-");
        if (parts.length != 2) {
            return false;
        }

        // 检查日期时间部分（14位数字）
        if (parts[0].length() != 14 || !parts[0].matches("\\d{14}")) {
            return false;
        }

        // 检查随机码部分（4位）
        if (parts[1].length() != 4) {
            return false;
        }

        return true;
    }
}