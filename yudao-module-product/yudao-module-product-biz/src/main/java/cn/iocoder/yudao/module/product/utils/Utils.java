package cn.iocoder.yudao.module.product.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.security.MessageDigest;
import java.util.*;

@Slf4j
public class Utils {
    /**
     * 获取  订单号
     */
    public static String getRandomOrderNo() {
        Random random = new Random();
        long timestamp = System.currentTimeMillis();
        String randomNumber = String.format("%04d", random.nextInt(9999));

        return "N" + timestamp + randomNumber;
    }

    /**
     * 获取  批次号
     */
    public static String getRandomBatchNo() {
        Random random = new Random();
        long timestamp = System.currentTimeMillis();
        String randomNumber = String.format("%04d", random.nextInt(9999));

        return "B" + timestamp + randomNumber;
    }

    public static String MD5(String s) {
        char[] hexDigits = {
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            byte[] strTemp = s.getBytes();
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(strTemp);
            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * map 转 字符串
     *
     * @param params
     * @return
     */
    public static String generateSortedQueryString(Map<String, ?> params, String key) {
        // 对参数按参数名的ASCII码字典序升序排列
        List<String> paramNames = new ArrayList<>(params.keySet());
        Collections.sort(paramNames);

        // 构建QueryString格式的参数字符串
        StringBuilder sb = new StringBuilder();
        for (String paramName : paramNames) {
            if (!sb.isEmpty()) {
                sb.append("&");
            }
            sb.append(paramName).append("=").append(params.get(paramName));
        }
        if (StringUtils.isNotEmpty(key)) {
            sb.append("&signKey=").append(key);
        }
        return sb.toString();
    }

    /**
     * 实体类转MAP
     *
     * @param entity
     * @return
     */
    public static Map<String, String> convertEntityToMap(Object entity) {
        Map<String, String> map = new HashMap<>();

        // 获取实体类的所有字段
        Field[] fields = entity.getClass().getDeclaredFields();

        // 遍历字段并将字段名和值放入Map中
        for (Field field : fields) {
            try {
                field.setAccessible(true); // 设置字段可访问
                Object value = field.get(entity); // 获取字段的值
                // 值为空  签名字段不参与
                if (value != null && StringUtils.isNotEmpty(String.valueOf(value)) && !field.getName().equals("signKey")) {
                    map.put(field.getName(), String.valueOf(value));
                }
            } catch (IllegalAccessException e) {
                log.error("转换entity to map 失败， {}", entity);
                e.printStackTrace();
            }
        }

        return map;
    }


}
