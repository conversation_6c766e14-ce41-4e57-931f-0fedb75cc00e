package cn.iocoder.yudao.module.product.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.product.controller.admin.vo.MerchantProductListReqVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.MerchantProductResVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.MerchantProductSaveVO;
import cn.iocoder.yudao.module.product.service.MerchantProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "商户商品")
@Validated
@RestController
@RequestMapping("/merchant-product")
@RequiredArgsConstructor
public class MerchantProductController {

    private final MerchantProductService merchantProductService;

    @GetMapping("/list")
    @Operation(summary = "获取商户商品列表")
    @PreAuthorize("@ss.hasPermission('merchant:product:query')")
    public CommonResult<PageResult<MerchantProductResVO>> getMerchantProductList(MerchantProductListReqVO reqVO) {
        var result = merchantProductService.getMerchantProductList(reqVO);
        return success(result);
    }

    @PostMapping("/bind")
    @Operation(summary = "获取商户商品列表")
    @PreAuthorize("@ss.hasPermission('merchant:product:bind')")
    public CommonResult<Boolean> bindMerchantProducts(@RequestBody @Valid List<MerchantProductSaveVO> reqVOList) {
        merchantProductService.bindMerchantProducts(reqVOList);
        return success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "更新商户商品")
    @PreAuthorize("@ss.hasPermission('merchant:product:update')")
    public CommonResult<Boolean> updateMerchantProducts(@RequestBody @Valid MerchantProductSaveVO saveVO) {
        merchantProductService.updateMerchantProduct(saveVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商户商品")
    @PreAuthorize("@ss.hasPermission('merchant:product:delete')")
    public CommonResult<Boolean> deleteMerchantProducts(Long id) {
        merchantProductService.deleteMerchantProduct(id);
        return success(true);
    }

}
