package cn.iocoder.yudao.module.product.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.product.controller.admin.vo.ProductCategoryCreateResVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.ProductCategoryMoveReqVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.ProductCategoryReqVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.ProductCategoryResVO;
import cn.iocoder.yudao.module.product.service.ProductCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "商品管理 - 商品类目")
@Validated
@RestController
@RequestMapping("/product-category")
@RequiredArgsConstructor
public class ProductCategoryController {

    private final ProductCategoryService productCategoryService;

    @GetMapping("/all")
    @Operation(summary = "获取所有商品类目")
    @PreAuthorize("@ss.hasPermission('product:category:query')")
    public CommonResult<List<ProductCategoryResVO>> getAllProductCategories() {
        return CommonResult.success(productCategoryService.getAllProductCategories());
    }

    @PostMapping("/create")
    @Operation(summary = "创建商品类目")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<ProductCategoryCreateResVO> createCategory(@RequestBody ProductCategoryReqVO categoryReqVO) {
        return CommonResult.success(productCategoryService.createCategory(categoryReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新商品类目")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<Boolean> updateCategory(@RequestBody ProductCategoryReqVO categoryReqVO) {
        productCategoryService.update(categoryReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/move")
    @Operation(summary = "移动类目")
    @PreAuthorize("@ss.hasPermission('product:category:update')")
    public CommonResult<Boolean> moveCategory(@RequestBody ProductCategoryMoveReqVO moveReqVO) {
        productCategoryService.moveCategory(moveReqVO);
        return CommonResult.success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品类目")
    @PreAuthorize("@ss.hasPermission('product:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") Long id) {
        productCategoryService.deleteCategory(id);
        return CommonResult.success(true);
    }

    @DeleteMapping("/batch-delete")
    @Operation(summary = "批量删除商品类目")
    @PreAuthorize("@ss.hasPermission('product:category:delete')")
    public CommonResult<Boolean> batchDeleteCategory(@RequestBody List<Long> ids) {
        productCategoryService.batchDeleteCategory(ids);
        return CommonResult.success(true);
    }
}
