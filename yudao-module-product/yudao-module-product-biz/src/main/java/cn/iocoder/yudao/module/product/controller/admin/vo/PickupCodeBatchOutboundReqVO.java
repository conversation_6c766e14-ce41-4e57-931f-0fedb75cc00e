package cn.iocoder.yudao.module.product.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 提货码批量出库请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 提货码批量出库 Request VO")
@Data
public class PickupCodeBatchOutboundReqVO {

    @Schema(description = "批次ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull
    private Long batchId;

    @Schema(description = "关联商户ID（可选）", example = "100")
    private Long merchantId;

    @Schema(description = "备注", example = "批量出库备注")
    private String remark;

    @Schema(description = "是否更新过期时间", example = "false")
    private Boolean updateExpireTime = false;
}
