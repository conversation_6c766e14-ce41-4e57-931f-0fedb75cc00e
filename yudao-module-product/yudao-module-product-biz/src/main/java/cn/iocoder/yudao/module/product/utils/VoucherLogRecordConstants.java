package cn.iocoder.yudao.module.product.utils;

public interface VoucherLogRecordConstants {

    String VOUCHER_PICKUP_CODE_BATCH = "VOUCHER_PICKUP_CODE_BATCH 提货码批次";

    String VOUCHER_PICKUP_CODE_BATCH_CREATE = "创建提货码批次";
    String VOUCHER_PICKUP_CODE_BATCH_DATA_CREATE_SUCCESS = "创建了提货码批次【{{#_ret.data.batchNumber}}】";

    String VOUCHER_PICKUP_CODE_BATCH_UPDATE = "更新提货码批次";
    String VOUCHER_PICKUP_CODE_BATCH_DATA_UPDATE_SUCCESS = "更新了提货码批次【{{#_ret.data.batchNumber}}】: {_DIFF{#updateReqVO}}";

    String VOUCHER_PICKUP_CODE_BATCH_DELETE = "删除提货码批次";
    String VOUCHER_PICKUP_CODE_BATCH_DATA_DELETE_SUCCESS = "删除了提货码批次【{{#id}}】";

    String VOUCHER_PICKUP_CODE_BATCH_VOID = "作废提货码批次";
    String VOUCHER_PICKUP_CODE_BATCH_DATA_VOID_SUCCESS = "作废了提货码批次【{{#id}}】下的提货码";

    // 提货码相关常量
    String VOUCHER_PICKUP_CODE = "VOUCHER_PICKUP_CODE 提货码";


    String VOUCHER_PICKUP_CODE_VALIDATE = "验证提货码";
    String VOUCHER_PICKUP_CODE_DATA_VALIDATE_SUCCESS = "验证了提货码【{{#validateReqVO.code}}】";

    String VOUCHER_PICKUP_CODE_OUTBOUND = "出库提货码";
    String VOUCHER_PICKUP_CODE_DATA_OUTBOUND_SUCCESS = "出库了提货码【{{#outboundReqVO.code}}】，订单号【{{#outboundReqVO.outboundOrderNumber}}】";

    String VOUCHER_PICKUP_CODE_VOID = "作废提货码";
    String VOUCHER_PICKUP_CODE_DATA_VOID_SUCCESS = "作废了提货码【{{#id}}】";

    String VOUCHER_PICKUP_CODE_DELETE = "删除提货码";
    String VOUCHER_PICKUP_CODE_DATA_DELETE_SUCCESS = "删除了提货码【{{#id}}】";

    String VOUCHER_PICKUP_CODE_UPDATE = "更新提货码";
    String VOUCHER_PICKUP_CODE_DATA_UPDATE_SUCCESS = "更新了提货码【{{#updateReqVO.id}}】: {_DIFF{#updateReqVO}}";

}
