package cn.iocoder.yudao.module.product.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@TableName(value = "voucher_template", autoResultMap = true)
public class VoucherTemplateDO extends BaseDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String name; // 卡券名称

    private String description; // 卡券详情

    private String mainImage; // 产品主图 URL

    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<String> carouselImages; // 轮播展示图片 URL列表

    private BigDecimal marketPrice; // 卡券市场价

    private Integer withdrawType; // 提货方式：1=按金额，2=按次数

    /**
     * 发票类型
     * 1 - 普票
     * 2 - 专票
     */
    private Integer fapiaoType;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 提货规则，使用JSON格式存储
     */
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private WithdrawRuleDO rule;

    private Integer status; // 状态：1=上架，2=下架
}