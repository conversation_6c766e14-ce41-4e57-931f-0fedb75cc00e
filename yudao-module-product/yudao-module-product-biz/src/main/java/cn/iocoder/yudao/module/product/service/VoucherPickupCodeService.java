package cn.iocoder.yudao.module.product.service;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.product.controller.admin.vo.*;
import cn.iocoder.yudao.module.product.controller.app.pickup.AppPickupCodeDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.pickup.AppPickupCodeProductsRespVO;

/**
 * 提货码 Service 接口
 */
public interface VoucherPickupCodeService {

    /**
     * 获得提货码分页
     *
     * @param pageReqVO 分页查询
     * @return 提货码分页
     */
    PageResult<PickupCodeRespVO> getCodePage(PickupCodePageReqVO pageReqVO);

    /**
     * 获得提货码详情
     *
     * @param id 提货码ID
     * @return 提货码详情
     */
    PickupCodeRespVO getCode(Long id);

    /**
     * 验证提货码
     *
     * @param validateReqVO 验证信息
     * @return 提货码信息
     */
    PickupCodeRespVO validateCode(PickupCodeValidateReqVO validateReqVO);

    /**
     * 核销提货码
     *
     * @param redeemReqVO 批量出库请求
     * @return 批量出库结果
     */
    Boolean redeemCode(PickupCodeRedeemReqVO redeemReqVO);

    /**
     * 作废提货码
     *
     * @param id 提货码ID
     */
    void voidCode(Long id);

    void deleteCode(Long id);

    /**
     * 更新提货码
     *
     * @param updateReqVO 更新信息
     */
    void updateCode(PickupCodeUpdateReqVO updateReqVO);

    /**
     * 根据提货码查询详情（H5端）
     *
     * @param code 提货码
     * @return 提货码详情
     */
    AppPickupCodeDetailRespVO getPickupCodeDetail(String code);

    /**
     * 获取可兑换商品列表（H5端）
     *
     * @param code 提货码
     * @return 可兑换商品列表
     */
    AppPickupCodeProductsRespVO getRedeemableProducts(String code);

    /**
     * 出库提货码
     * @param outboundReqVO 出库请求
     * @return 是否成功
     */
    Boolean outboundCode(PickupCodeOutboundReqVO outboundReqVO);

    PickupCodeBatchOutboundRespVO batchOutbound(PickupCodeBatchOutboundReqVO reqVO);
}
