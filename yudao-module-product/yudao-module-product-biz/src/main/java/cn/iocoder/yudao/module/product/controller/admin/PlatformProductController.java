package cn.iocoder.yudao.module.product.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantDO;
import cn.iocoder.yudao.module.merchant.service.MerchantService;
import cn.iocoder.yudao.module.product.controller.admin.vo.PlatformProductBatchSaveVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.PlatformProductListReqVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.PlatformProductResVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.PlatformProductSaveVO;
import cn.iocoder.yudao.module.product.service.PlatformProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_NOT_EXISTS;

@Tag(name = "商品管理 - 平台商品")
@Validated
@RestController
@RequestMapping("/platform-product")
@RequiredArgsConstructor
public class PlatformProductController {

    private final PlatformProductService platformProductService;
    private final MerchantService merchantService;

    @PostMapping("/create")
    @Operation(summary = "创建平台商品")
    @PreAuthorize("@ss.hasPermission('platform-product:data:update')")
    public CommonResult<Long> savePlatformProduct(@Valid @RequestBody PlatformProductSaveVO platformProductSaveVO) {
        var id = platformProductService.createPlatformProduct(platformProductSaveVO);
        return CommonResult.success(id);
    }

    @PostMapping("/batch-create")
    @Operation(summary = "批量创建平台商品")
    @PreAuthorize("@ss.hasPermission('platform-product:data:update')")
    public CommonResult<List<Long>> batchSavePlatformProduct(@Valid @RequestBody PlatformProductBatchSaveVO batchSaveVO) {
        var ids = platformProductService.batchCreatePlatformProduct(batchSaveVO.getProducts());
        return CommonResult.success(ids);
    }

    @PostMapping("/update")
    @Operation(summary = "修改平台商品")
    @PreAuthorize("@ss.hasPermission('platform-product:data:update')")
    public CommonResult<Long> updatePlatformProduct(@Valid @RequestBody PlatformProductSaveVO platformProductSaveVO) {
        var id = platformProductService.updatePlatformProduct(platformProductSaveVO);
        return CommonResult.success(id);
    }

    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('platform-product:data:query')")
    @Operation(summary = "获取平台商品列表", description = "用于【商品管理】平台商品列表界面")
    public CommonResult<PageResult<PlatformProductResVO>> getPlatformProductList(PlatformProductListReqVO req) {
        var result = platformProductService.getPlatformProductList(req);
        return CommonResult.success(result);
    }

    @GetMapping("/list-all")
    @PreAuthorize("@ss.hasPermission('platform-product:data:query')")
    @Operation(summary = "获取平台商品列表", description = "用于【商品管理】平台商品列表界面")
    public CommonResult<List<PlatformProductResVO>> getAllPlatformProductList() {
        var result = platformProductService.getAllPlatformProduct();
        return CommonResult.success(BeanUtils.toBean(result, PlatformProductResVO.class));
    }

    @GetMapping("/available/list")
    @PreAuthorize("@ss.hasPermission('merchant:product:bind')")
    @Operation(summary = "获取平台商品列表", description = "用于【商品管理】平台商品列表界面")
    public CommonResult<PageResult<PlatformProductResVO>> getAvailablePlatformProductList(PlatformProductListReqVO req) {
        MerchantDO merchant = merchantService.getMerchant(req.getMerchantId());
        if (merchant == null) {
            throw exception(MERCHANT_NOT_EXISTS);
        }

        req.setExcludeIdList(merchant.getProductIdList());

        var result = platformProductService.getPlatformProductList(req);
        return CommonResult.success(result);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('platform-product:data:query')")
    @Operation(summary = "根据ID获取平台商品详情")
    public CommonResult<PlatformProductResVO> getPlatformProduct(Long id) {
        var platformProductResVO = platformProductService.getPlatformProduct(id);
        return CommonResult.success(platformProductResVO);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除平台商品")
    @PreAuthorize("@ss.hasPermission('platform-product:data:delete')")
    public CommonResult<Boolean> deletePlatformProduct(Long id) {
        platformProductService.deletePlatformProduct(id);
        return CommonResult.success(true);
    }
}
