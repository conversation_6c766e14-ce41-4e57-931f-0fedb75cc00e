package cn.iocoder.yudao.module.product.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * product 错误码枚举类
 * <p>
 * product 系统，使用 1-014-000-000 段
 */
public interface ErrorCodeConstants {

    ErrorCode PLATFORM_PRODUCT_NOT_EXISTS = new ErrorCode(1_014_000_000, "当前平台商品信息不存在");
    ErrorCode MERCHANT_PRODUCT_PRICE_LESS_THAN_COST = new ErrorCode(1_014_000_001, "商户商品售价低于成本");
    ErrorCode MERCHANT_PRODUCT_BIND_PRODUCT_NOT_EXIST = new ErrorCode(1_014_000_002, "商户商品对应的平台商品不存在");
    ErrorCode MERCHANT_PRODUCT_COST_NOT_EXIST = new ErrorCode(1_014_000_003, "商户商品没有成本价，不能设置价格");
    ErrorCode PLATFORM_PRODUCT_DIVERSION_WRONG = new ErrorCode(1_014_000_004, "未设置分流规则");
    ErrorCode PLATFORM_PRODUCT_DIVERSION_NO_OTHER = new ErrorCode(1_014_000_005, "分流规则必须有单独的其他选项");
    ErrorCode MERCHANT_PRODUCT_NOT_EXISTS = new ErrorCode(1_014_000_006, "商户商品信息不存在");
    ErrorCode PLATFORM_PRODUCT_PRICE_LESS_THAN_COST = new ErrorCode(1_014_000_007, "平台商品售价低于成本");
    ErrorCode PLATFORM_PRODUCT_PRICE_ERROR = new ErrorCode(1_014_000_008, "平台商品售价为空或等于0");
    ErrorCode MERCHANT_PRODUCT_EDIT_NEGATIVE_PROFIT_NO_PERMISSION = new ErrorCode(1_014_000_009, "没有编辑商户商品负利润的权限，请联系管理员");
    ErrorCode PLATFORM_PRODUCT_EDIT_NEGATIVE_PROFIT_NO_PERMISSION = new ErrorCode(1_014_000_009, "没有编辑平台商品负利润的权限，请联系管理员");
    ErrorCode MERCHANT_PRODUCT_BIND_VOUCHER_TEMPLATE_NOT_EXIST  = new ErrorCode(1_014_000_010, "商户商品对应的提货券模版不存在");


    ErrorCode PRODUCT_CATEGORY_NAME_IS_EMPTY = new ErrorCode(1_014_001_000, "类目名称不能为空");
    ErrorCode PRODUCT_CATEGORY_PARENT_NOT_EXIST = new ErrorCode(1_014_001_001, "父类目不存在");
    ErrorCode PRODUCT_CATEGORY_UPDATE_ID_IS_EMPTY = new ErrorCode(1_014_001_002, "所更新类目的Id不能为空");
    ErrorCode PRODUCT_CATEGORY_NOT_EXIST = new ErrorCode(1_014_001_003, "类目不存在");
    ErrorCode PRODUCT_CATEGORY_HAS_RELATED_PRODUCT = new ErrorCode(1_014_001_004, "当前类目已关联商品，不允许删除");

    // ========== VOUCHER TEMPLATE 模块 1-001-000-000 ==========
    ErrorCode VOUCHER_TEMPLATE_NOT_EXISTS = new ErrorCode(1_001_000_000, "提货券模板不存在");
    ErrorCode VOUCHER_TEMPLATE_PLATFORM_PRODUCT_REQUIRED = new ErrorCode(1_001_000_001, "必须关联平台商品");

    // ========== PICKUP CODE 模块 1-014-100-000 ==========
    ErrorCode PICKUP_CODE_BATCH_NOT_EXISTS = new ErrorCode(1_014_100_000, "提货码批次不存在");
    ErrorCode PICKUP_CODE_BATCH_ALREADY_GENERATED = new ErrorCode(1_014_100_001, "批次提货码已生成");
    ErrorCode PICKUP_CODE_BATCH_INVALID_STATUS = new ErrorCode(1_014_100_002, "批次状态无效");
    ErrorCode PICKUP_CODE_NOT_EXISTS = new ErrorCode(1_014_101_000, "提货码不存在");
    ErrorCode PICKUP_CODE_ALREADY_OUTBOUND = new ErrorCode(1_014_101_001, "提货码已出库");
    ErrorCode PICKUP_CODE_EXPIRED = new ErrorCode(1_014_101_002, "提货码已过期");
    ErrorCode PICKUP_CODE_INVALID = new ErrorCode(1_014_101_003, "提货码无效");
    ErrorCode PICKUP_CODE_ALREADY_REDEEMED = new ErrorCode(1_014_101_004, "提货码已被核销");
    ErrorCode PICKUP_CODE_GENERATION_FAILED = new ErrorCode(1_014_102_000, "提货码生成失败");
    ErrorCode MERCHANT_NOT_EXISTS = new ErrorCode(1_014_103_000, "商户不存在");
    ErrorCode MERCHANT_BALANCE_NOT_ENOUGH = new ErrorCode(1_014_103_001, "商户余额不足");

    // ========== VOUCHER CAPTCHA 模块 1-014-103-000 ==========
    ErrorCode VOUCHER_CAPTCHA_GENERATE_TOO_FAST = new ErrorCode(1_014_103_000, "验证码生成过于频繁，请稍后再试");
    ErrorCode VOUCHER_CAPTCHA_NOT_FOUND = new ErrorCode(1_014_103_001, "验证码不存在或已过期");
    ErrorCode VOUCHER_CAPTCHA_ERROR = new ErrorCode(1_014_103_002, "验证码错误");
    ErrorCode VOUCHER_CAPTCHA_LOCKED = new ErrorCode(1_014_103_003, "验证码错误次数过多，已锁定");
}