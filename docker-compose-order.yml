name: quanyi-order-api

services:
  order-api:
    container_name: order-api
    image: admin-backend
    ports:
      - "38080:38080"
    restart: always
    networks:
      - quanyi_network
    environment:
      TOPIC_ID: ${ORDER_TOPIC_ID:-87a8ef58-c58b-464e-8405-2f64c5e23cce}
      SPRING_PROFILES_ACTIVE: ${ACTIVE_PROFILES:-dev}
      JAVA_OPTS: |
        -Xms2048m
        -Xmx2048m
        -Djava.security.egd=file:/dev/./urandom
      ARGS:
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-quanyi-redis}
        ${REDIS_PASSWORD_ARG}
    logging:
      driver: "json-file"
      options:
        max-size: "2g"
        max-file: "3"

networks:
  quanyi_network:
    name: quanyi_network
    external: true

