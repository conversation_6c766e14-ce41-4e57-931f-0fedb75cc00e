package cn.iocoder.yudao.module.order.controller.app;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.yudao.module.integration.gateway.params.aiyiqi.mongolia.AiqiyiMongoliaApiParams;
import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import cn.iocoder.yudao.module.order.controller.app.vo.BizContentVO;
import cn.iocoder.yudao.module.order.controller.app.vo.OrderCreateReqVO;
import cn.iocoder.yudao.module.order.controller.app.vo.OrderQueryReqVO;
import cn.iocoder.yudao.module.product.controller.admin.vo.MerchantProductQueryReqVO;
import cn.iocoder.yudao.module.product.utils.Utils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.*;

@Disabled
class OrderControllerTest {
    private static final String CREATE_URL = "http://127.0.0.1:38080/app-api/order/create";
    private static final String QUERY_URL = "http://127.0.0.1:38080/app-api/order/get";
    private static final String SIGN_KEY = "a3cab37d-149c-4c40-8a61-4a953e509578";

//    private static String CREATE_URL = "http://*************:38080/app-api/order/create";
//    private static String QUERY_URL = "http://*************:38080/app-api/order/get";
//    private static String SIGN_KEY = "95140938-e56a-4463-b5e9-793d73a78cd5";


//    private static String CREATE_URL = "http://*************:38090/app-api/order/create";
//    private static String QUERY_URL = "http://*************:38090/app-api/order/get";
//    private static String SIGN_KEY = "59812d41-83e2-4503-9845-dae9a5b240ac";


    @Test
    void testCreateOrder() {
        var reqVO = new OrderCreateReqVO();
        var bizContentVO = new BizContentVO();
        reqVO.setMerchantId(10086L);
        bizContentVO.setProductId("10103");
        bizContentVO.setMerchantOrderNo(Utils.getRandomOrderNo());
        bizContentVO.setRechargeAccount("***********");
        bizContentVO.setQuantity(1);
        bizContentVO.setMaxPrice(new BigDecimal("55.00"));
        reqVO.setBizContent(JSON.toJSONString(bizContentVO));
        reqVO.setTimestamp(System.currentTimeMillis());
        JSONObject jsonObject = (JSONObject) JSON.toJSON(reqVO);
        System.out.println(jsonObject.toString());
        String sign = Utils.MD5(Utils.generateSortedQueryString(Utils.convertEntityToMap(reqVO), SIGN_KEY));
        jsonObject.put("signKey", sign);

        HttpResponse response = HttpRequest.post(CREATE_URL)
                .charset("UTF-8")
                .header("content-type", "application/json;charset=UTF-8")
                .body(jsonObject.toString())
                .execute();

        JSONObject resp = JSONObject.parseObject(response.body());
        System.out.println("返回结果");
        System.out.println(resp);
    }

    @Test
    void testQueryOrder() {
        var reqVO = new OrderQueryReqVO();
        reqVO.setMerchantId(10086L);
        reqVO.setMerchantOrderNo("N17486208025091996");
        reqVO.setSignKey(Utils.MD5(SIGN_KEY));
        reqVO.setTimestamp(System.currentTimeMillis());

        JSONObject jsonObject = (JSONObject) JSON.toJSON(reqVO);
        System.out.println(jsonObject.toString());
        String sign = Utils.MD5(Utils.generateSortedQueryString(Utils.convertEntityToMap(reqVO), SIGN_KEY));
        jsonObject.put("signKey", sign);

        HttpResponse response = HttpRequest.post(QUERY_URL)
                .charset("UTF-8")
                .header("content-type", "application/json;charset=UTF-8")
                .body(jsonObject.toString())
                .execute();

        JSONObject resp = JSONObject.parseObject(response.body());
        System.out.println("返回结果");
        System.out.println(resp);
    }

    @Test
    void testQueryProduct() {
        String url = "http://127.0.0.1:38080/app-api/product/list";

        var reqVO = new MerchantProductQueryReqVO();
        reqVO.setMerchantId(10086L);
        reqVO.setTimestamp(System.currentTimeMillis());
        reqVO.setPageSize(1000);
        reqVO.setPageNo(1);

        JSONObject jsonObject = (JSONObject) JSON.toJSON(reqVO);
        String sign = Utils.MD5(Utils.generateSortedQueryString(Utils.convertEntityToMap(reqVO), SIGN_KEY));
        jsonObject.put("signKey", sign);
        System.out.println(jsonObject);

        HttpResponse response = HttpRequest.post(url)
                .charset("UTF-8")
                .header("content-type", "application/json;charset=UTF-8")
                .body(jsonObject.toString())
                .execute();

        JSONObject resp = JSONObject.parseObject(response.body());
        System.out.println("返回结果");
        System.out.println(resp);
    }

    @Test
    void testRechargeCallBack() {
        var url = "http://localhost:48088/api/fulu/order-recharge-result";
//        var url = "http://*************:48088/api/fulu/order-recharge-result";

        JSONObject jsonObject = new JSONObject();
        // order_id 是 fulu 平台生成的订单号
        jsonObject.put("order_id", "25053149535946440031");
        jsonObject.put("charge_finish_time", "2025-05-30 01:18:09");
        // customer_order_no 是上游订单号
        jsonObject.put("customer_order_no", "20250531003122091322");
        jsonObject.put("order_status", "success");
        jsonObject.put("recharge_description", "成功");
        // product_id 供应商商品 id
        jsonObject.put("product_id", "10000502");
        jsonObject.put("price", "30.1");
        jsonObject.put("buy_num", "1");
        jsonObject.put("operator_serial_number", null);

        System.out.println(jsonObject.toJSONString());

        HttpResponse response = HttpRequest.post(url)
                .charset("UTF-8")
                .header("content-type", "application/json;charset=UTF-8")
                .body(jsonObject.toJSONString())
                .execute();

        System.out.println("返回结果");
        System.out.println(response.body());
    }

    @Test
    void testJingYinRechargeCallBack() {
        var url = "http://localhost:38080/app-api/callback/jingyin/order-recharge-result";

        Map<String, String> map = new TreeMap<>();
        map.put("merAccount", "test");
        map.put("businessType", "13");
        map.put("merOrderNo", "20250809192709268190");
        map.put("merOrderTime", "2025-08-09 19:27:10");
        map.put("orderNo", "1320250809200037697233");
        map.put("orderTime", "2025-08-09 19:27:10");
        map.put("endTime", "2025-08-09 19:28:10");
        map.put("orderAmount", "10000");
        map.put("payAmount", "9500");
        map.put("discountAmount", "9000");
        map.put("successAmount", "9000");
        map.put("orderState", "24");
        map.put("orderStateDesc", "充值成功");
        map.put("sign", createJingYinSign(map));

        String postJson = com.alibaba.fastjson2.JSON.toJSONString(map);
        System.out.println(postJson);

        HttpResponse response = HttpRequest.post(url)
                .charset("UTF-8")
                .header("content-type", "application/json;charset=UTF-8")
                .body(postJson)
                .execute();

        System.out.println("返回结果");
        System.out.println(response.body());
    }

    @Test
    void testAiqiyiMongolia() {
        var url = "http://nm-cmcc-ott-bestv-q.sdk.kingnut.cn/syncOrder";
        String phoneNo = "15991948084";
        AiqiyiMongoliaApiParams request = new AiqiyiMongoliaApiParams();
        request.setTransactionID("15991948084");
        request.setSpID("bestv");
        request.setUserID(phoneNo);
        request.setStbID(phoneNo);
        request.setMobileNO(phoneNo);
        request.setOpType(1);
        request.setProductID("00000000000010010000000000000214");
        request.setProductPrice(0);
        request.setContentID("0");
        request.setEffectiveTime("20250526213254");
        request.setExpireTime("20250626213254");
        request.setOrderContinue(1);
        request.setOrderSource(2);
        request.setProductType(2);

        String sign = generateAiqiyiMongoliaQueryString(Utils.convertEntityToMap(request), "test");
        System.out.println(sign);
        String singedKey = Utils.MD5(sign);
        System.out.println("====================================");
        System.out.println(singedKey);

    }

    public static String createJingYinSign(Map<String, String> map) {
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {

            if (entry.getValue() == null) {
                continue;
            }
            builder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        String signData = builder + "key=" + "0cbc6611f5540bd0809a388dc95a615b";
        return MD5Utils.MD5(signData);
    }

    public static String generateAiqiyiMongoliaQueryString(Map<String, ?> params, String key) {
        List<String> paramNames = new ArrayList<>(params.keySet());
        Collections.sort(paramNames);

        // 构建QueryString格式的参数字符串
        StringBuilder sb = new StringBuilder();
        for (String paramName : paramNames) {
            if (!sb.isEmpty()) {
                sb.append("&");
            }
            sb.append(paramName).append("=").append(params.get(paramName));
        }
        // 叠加publicKey
        if (StringUtils.isNotEmpty(key)) {
            sb.append(key);
        }
        return sb.toString();
    }
}
