package cn.iocoder.yudao.module.order.controller.admin.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "订单 - 查询条件 VO")
@Data
public class OrderListReqVO extends PageParam {

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "创建时间范围")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "完成时间范围")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] completeTime;

    @Schema(description = "充值帐号")
    private String rechargeAccount;

    @Schema(description = "商品类型")
    private Integer productType;

    @Schema(description = "平台商品ID")
    private Long platformProductId;

    @Schema(description = "平台商品名称")
    private String platformProductName;

    @Schema(description = "商户号(商户ID)")
    private Long merchantId;

    @Schema(description = "商户名称")
    private String merchantName;

    @Schema(description = "批次号")
    private String batchNo;
}