package cn.iocoder.yudao.module.order.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderDetailResVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderListReqVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderListResVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderNoListReqVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderOperationLogResVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderRefundReqVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.UpstreamOrderReqVO;
import cn.iocoder.yudao.module.order.controller.admin.vo.OrderExportVO;
import cn.iocoder.yudao.module.order.convert.OrderConvert;
import cn.iocoder.yudao.module.order.service.AdminOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "订单管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("order/system-order")
public class AdminOrderController {

    private final AdminOrderService adminOrderService;

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('order:system-order:query')")
    @Operation(summary = "获取订单列表", description = "用于【订单管理】界面")
    public CommonResult<PageResult<OrderListResVO>> getOrderList(@Validated OrderListReqVO reqVO) {
        PageResult<OrderListResVO> pageResult = adminOrderService.getOrderList(reqVO);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPermission('order:system-order:query')")
    @Operation(summary = "获取订单详情", description = "用于【订单管理】界面")
    public CommonResult<OrderDetailResVO> getOrderDetail(@PathVariable("id") Long id) {
        return CommonResult.success(adminOrderService.getOrderDetail(id));
    }

    @GetMapping("/{id}/operation-logs")
    @PreAuthorize("@ss.hasPermission('order:system-order:query')")
    @Operation(summary = "获取订单操作日志", description = "用于【订单管理】界面")
    public CommonResult<List<OrderOperationLogResVO>> getOrderOperationLogs(@PathVariable("id") Long id) {
        return CommonResult.success(adminOrderService.getOrderOperationLogs(id));
    }

    @PostMapping("/refund")
    @PreAuthorize("@ss.hasPermission('order:system-order:refund')")
    @Operation(summary = "退款订单", description = "用于【订单管理】界面")
    public CommonResult<Boolean> refundOrder(@Validated @RequestBody OrderRefundReqVO refundReqVO) {
        adminOrderService.refundOrder(refundReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/manual-query")
    @PreAuthorize("@ss.hasPermission('order:system-order:manual-query')")
    @Operation(summary = "手动触发未完成订单查询", description = "用于【订单管理】界面")
    public CommonResult<Boolean> manualQuery(@RequestBody OrderNoListReqVO reqVO) {
        adminOrderService.manualQueryOrders(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/force-success")
    @PreAuthorize("@ss.hasPermission('order:system-order:force-success')")
    @Operation(summary = "强制订单成功", description = "用于【订单管理】列表界面")
    public CommonResult<Boolean> forceSuccess(@RequestBody OrderNoListReqVO reqVO) {
        adminOrderService.forceSuccess(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/force-success/detail")
    @PreAuthorize("@ss.hasPermission('order:system-order:force-success')")
    @Operation(summary = "强制订单成功", description = "用于【订单管理】详情界面")
    public CommonResult<Boolean> forceSuccess(@RequestBody UpstreamOrderReqVO reqVO) {
        adminOrderService.forceSuccess(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/force-failed")
    @PreAuthorize("@ss.hasPermission('order:system-order:force-failed')")
    @Operation(summary = "强制订单失败", description = "用于【订单管理】列表界面")
    public CommonResult<Boolean> forceFailed(@RequestBody OrderNoListReqVO reqVO) {
        adminOrderService.forceFailed(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/force-failed/detail")
    @PreAuthorize("@ss.hasPermission('order:system-order:force-failed')")
    @Operation(summary = "强制订单失败", description = "用于【订单管理】详情界面")
    public CommonResult<Boolean> forceFailed(@RequestBody UpstreamOrderReqVO reqVO) {
        adminOrderService.forceFailed(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/re-order")
    @PreAuthorize("@ss.hasPermission('order:system-order:re-order')")
    @Operation(summary = "重新下单", description = "用于【订单管理】列表界面")
    public CommonResult<Boolean> reOrder(@RequestBody OrderNoListReqVO reqVO) {
        adminOrderService.reOrder(reqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/re-order/detail")
    @PreAuthorize("@ss.hasPermission('order:system-order:re-order')")
    @Operation(summary = "重新下单", description = "用于【订单管理】详情界面")
    public CommonResult<Boolean> reOrder(@RequestBody UpstreamOrderReqVO reqVO) {
        adminOrderService.reOrder(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('order:system-order:export')")
    @Operation(summary = "导出订单")
    public void exportOrderList(@Validated OrderListReqVO exportReqVO,
                               HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);

        var list = adminOrderService.getOrderList(exportReqVO).getList();
        var exportList = OrderConvert.INSTANCE.convertList(list);
        
        ExcelUtils.write(response, "订单数据.xlsx", "数据", OrderExportVO.class, exportList);
    }

}