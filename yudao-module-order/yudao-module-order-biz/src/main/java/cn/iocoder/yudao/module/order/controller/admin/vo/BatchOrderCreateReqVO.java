package cn.iocoder.yudao.module.order.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "批充批采订单管理 - 订单创建 Response VO")
public class BatchOrderCreateReqVO {
    @NotNull(message = "商品ID不能为空")
    @Schema(description = "商品ID", example = "1L")
    private Long productId;

    @NotNull(message = "商品价格不能为空")
    @Schema(description = "商品价格", example = "1.1")
    private BigDecimal price;

    @NotEmpty(message = "充值账号不能为空")
    @Schema(description = "商品价格", example = "************")
    private List<String> rechargeAccounts;
}
