package cn.iocoder.yudao.module.order.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.order.controller.admin.vo.BatchOrderCreateReqVO;
import cn.iocoder.yudao.module.order.service.BatchOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Tag(name = "批充批采订单管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("order/batch-order")
public class BatchOrderController {
    private final BatchOrderService batchOrderService;

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('batch-charge:order:create')")
    @Operation(summary = "创建批充批采订单")
    public CommonResult<Boolean> createBatchOrder(@RequestBody @Valid List<BatchOrderCreateReqVO> reqVOList) {
        batchOrderService.saveBatchOrderToRedis(reqVOList);
        return CommonResult.success(true);
    }

}
