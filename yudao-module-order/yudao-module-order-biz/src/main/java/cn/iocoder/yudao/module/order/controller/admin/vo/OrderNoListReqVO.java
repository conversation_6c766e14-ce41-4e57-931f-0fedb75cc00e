package cn.iocoder.yudao.module.order.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Schema(description = "订单 - 订单号列表 VO")
@Data
public class OrderNoListReqVO {
    @Schema(description = "订单号列表", example = "[1234567890, 987654321]")
    @NotEmpty(message = "订单号列表不能为空")
    List<String> orderNos;
}
