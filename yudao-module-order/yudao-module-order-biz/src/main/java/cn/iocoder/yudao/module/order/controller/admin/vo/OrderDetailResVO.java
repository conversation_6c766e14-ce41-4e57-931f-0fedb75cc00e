package cn.iocoder.yudao.module.order.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@Schema(description = "订单 - 订单详情 VO")
public class OrderDetailResVO {

    @Schema(description = "订单基本信息")
    private OrderListResVO basicInfo;

    @Schema(description = "订单轮询日志")
    private List<OrderPollingLogResVO> pollingLogs;

    // 新增退款记录列表
    @Schema(description = "退款记录列表")
    private List<RefundRecordVO> refundRecords;

}