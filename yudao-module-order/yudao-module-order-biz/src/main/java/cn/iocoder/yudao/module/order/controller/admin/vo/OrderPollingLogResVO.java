package cn.iocoder.yudao.module.order.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "订单 - 订单轮询日志 VO")
public class OrderPollingLogResVO {
    @Schema(description = "上游订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String mainOrderNo;

    @Schema(description = "上游订单号")
    private String upstreamOrderNo;

    @Schema(description = "外部订单ID")
    private String externalOrderId;

    @Schema(description = "外部订单号")
    private String externalOrderNo;

    @Schema(description = "供应商ID")
    private String supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "上游商品名称")
    private String upstreamProductName;

    @Schema(description = "上游商品id")
    private String upstreamProductId;

    @Schema(description = "充值账号")
    private String rechargeAccount;

    @Schema(description = "上游售价")
    private BigDecimal unitSalePrice;

    @Schema(description = "充值数量")
    private Integer quantity;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "上游成本金额")
    private BigDecimal totalCost;

    @Schema(description = "订单状态（0-充值成功/1-充值中/2-充值失败/3-未处理）")
    private Integer upstreamOrderStatus;

    @Schema(description = "订单完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "备注（记录失败原因）")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
