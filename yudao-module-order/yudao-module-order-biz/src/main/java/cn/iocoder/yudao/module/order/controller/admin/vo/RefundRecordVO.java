package cn.iocoder.yudao.module.order.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@Schema(description = "退款记录 VO")
public class RefundRecordVO {

    @Schema(description = "退款记录ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "退款单号")
    private String orderNumber;

    @Schema(description = "退款金额")
    private BigDecimal amount;

    @Schema(description = "退款数量")
    private Integer quantity;

    @Schema(description = "退款状态")
    private Integer status;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}