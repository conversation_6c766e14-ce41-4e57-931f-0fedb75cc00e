package cn.iocoder.yudao.module.order.controller.admin.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderExportVO {

    @ExcelProperty("平台订单号")
    private String orderNo;

    @ExcelProperty("订单状态")
    private String orderStatus;

    @ExcelProperty("商户订单号")
    private String merchantOrderNo;

    @ExcelProperty("上游订单号")
    private String upstreamOrderNo;

    @ExcelProperty("充值账号")
    private String rechargeAccount;

    @ExcelProperty("归属地")
    private String region;

    @ExcelProperty("商品类型")
    private String productType;

    @ExcelProperty("平台商品ID")
    private Long platformProductId;

    @ExcelProperty("平台商品名称")
    private String platformProductName;

    @ExcelProperty("商户ID")
    private Long merchantId;

    @ExcelProperty("商户名称")
    private String merchantName;

    @ExcelProperty("发票类型")
    private String fapiaoType;

    @ExcelProperty("税点")
    private BigDecimal taxRate;

    @ExcelProperty("售价")
    private BigDecimal unitSalePrice;

    @ExcelProperty("销售数量")
    private Integer quantity;

    @ExcelProperty("成本")
    private BigDecimal totalCost;

    @ExcelProperty("订单金额")
    private BigDecimal orderAmount;

    @ExcelProperty("供应商")
    private String supplierName;

    @ExcelProperty("订单耗时")
    private String orderDuration;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("订单完成时间")
    private LocalDateTime completeTime;

    @ExcelProperty("售后状态")
    private String afterSaleStatus;

    @ExcelProperty("备注")
    private String remark;
} 