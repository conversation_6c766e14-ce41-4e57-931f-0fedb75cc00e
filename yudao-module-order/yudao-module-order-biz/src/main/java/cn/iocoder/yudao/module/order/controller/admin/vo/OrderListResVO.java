package cn.iocoder.yudao.module.order.controller.admin.vo;

import cn.iocoder.yudao.module.order.enums.AfterSaleStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "订单 - 查询结果 VO")
@Data
public class OrderListResVO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "系统订单号")
    private String orderNo;

    @Schema(description = "商户订单号")
    private String merchantOrderNo;

    @Schema(description = "上游订单号")
    private String upstreamOrderNo;

    @Schema(description = "充值账号")
    private String rechargeAccount;

    @Schema(description = "归属地")
    private String region;

    @Schema(description = "商品类型（直充/卡密）")
    private Integer productType;

    @Schema(description = "平台商品ID")
    private Long platformProductId;

    @Schema(description = "平台商品名称")
    private String platformProductName;

    @Schema(description = "商户ID")
    private Long merchantId;

    @Schema(description = "商户名称")
    private String merchantName;

    @Schema(description = "发票类型")
    private Integer fapiaoType;

    @Schema(description = "税点")
    private BigDecimal taxRate;

    @Schema(description = "售价（密价）")
    private BigDecimal unitSalePrice;

    @Schema(description = "销售数量")
    private Integer quantity;

    @Schema(description = "成本（上游成本*数量）")
    private BigDecimal totalCost;

    @Schema(description = "订单金额（密价*数量）")
    private BigDecimal orderAmount;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "售后状态")
    private Integer afterSaleStatus;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "订单完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}