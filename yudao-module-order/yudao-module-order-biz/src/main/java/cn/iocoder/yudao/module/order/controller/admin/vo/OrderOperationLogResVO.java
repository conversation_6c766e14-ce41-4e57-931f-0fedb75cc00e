package cn.iocoder.yudao.module.order.controller.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "订单 - 订单操作日志 VO")
public class OrderOperationLogResVO {
    @Schema(description = "日志ID")
    private Long id;

    @Schema(description = "主订单号")
    private String mainOrderNo;

    @Schema(description = "日志类型")
    private Integer logType;

    @Schema(description = "日志内容")
    private String logContent;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建者")
    private String creator;
}
