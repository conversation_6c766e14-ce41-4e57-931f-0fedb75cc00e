package cn.iocoder.yudao.module.merchant.controller.admin;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.*;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantDO;
import cn.iocoder.yudao.module.merchant.service.MerchantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "商户管理")
@Validated
@RestController
@RequestMapping("/merchant")
@RequiredArgsConstructor
public class MerchantController {

    private final MerchantService merchantService;

    @PostMapping("/create")
    @Operation(summary = "创建商户")
    @PreAuthorize("@ss.hasPermission('merchant:data:create')")
    public CommonResult<Long> saveMerchant(@Valid @RequestBody MerchantSaveVO merchantSaveVO) {
        Long id = merchantService.createMerchant(merchantSaveVO);
        return CommonResult.success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "修改商户")
    @PreAuthorize("@ss.hasPermission('merchant:data:update')")
    public CommonResult<Long> updateMerchant(@Valid @RequestBody MerchantSaveVO merchantSaveVO) {
        Long id = merchantService.updateMerchant(merchantSaveVO);
        return CommonResult.success(id);
    }

    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('merchant:data:query')")
    @Operation(summary = "获取商户列表", description = "用于【商户管理】界面")
    public CommonResult<PageResult<MerchantResVO>> getMerchantList(MerchantListReqVO req) {
        PageResult<MerchantDO> list = merchantService.getMerchantList(req);
        return CommonResult.success(BeanUtils.toBean(list, MerchantResVO.class));
    }

    @GetMapping("/all")
    @PreAuthorize("@ss.hasPermission('merchant:data:query')")
    @Operation(summary = "获取所有商户列表", description = "用于下拉选择等场景")
    public CommonResult<List<MerchantResVO>> getAllMerchantList() {
        List<MerchantDO> list = merchantService.getAllMerchantList();
        return CommonResult.success(BeanUtils.toBean(list, MerchantResVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "根据ID获取商户")
    @PreAuthorize("@ss.hasPermission('merchant:data:query')")
    public CommonResult<MerchantResVO> getMerchant(Long id) {
        MerchantDO merchantDO = merchantService.getMerchant(id);
        return CommonResult.success(BeanUtils.toBean(merchantDO, MerchantResVO.class));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商户")
    @PreAuthorize("@ss.hasPermission('merchant:data:delete')")
    public CommonResult<Boolean> deleteMerchant(Long id) {
        merchantService.deleteMerchant(id);
        return CommonResult.success(true);
    }

    @PostMapping("/recharge")
    @Operation(summary = "加减余额")
    @PreAuthorize("@ss.hasPermission('merchant:data:recharge')")
    public CommonResult<Boolean> rechargeForMerchant(@RequestBody RechargeReqVO rechargeReqVO) {
        merchantService.recharge(rechargeReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/modify-credit-limit")
    @Operation(summary = "修改授信额度")
    @PreAuthorize("@ss.hasPermission('merchant:data:modify-credit-limit')")
    public CommonResult<Boolean> modifyCreditLimit(@RequestBody RechargeReqVO rechargeReqVO) {
        merchantService.modifyCreditLimit(rechargeReqVO);
        return CommonResult.success(true);
    }
}
