package cn.iocoder.yudao.module.merchant.service;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.MerchantListReqVO;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.MerchantSaveVO;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.RechargeReqVO;
import cn.iocoder.yudao.module.merchant.controller.app.vo.MerchantInfoReqVO;
import cn.iocoder.yudao.module.merchant.controller.app.vo.MerchantInfoResVO;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantDO;
import jakarta.validation.Valid;

import java.util.List;

public interface MerchantService {

    /**
     * 筛选商户列表
     *
     * @param req 筛选条件请求 VO
     * @return 商户列表
     */
    PageResult<MerchantDO> getMerchantList(MerchantListReqVO req);

    /**
     * 获取所有商户列表
     *
     * @return 所有商户列表
     */
    List<MerchantDO> getAllMerchantList();

    /**
     * 新建商户
     *
     * @param merchantSaveVO 新建/修改请求 VO
     * @return 商户ID
     */
    Long createMerchant(@Valid MerchantSaveVO merchantSaveVO);

    /**
     * 根据 ID 获取商户信息
     *
     * @param id 商户ID
     * @return 商户信息
     */
    MerchantDO getMerchant(Long id);

    /**
     * 删除商户
     *
     * @param id 商户ID
     */
    void deleteMerchant(Long id);

    /**
     * 修改商户
     *
     * @param merchantSaveVO 新建/修改请求 VO
     * @return 商户ID
     */
    Long updateMerchant(@Valid MerchantSaveVO merchantSaveVO);

    /**
     * 充值余额
     *
     * @param rechargeReqVO 加减余额/授信额度请求 VO
     */
    void recharge(RechargeReqVO rechargeReqVO);

    /**
     * 修改授信额度
     *
     * @param rechargeReqVO 加减余额/授信额度请求 VO
     */
    void modifyCreditLimit(RechargeReqVO rechargeReqVO);

    /**
     * 获取商户信息
     *
     * @param reqVO 获取商户信息请求 VO
     * @return 商户信息
     */
    MerchantInfoResVO getMerchantInfo(@Valid MerchantInfoReqVO reqVO) throws Exception;
}
