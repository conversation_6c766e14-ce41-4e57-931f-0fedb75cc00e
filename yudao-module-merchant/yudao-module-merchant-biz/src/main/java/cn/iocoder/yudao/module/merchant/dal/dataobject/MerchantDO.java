package cn.iocoder.yudao.module.merchant.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.merchant.enums.TaxInclusiveType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@TableName(value = "merchant", autoResultMap = true)
@KeySequence("merchant_seq")
@Data
@EqualsAndHashCode(callSuper = true)
public class MerchantDO extends BaseDO {

    /**
     * 商户编号
     */
    @TableId
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 交易密码
     */
    private String transactionPassword;

    /**
     * 签名密钥
     */
    private String signKey;

    /**
     * 公司主体
     */
    private String companyEntity;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 授信额度
     */
    private BigDecimal creditLimit;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 商品变更回调地址
     */
    private String productChangeCallbackUrl;

    /**
     * 含税类型
     * 枚举 {@link TaxInclusiveType}
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<TaxInclusiveType> taxInclusiveType;

    /**
     * 是否允许负利润
     */
    private Boolean allowNegativeProfit;

    /**
     * IP白名单
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> ipWhitelist;

    /**
     * 商户商品ID List
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> productIdList;

    /**
     * 商户提货券模板ID List
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> voucherTemplateIdList;

    /**
     * 是否启用
     */
    private Boolean enable;

}
