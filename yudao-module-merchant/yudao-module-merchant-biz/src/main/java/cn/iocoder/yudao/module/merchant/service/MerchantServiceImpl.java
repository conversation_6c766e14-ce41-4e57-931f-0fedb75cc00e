package cn.iocoder.yudao.module.merchant.service;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.encryption.EncryptionUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.MerchantListReqVO;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.MerchantSaveVO;
import cn.iocoder.yudao.module.merchant.controller.admin.vo.RechargeReqVO;
import cn.iocoder.yudao.module.merchant.controller.app.vo.MerchantInfoReqVO;
import cn.iocoder.yudao.module.merchant.controller.app.vo.MerchantInfoResVO;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantDO;
import cn.iocoder.yudao.module.merchant.dal.dataobject.MerchantRechargeRequestDO;
import cn.iocoder.yudao.module.merchant.dal.mysql.MerchantMapper;
import cn.iocoder.yudao.module.merchant.dal.mysql.RechargeRequestMapper;
import cn.iocoder.yudao.module.merchant.enums.RechargeType;
import cn.iocoder.yudao.module.merchant.utils.Utils;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_BALANCE_UPDATE_FAILED;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_INFO_SIGN_CHECK_FAILED;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_INFO_TIMESTAMP_INVALID;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_NOT_EXISTS;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_SIGN_KEY_IS_EMPTY;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MERCHANT_SIGN_KEY_NOT_EXISTS;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MONEY_IS_EMPTY;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MONEY_IS_ZERO;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.MONEY_TYPE_ERROR;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.PASSWORD_IS_EMPTY;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.TOTAL_MONEY_CAN_NOT_LESS_ZERO;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.USER_MOBILE_EXISTS;
import static cn.iocoder.yudao.module.merchant.enums.ErrorCodeConstants.USER_USERNAME_EXISTS;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_CREATE_SUB_TYPE;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_CREATE_SUCCESS;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_CREDIT_LIMIT_SUB_TYPE;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_CREDIT_LIMIT_SUCCESS;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_DELETE_SUB_TYPE;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_DELETE_SUCCESS;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_RECHARGE_SUB_TYPE;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_RECHARGE_SUCCESS;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_TYPE;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.merchant.enums.MerchantLogRecordConstants.MERCHANT_DATA_UPDATE_SUCCESS;
import static com.mzt.logapi.service.impl.DiffParseFunction.OLD_OBJECT;

@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantServiceImpl implements MerchantService {

    private final MerchantMapper merchantMapper;
    private final RechargeRequestMapper rechargeRequestMapper;
    private final PasswordEncoder passwordEncoder;
    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public PageResult<MerchantDO> getMerchantList(MerchantListReqVO req) {
        return merchantMapper.selectList(req);
    }

    @Override
    public List<MerchantDO> getAllMerchantList() {
        return merchantMapper.selectList();
    }

    @Override
    @SneakyThrows
    @LogRecord(type = MERCHANT_DATA_TYPE, subType = MERCHANT_DATA_CREATE_SUB_TYPE, bizNo = "{{#merchantDO.id}}",
            success = MERCHANT_DATA_CREATE_SUCCESS)
    public Long createMerchant(MerchantSaveVO merchantSaveVO) {
        validateMerchantForCreate(merchantSaveVO);

        var merchantDO = BeanUtils.toBean(merchantSaveVO, MerchantDO.class);
        merchantDO.setPassword(passwordEncoder.encode(merchantDO.getPassword()));
        merchantDO.setTransactionPassword(passwordEncoder.encode(merchantDO.getTransactionPassword()));
        merchantDO.setSignKey(EncryptionUtil.encrypt(merchantSaveVO.getSignKey()));
        merchantMapper.insert(merchantDO);
        LogRecordContext.putVariable("merchantDO", merchantDO);

        saveWhiteListToRedis(merchantDO.getId(), merchantSaveVO);

        return merchantDO.getId();
    }

    @Override
    @SneakyThrows
    @LogRecord(type = MERCHANT_DATA_TYPE, subType = MERCHANT_DATA_UPDATE_SUB_TYPE, bizNo = "{{#merchantSaveVO.id}}",
            success = MERCHANT_DATA_UPDATE_SUCCESS)
    public Long updateMerchant(MerchantSaveVO merchantSaveVO) {
        if (merchantSaveVO.getId() == null) {
            throw exception(MERCHANT_NOT_EXISTS);
        }
        validateUsernameUnique(merchantSaveVO.getId(), merchantSaveVO.getUsername());
        validatePhoneUnique(merchantSaveVO.getId(), merchantSaveVO.getPhone());
        var oldMerchant = merchantMapper.selectById(merchantSaveVO.getId());
        if (Objects.nonNull(merchantSaveVO.getPassword())) {
            merchantSaveVO.setPassword(passwordEncoder.encode(merchantSaveVO.getPassword()));
        } else {
            oldMerchant.setPassword(null);
        }
        if (Objects.nonNull(merchantSaveVO.getTransactionPassword())) {
            merchantSaveVO.setTransactionPassword(passwordEncoder.encode(merchantSaveVO.getTransactionPassword()));
        } else {
            oldMerchant.setTransactionPassword(null);
        }
        if (Objects.nonNull(merchantSaveVO.getSignKey())) {
            merchantSaveVO.setSignKey(EncryptionUtil.encrypt(merchantSaveVO.getSignKey()));
        }
        var merchantDO = BeanUtils.toBean(merchantSaveVO, MerchantDO.class);
        merchantMapper.updateById(merchantDO);
        LogRecordContext.putVariable(OLD_OBJECT, BeanUtils.toBean(oldMerchant, MerchantSaveVO.class));
        LogRecordContext.putVariable("merchantDO", oldMerchant);

        saveWhiteListToRedis(merchantDO.getId(), merchantSaveVO);

        return merchantDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = MERCHANT_DATA_TYPE, subType = MERCHANT_DATA_RECHARGE_SUB_TYPE,
            bizNo = "{{#rechargeReqVO.merchantId}}",
            success = MERCHANT_DATA_RECHARGE_SUCCESS)
    public void recharge(RechargeReqVO rechargeReqVO) {
        validateMerchantExists(rechargeReqVO.getMerchantId());
        validateAmountOfMoney(rechargeReqVO, RechargeType.BALANCE);
//    validateMoneyType(rechargeReqVO, RechargeType.BALANCE);

        var merchantDO = merchantMapper.selectById(rechargeReqVO.getMerchantId());
        var rechargeRequestDO = BeanUtils.toBean(rechargeReqVO, MerchantRechargeRequestDO.class);
        rechargeRequestDO.setBeforeAmountOfMoney(merchantDO.getBalance());

        // 使用原子更新余额的方法
        int updatedRows = merchantMapper.updateBalanceAtomic(rechargeReqVO.getMerchantId(), rechargeReqVO.getAmountOfMoney());
        if (updatedRows != 1) {
            throw exception(MERCHANT_BALANCE_UPDATE_FAILED);
        }

        rechargeRequestDO.setAfterAmountOfMoney(merchantDO.getBalance().add(rechargeReqVO.getAmountOfMoney()));
        rechargeRequestMapper.insert(rechargeRequestDO);
        LogRecordContext.putVariable("amountOfMoney", rechargeReqVO.getAmountOfMoney());
        LogRecordContext.putVariable("merchantDO", merchantDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = MERCHANT_DATA_TYPE, subType = MERCHANT_DATA_CREDIT_LIMIT_SUB_TYPE,
            bizNo = "{{#rechargeReqVO.merchantId}}",
            success = MERCHANT_DATA_CREDIT_LIMIT_SUCCESS)
    public void modifyCreditLimit(RechargeReqVO rechargeReqVO) {
        validateMerchantExists(rechargeReqVO.getMerchantId());
        validateAmountOfMoney(rechargeReqVO, RechargeType.CREDIT_LIMIT);
        validateMoneyType(rechargeReqVO, RechargeType.CREDIT_LIMIT);

        var merchantDO = merchantMapper.selectById(rechargeReqVO.getMerchantId());
        var rechargeRequestDO = BeanUtils.toBean(rechargeReqVO, MerchantRechargeRequestDO.class);
        rechargeRequestDO.setBeforeAmountOfMoney(merchantDO.getCreditLimit());
        merchantDO.setCreditLimit(rechargeReqVO.getAmountOfMoney());
        rechargeRequestDO.setAfterAmountOfMoney(merchantDO.getCreditLimit());
        rechargeRequestMapper.insert(rechargeRequestDO);
        merchantMapper.updateById(merchantDO);
        LogRecordContext.putVariable("beforeCreditLimit", merchantDO.getBalance());
        LogRecordContext.putVariable("afterCreditLimit", rechargeReqVO.getAmountOfMoney());
        LogRecordContext.putVariable("merchantDO", merchantDO);
    }

    @Override
    @SneakyThrows
    public MerchantInfoResVO getMerchantInfo(MerchantInfoReqVO reqVO) {
        checkTimestamp(reqVO.getTimestamp());
        MerchantDO merchantDO = merchantMapper.selectById(reqVO.getMerchantId());
        if (merchantDO == null) {
            throw exception(MERCHANT_NOT_EXISTS);
        }
        if (Objects.nonNull(merchantDO.getSignKey())) {
            merchantDO.setSignKey(EncryptionUtil.decrypt(merchantDO.getSignKey()));
        } else {
            throw exception(MERCHANT_SIGN_KEY_NOT_EXISTS);
        }
        String signKey = Utils.MD5(Utils.generateSortedQueryString(Utils.convertEntityToMap(reqVO), merchantDO.getSignKey()));
        checkSignKey(reqVO.getSignKey(), signKey);
        return BeanUtils.toBean(merchantDO, MerchantInfoResVO.class);
    }

    private void checkSignKey(String signKeyFromRequest, String generatedSignKey) {
        log.info("开始校验签名");

        if (!signKeyFromRequest.equals(generatedSignKey)) {
            log.error("校验签名失败!");
            throw exception(MERCHANT_INFO_SIGN_CHECK_FAILED);
        }
    }

    private void checkTimestamp(Long timestamp) {
        // 校验 timestamp 是否在有效范围内
        long currentTimestamp = System.currentTimeMillis(); // 当前时间戳（秒）
        if (timestamp == null ||
                Math.abs(currentTimestamp - timestamp) > 300000) {
            log.error("时间戳无效，当前时间戳: {}, 请求时间戳: {}", currentTimestamp, timestamp);
            throw exception(MERCHANT_INFO_TIMESTAMP_INVALID);
        }
    }

    @Override
    @SneakyThrows
    public MerchantDO getMerchant(Long id) {
        var merchantDO = merchantMapper.selectById(id);
        if (merchantDO != null && Objects.nonNull(merchantDO.getSignKey())) {
            merchantDO.setSignKey(EncryptionUtil.decrypt(merchantDO.getSignKey()));
        }
        return merchantDO;
    }

    @Override
    @LogRecord(type = MERCHANT_DATA_TYPE, subType = MERCHANT_DATA_DELETE_SUB_TYPE,
            bizNo = "{{#id}}",
            success = MERCHANT_DATA_DELETE_SUCCESS)
    public void deleteMerchant(Long id) {
        var merchantDO = merchantMapper.selectById(id);
        merchantMapper.deleteById(id);
        LogRecordContext.putVariable("merchantDO", merchantDO);
    }

    private void validateMerchantForCreate(MerchantSaveVO merchantSaveVO) {
        validateUsernameUnique(null, merchantSaveVO.getUsername());
        validatePhoneUnique(null, merchantSaveVO.getPhone());
        validatePasswordNotEmpty(merchantSaveVO.getPassword());
        validatePasswordNotEmpty(merchantSaveVO.getTransactionPassword());
        if (Objects.isNull(merchantSaveVO.getSignKey())) {
            throw exception(MERCHANT_SIGN_KEY_IS_EMPTY);
        }
    }

    private void validateUsernameUnique(Long id, String username) {
        MerchantDO merchantDO = merchantMapper.selectByUsername(username);
        if (Objects.isNull(merchantDO)) {
            return;
        }
        if (!Objects.equals(id, merchantDO.getId())) {
            throw exception(USER_USERNAME_EXISTS);
        }
    }

    private void validatePhoneUnique(Long id, String phone) {
        MerchantDO merchantDO = merchantMapper.selectByPhone(phone);
        if (Objects.isNull(merchantDO)) {
            return;
        }
        if (!Objects.equals(id, merchantDO.getId())) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }

    private void validatePasswordNotEmpty(String password) {
        if (StringUtils.isEmpty(password)) {
            throw exception(PASSWORD_IS_EMPTY);
        }
    }

    private void validateMerchantExists(Long id) {
        if (Objects.isNull(id)) {
            throw exception(MERCHANT_NOT_EXISTS);
        }
        var exists = merchantMapper.exists(new LambdaQueryWrapperX<MerchantDO>().eq(MerchantDO::getId, id));
        if (!exists) {
            throw exception(MERCHANT_NOT_EXISTS);
        }
    }

    private void validateAmountOfMoney(RechargeReqVO rechargeReqVO, RechargeType type) {
        if (Objects.isNull(rechargeReqVO.getAmountOfMoney())) {
            throw exception(MONEY_IS_EMPTY);
        }
        if (type.equals(RechargeType.BALANCE) && NumberUtil.isLess(rechargeReqVO.getAmountOfMoney(), BigDecimal.ZERO)) {
            throw exception(MONEY_IS_ZERO);
        }
        var merchantDO = merchantMapper.selectById(rechargeReqVO.getMerchantId());
        BigDecimal result;
        if (type.equals(RechargeType.CREDIT_LIMIT)) {
            result = merchantDO.getBalance().add(rechargeReqVO.getAmountOfMoney());
        } else {
            result = merchantDO.getBalance().add(merchantDO.getCreditLimit()).add(rechargeReqVO.getAmountOfMoney());
        }
        if (NumberUtil.isLess(result, BigDecimal.ZERO)) {
            throw exception(TOTAL_MONEY_CAN_NOT_LESS_ZERO);
        }
    }

    private void validateMoneyType(RechargeReqVO rechargeReqVO, RechargeType type) {
        if (!rechargeReqVO.getRechargeType().equals(type)) {
            throw exception(MONEY_TYPE_ERROR);
        }
    }

    private void saveWhiteListToRedis(Long merchantId, MerchantSaveVO merchantSaveVO) {
        String whiteListKey = "merchant:whiteList:" + merchantId;
        if (merchantSaveVO.getIpWhitelist() != null && !merchantSaveVO.getIpWhitelist().isEmpty()) {
            stringRedisTemplate.opsForValue().set(whiteListKey, String.join(",", merchantSaveVO.getIpWhitelist()));
        } else {
            stringRedisTemplate.delete(whiteListKey);
        }
    }
}
