package cn.iocoder.yudao.module.infra.framework.file.core.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import javax.imageio.ImageIO;

/**
 * 图片验证工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ImageValidationUtils {

    /**
     * 支持的图片扩展名
     */
    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp"
    );

    /**
     * 默认最大图片大小（512KB）
     */
    public static final long DEFAULT_MAX_IMAGE_SIZE = 512 * 1024;

    /**
     * 默认最大图片宽度（4096px）
     */
    public static final int DEFAULT_MAX_WIDTH = 4096;

    /**
     * 默认最大图片高度（4096px）
     */
    public static final int DEFAULT_MAX_HEIGHT = 4096;

    /**
     * 验证字节数组是否为图片
     *
     * @param content   文件内容
     * @param filename  文件名
     * @param maxSize   最大文件大小
     * @param maxWidth  最大宽度
     * @param maxHeight 最大高度
     * @return 验证结果
     */
    public static ValidationResult validateImageBytes(byte[] content, String filename, long maxSize, int maxWidth, int maxHeight) {
        try {
            // 1. 检查内容是否为空
            if (content == null || content.length == 0) {
                return ValidationResult.fail("文件内容不能为空");
            }

            // 2. 检查文件大小
            if (content.length > maxSize) {
                return ValidationResult.fail(String.format("图片大小不能超过 %s", formatFileSize(maxSize)));
            }

            // 3. 检查文件扩展名
            if (StrUtil.isNotBlank(filename)) {
                String extension = getFileExtension(filename);
                if (StrUtil.isBlank(extension) || !SUPPORTED_IMAGE_EXTENSIONS.contains(extension.toLowerCase())) {
                    return ValidationResult.fail("不支持的图片扩展名，支持扩展名：jpg、png、gif、bmp、webp");
                }
            }

            // 4. 验证图片内容和尺寸
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(content));
            
            if (image == null) {
                return ValidationResult.fail("文件不是有效的图片格式");
            }

            // 5. 检查图片尺寸
            int width = image.getWidth();
            int height = image.getHeight();
            
            if (width > maxWidth || height > maxHeight) {
                return ValidationResult.fail(String.format("图片尺寸不能超过 %dx%d 像素，当前尺寸：%dx%d", 
                        maxWidth, maxHeight, width, height));
            }

            // 验证通过
            return ValidationResult.success(String.format("图片验证通过，尺寸：%dx%d，大小：%s", 
                    width, height, formatFileSize(content.length)));

        } catch (IOException e) {
            log.error("图片验证失败", e);
            return ValidationResult.fail("图片文件读取失败，请检查文件是否损坏");
        } catch (Exception e) {
            log.error("图片验证异常", e);
            return ValidationResult.fail("图片验证失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean success;
        private final String message;

        private ValidationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public static ValidationResult success(String message) {
            return new ValidationResult(true, message);
        }

        public static ValidationResult fail(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isSuccess() {
            return success;
        }

        public boolean isFail() {
            return !success;
        }

        public String getMessage() {
            return message;
        }

        /**
         * 如果验证失败，抛出异常
         */
        public void throwIfFail() {
            if (isFail()) {
                throw new IllegalArgumentException(message);
            }
        }
    }
}
