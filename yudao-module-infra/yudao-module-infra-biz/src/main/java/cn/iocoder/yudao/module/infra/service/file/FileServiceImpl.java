package cn.iocoder.yudao.module.infra.service.file;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileTypeUtils;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.ImageValidationUtils;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileMapper;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 如果是图片文件，进行图片验证
        if (isImageFile(type, name)) {
            ImageValidationUtils.ValidationResult validationResult = 
                ImageValidationUtils.validateImageBytes(content, name, 
                    ImageValidationUtils.DEFAULT_MAX_IMAGE_SIZE, 
                    ImageValidationUtils.DEFAULT_MAX_WIDTH, 
                    ImageValidationUtils.DEFAULT_MAX_HEIGHT);
            
            if (validationResult.isFail()) {
                throw new IllegalArgumentException(validationResult.getMessage());
            }
            
            log.info("图片验证通过: {}", validationResult.getMessage());
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @Override
    public Long createFile(FileCreateReqVO createReqVO) {
        FileDO file = BeanUtils.toBean(createReqVO, FileDO.class);
        fileMapper.insert(file);
        return file.getId();
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client.getContent(path);
    }

    @Override
    public FilePresignedUrlRespVO getFilePresignedUrl(String path) throws Exception {
        FileClient fileClient = fileConfigService.getMasterFileClient();
        FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(path);
        return BeanUtils.toBean(presignedObjectUrl, FilePresignedUrlRespVO.class,
                object -> object.setConfigId(fileClient.getId()));
    }


    @Override
    public void deleteFileByPath(String path) throws Exception {
        log.info("[deleteFileByPath] 开始删除文件，路径: {}", path);
        
        try {
            // 查找文件记录（可能有多个相同路径的记录）
            FileDO file = fileMapper.selectByPath(path);
            if (file != null) {
                log.info("[deleteFileByPath] 找到数据库记录，文件ID: {}，将删除数据库记录和云存储文件", file.getId());
                // 如果数据库中有记录，使用原有的删除方法（会同时删除数据库记录和云存储文件）
                deleteFile(file.getId());
            } else {
                log.info("[deleteFileByPath] 数据库中无记录，直接删除云存储文件");
                // 如果数据库中没有记录，直接从存储器中删除
                FileClient fileClient = fileConfigService.getMasterFileClient();
                if (fileClient == null) {
                    throw new RuntimeException("文件客户端未配置");
                }
                fileClient.delete(path);
            }
            log.info("[deleteFileByPath] 文件删除成功，路径: {}", path);
        } catch (Exception e) {
            log.error("[deleteFileByPath] 删除文件失败，路径: {}, 错误: {}", path, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String mimeType, String fileName) {
        // 根据MIME类型判断
        if (StrUtil.isNotBlank(mimeType) && mimeType.startsWith("image/")) {
            return true;
        }
        
        // 根据文件扩展名判断
        if (StrUtil.isNotBlank(fileName)) {
            String extension = getFileExtension(fileName);
            if (StrUtil.isNotBlank(extension)) {
                String lowerExt = extension.toLowerCase();
                return lowerExt.equals("jpg") || lowerExt.equals("jpeg") || 
                       lowerExt.equals("png") || lowerExt.equals("gif") || 
                       lowerExt.equals("bmp") || lowerExt.equals("webp");
            }
        }
        
        return false;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

}
