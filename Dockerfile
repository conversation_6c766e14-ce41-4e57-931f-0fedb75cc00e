# 构建阶段
FROM maven:3.9.9-eclipse-temurin-21-alpine AS build

# 设置工作目录
WORKDIR /app

# 首先复制父 pom 文件
COPY ./pom.xml pom.xml
COPY ./yudao-dependencies/pom.xml yudao-dependencies/pom.xml
COPY ./yudao-framework/pom.xml yudao-framework/pom.xml
COPY ./yudao-server/pom.xml yudao-server/pom.xml
COPY ./yudao-shop-server/pom.xml yudao-shop-server/pom.xml
COPY ./yudao-order-server/pom.xml yudao-order-server/pom.xml
COPY ./yudao-voucher-server/pom.xml yudao-voucher-server/pom.xml
COPY ./yudao-module-system/pom.xml yudao-module-system/pom.xml
COPY ./yudao-module-infra/pom.xml yudao-module-infra/pom.xml
COPY ./yudao-module-merchant/pom.xml yudao-module-merchant/pom.xml
COPY ./yudao-module-supplier/pom.xml yudao-module-supplier/pom.xml
COPY ./yudao-module-product/pom.xml yudao-module-product/pom.xml
COPY ./yudao-module-Integration/pom.xml yudao-module-Integration/pom.xml
COPY ./yudao-module-order/pom.xml yudao-module-order/pom.xml
COPY ./yudao-module-callback/pom.xml yudao-module-callback/pom.xml

# 复制 framework 子模块的 pom 文件
COPY ./yudao-framework/yudao-common/pom.xml yudao-framework/yudao-common/
COPY ./yudao-framework/yudao-spring-boot-starter-mybatis/pom.xml yudao-framework/yudao-spring-boot-starter-mybatis/
COPY ./yudao-framework/yudao-spring-boot-starter-redis/pom.xml yudao-framework/yudao-spring-boot-starter-redis/
COPY ./yudao-framework/yudao-spring-boot-starter-web/pom.xml yudao-framework/yudao-spring-boot-starter-web/
COPY ./yudao-framework/yudao-spring-boot-starter-security/pom.xml yudao-framework/yudao-spring-boot-starter-security/
COPY ./yudao-framework/yudao-spring-boot-starter-websocket/pom.xml yudao-framework/yudao-spring-boot-starter-websocket/
COPY ./yudao-framework/yudao-spring-boot-starter-monitor/pom.xml yudao-framework/yudao-spring-boot-starter-monitor/
COPY ./yudao-framework/yudao-spring-boot-starter-protection/pom.xml yudao-framework/yudao-spring-boot-starter-protection/
COPY ./yudao-framework/yudao-spring-boot-starter-job/pom.xml yudao-framework/yudao-spring-boot-starter-job/
COPY ./yudao-framework/yudao-spring-boot-starter-mq/pom.xml yudao-framework/yudao-spring-boot-starter-mq/
COPY ./yudao-framework/yudao-spring-boot-starter-excel/pom.xml yudao-framework/yudao-spring-boot-starter-excel/
COPY ./yudao-framework/yudao-spring-boot-starter-biz-tenant/pom.xml yudao-framework/yudao-spring-boot-starter-biz-tenant/
COPY ./yudao-framework/yudao-spring-boot-starter-biz-data-permission/pom.xml yudao-framework/yudao-spring-boot-starter-biz-data-permission/
COPY ./yudao-framework/yudao-spring-boot-starter-biz-ip/pom.xml yudao-framework/yudao-spring-boot-starter-biz-ip/

# 复制 system 模块的子模块 pom 文件
COPY ./yudao-module-system/yudao-module-system-api/pom.xml yudao-module-system/yudao-module-system-api/
COPY ./yudao-module-system/yudao-module-system-biz/pom.xml yudao-module-system/yudao-module-system-biz/

# 复制 infra 模块的子模块 pom 文件
COPY ./yudao-module-infra/yudao-module-infra-api/pom.xml yudao-module-infra/yudao-module-infra-api/
COPY ./yudao-module-infra/yudao-module-infra-biz/pom.xml yudao-module-infra/yudao-module-infra-biz/

# 复制 merchant 模块的子模块 pom 文件
COPY ./yudao-module-merchant/yudao-module-merchant-api/pom.xml yudao-module-merchant/yudao-module-merchant-api/
COPY ./yudao-module-merchant/yudao-module-merchant-biz/pom.xml yudao-module-merchant/yudao-module-merchant-biz/

# 复制 supplier 模块的子模块 pom 文件
COPY ./yudao-module-supplier/yudao-module-supplier-api/pom.xml yudao-module-supplier/yudao-module-supplier-api/
COPY ./yudao-module-supplier/yudao-module-supplier-biz/pom.xml yudao-module-supplier/yudao-module-supplier-biz/

# 复制 product 模块的子模块 pom 文件
COPY ./yudao-module-product/yudao-module-product-api/pom.xml yudao-module-product/yudao-module-product-api/
COPY ./yudao-module-product/yudao-module-product-biz/pom.xml yudao-module-product/yudao-module-product-biz/

## 复制 integration 模块的子模块 pom 文件
COPY ./yudao-module-Integration/yudao-module-Integration-api/pom.xml yudao-module-Integration/yudao-module-Integration-api/
COPY ./yudao-module-Integration/yudao-module-Integration-biz/pom.xml yudao-module-Integration/yudao-module-Integration-biz/

## 复制 order 模块的子模块 pom 文件
COPY ./yudao-module-order/yudao-module-order-api/pom.xml yudao-module-order/yudao-module-order-api/
COPY ./yudao-module-order/yudao-module-order-biz/pom.xml yudao-module-order/yudao-module-order-biz/

## 复制 callback 模块的子模块 pom 文件
COPY ./yudao-module-callback/yudao-module-callback-api/pom.xml yudao-module-callback/yudao-module-callback-api/
COPY ./yudao-module-callback/yudao-module-callback-biz/pom.xml yudao-module-callback/yudao-module-callback-biz/

# 创建必要的目录
RUN mkdir -p yudao-dependencies yudao-framework yudao-server yudao-shop-server yudao-order-server yudao-voucher-server yudao-module-system yudao-module-infra \
    yudao-framework/yudao-common \
    yudao-framework/yudao-spring-boot-starter-mybatis \
    yudao-framework/yudao-spring-boot-starter-redis \
    yudao-framework/yudao-spring-boot-starter-web \
    yudao-framework/yudao-spring-boot-starter-security \
    yudao-framework/yudao-spring-boot-starter-websocket \
    yudao-framework/yudao-spring-boot-starter-monitor \
    yudao-framework/yudao-spring-boot-starter-protection \
    yudao-framework/yudao-spring-boot-starter-job \
    yudao-framework/yudao-spring-boot-starter-mq \
    yudao-framework/yudao-spring-boot-starter-excel \
    yudao-framework/yudao-spring-boot-starter-biz-tenant \
    yudao-framework/yudao-spring-boot-starter-biz-data-permission \
    yudao-framework/yudao-spring-boot-starter-biz-ip \
    yudao-module-system/yudao-module-system-api \
    yudao-module-system/yudao-module-system-biz \
    yudao-module-infra/yudao-module-infra-api \
    yudao-module-infra/yudao-module-infra-biz \
    yudao-module-supplier/yudao-module-supplier-api \
    yudao-module-supplier/yudao-module-supplier-biz \
    yudao-module-product/yudao-module-product-api \
    yudao-module-product/yudao-module-product-biz \
    yudao-module-Integration/yudao-module-Integration-api \
    yudao-module-Integration/yudao-module-Integration-biz \
    yudao-module-order/yudao-module-order-api \
    yudao-module-order/yudao-module-order-biz \
    yudao-module-callback/yudao-module-callback-api \
    yudao-module-callback/yudao-module-callback-biz


# 下载依赖
RUN mvn dependency:go-offline -B

# 然后复制源代码
COPY . .
# 执行构建
RUN mvn clean package -DskipTests

FROM eclipse-temurin:21-jre as shopServer

WORKDIR /app
COPY --from=build /app/yudao-shop-server/target/*.jar /app/app.jar

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms512m -Xmx512m -Djava.security.egd=file:/dev/./urandom"
ENV ARGS=""

EXPOSE 58080

CMD java ${JAVA_OPTS} -jar app.jar $ARGS

# 运行阶段
FROM eclipse-temurin:21-jre as server

WORKDIR /app
COPY --from=build /app/yudao-server/target/*.jar /app/app.jar

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms512m -Xmx512m -Djava.security.egd=file:/dev/./urandom"
ENV ARGS=""

EXPOSE 48080

CMD java ${JAVA_OPTS} -jar app.jar $ARGS


# 运行阶段
FROM eclipse-temurin:21-jre as orderServer

WORKDIR /app
COPY --from=build /app/yudao-order-server/target/*.jar /app/app.jar

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms512m -Xmx512m -Djava.security.egd=file:/dev/./urandom"
ENV ARGS=""

EXPOSE 38080

CMD java ${JAVA_OPTS} -jar app.jar $ARGS

# 运行阶段
FROM eclipse-temurin:21-jre as voucherServer

WORKDIR /app
COPY --from=build /app/yudao-voucher-server/target/*.jar /app/app.jar

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-Xms512m -Xmx512m -Djava.security.egd=file:/dev/./urandom"
ENV ARGS=""

EXPOSE 58080

CMD java ${JAVA_OPTS} -jar app.jar $ARGS