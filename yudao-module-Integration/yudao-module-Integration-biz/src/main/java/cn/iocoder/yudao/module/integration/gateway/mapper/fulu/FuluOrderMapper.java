package cn.iocoder.yudao.module.integration.gateway.mapper.fulu;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluDirectRechargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluOrderInfoGetDTO;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.order.FuluDirectRechargeOrderBizParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_RESPONSE_ORDER_STATUS_ERROR;

@Mapper
public interface FuluOrderMapper {
    FuluOrderMapper MAPPER = Mappers.getMapper(FuluOrderMapper.class);

    @Mappings({
            @Mapping(source = "productId", target = "productId"),
            @Mapping(source = "upstreamOrderNo", target = "customerOrderNo"),
            @Mapping(source = "rechargeAccount", target = "chargeAccount"),
            @Mapping(source = "quantity", target = "buyNum"),
    })
    FuluDirectRechargeOrderBizParams toParams(DirectRechargeOrderDTO directRechargeOrderDTO);

    @Mappings({
            @Mapping(source = "customerOrderNo", target = "upstreamOrderNo"),
            @Mapping(source = "orderId", target = "externalOrderNo"),
            @Mapping(source = "productName", target = "upstreamProductName"),
            @Mapping(source = "chargeAccount", target = "rechargeAccount"),
            @Mapping(source = "orderPrice", target = "unitSalePrice", qualifiedByName = "mapToBigDecimal"),
            @Mapping(source = "buyNum", target = "quantity"),
            @Mapping(source = "orderState", target = "upstreamOrderStatus", qualifiedByName = "mapUpstreamOrderStatus"),
            @Mapping(source = "finishTime", target = "finishTime", qualifiedByName = "mapTime")
    })
    DirectChargeResultDTO toResult(FuluDirectRechargeResultDTO fuluDirectRechargeResultDTO);

    @Mappings({
            @Mapping(source = "customerOrderNo", target = "upstreamOrderNo"),
            @Mapping(source = "orderId", target = "externalOrderNo"),
            @Mapping(source = "productName", target = "upstreamProductName"),
            @Mapping(source = "chargeAccount", target = "rechargeAccount"),
            @Mapping(source = "orderPrice", target = "unitSalePrice", qualifiedByName = "mapToBigDecimal"),
            @Mapping(source = "productId", target = "productId", qualifiedByName = "mapStringToInt"),
            @Mapping(source = "buyNum", target = "quantity", qualifiedByName = "mapStringToInt"),
            @Mapping(source = "orderState", target = "upstreamOrderStatus", qualifiedByName = "mapUpstreamOrderStatus"),
            @Mapping(source = "finishTime", target = "completeTime", qualifiedByName = "mapTime")
    })
    UpstreamOrderQueryDTO toQueryDTO(FuluOrderInfoGetDTO fuluOrderInfoGetDTO);

    @Named("mapToBigDecimal")
    default BigDecimal mapToBigDecimal(String price) {
        return new BigDecimal(price).setScale(2, RoundingMode.HALF_UP);
    }

    @Named("mapStringToInt")
    default Integer mapStringToInt(String value) {
        return Integer.valueOf(value);
    }

    @Named("mapTime")
    default LocalDateTime mapTime(String chargeFinishTime) {
        if (Objects.isNull(chargeFinishTime)){
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(chargeFinishTime, formatter);
    }

    @Named("mapUpstreamOrderStatus")
    default Integer mapUpstreamOrderStatus(String orderState) {
        return switch (orderState) {
            case "success" -> 0;
            case "processing" -> 1;
            case "failed" -> 2;
            case "untreated" -> 3;
            default -> throw exception(FULU_RESPONSE_ORDER_STATUS_ERROR);
        };
    }
}
