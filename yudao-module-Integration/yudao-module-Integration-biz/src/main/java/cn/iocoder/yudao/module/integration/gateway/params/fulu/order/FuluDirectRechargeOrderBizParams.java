package cn.iocoder.yudao.module.integration.gateway.params.fulu.order;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import static cn.iocoder.yudao.module.integration.enums.FuluMethodType.ORDER_DIRECT_ADD;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluDirectRechargeOrderBizParams implements FuluApiBizParams {

    @Schema(description = "商品编号", example = "********", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("product_id")
    private Long productId;

    @Schema(description = "外部订单号", example = "201906281030191013526", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer_order_no")
    private String customerOrderNo;

    @Schema(description = "充值账号", example = "888888", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("charge_account")
    private String chargeAccount;

    @Schema(description = "购买数量", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("buy_num")
    private Integer buyNum;

    @Schema(description = "充值游戏名称", example = "三国群英传", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_game_name")
    private String chargeGameName;

    @Schema(description = "充值游戏区", example = "电信一区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_game_region")
    private String chargeGameRegion;

    @Schema(description = "充值游戏服", example = "逐鹿中原", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_game_srv")
    private String chargeGameSrv;

    @Schema(description = "充值类型", example = "Q币", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_type")
    private String chargeType;

    @Schema(description = "充值密码", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_password")
    private String chargePassword;

    @Schema(description = "下单真实Ip", example = "*************", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_ip")
    private String chargeIp;

    @Schema(description = "联系QQ", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("contact_qq")
    private String contactQq;

    @Schema(description = "联系电话", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("contact_tel")
    private String contactTel;

    @Schema(description = "剩余数量", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("remaining_number")
    private Integer remainingNumber;

    @Schema(description = "充值游戏角色", example = "赵云", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("charge_game_role")
    private String chargeGameRole;

    @Schema(description = "外部销售价", example = "1.00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("customer_price")
    private Double customerPrice;

    @Schema(description = "店铺类型", example = "淘宝", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("shop_type")
    private String shopType;

    @Schema(description = "透传字段", example = "C564982164", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("external_biz_id")
    private String externalBizId;

    @Override
    public FuluMethodType getMethodType() {
        return ORDER_DIRECT_ADD;
    }

    public String getLogContent() {
        return "上游直充接口订单参数:\n" +
                "productId=" + productId + "\n" +
                "customerOrderNo='" + customerOrderNo + "'\n" +
                "chargeAccount='" + chargeAccount + "'\n" +
                "buyNum=" + buyNum + "\n" +
                "chargeGameName='" + chargeGameName + "'\n" +
                "chargeGameRegion='" + chargeGameRegion + "'\n" +
                "chargeGameSrv='" + chargeGameSrv + "'\n" +
                "chargeType='" + chargeType + "'\n" +
                "chargePassword='" + chargePassword + "'\n" +
                "chargeIp='" + chargeIp + "'\n" +
                "contactQq='" + contactQq + "'\n" +
                "contactTel='" + contactTel + "'\n" +
                "remainingNumber=" + remainingNumber + "\n" +
                "chargeGameRole='" + chargeGameRole + "'\n" +
                "customerPrice=" + customerPrice + "\n" +
                "shopType='" + shopType + "'\n" +
                "externalBizId='" + externalBizId + "'";
    }
}
