package cn.iocoder.yudao.module.integration.gateway.dto.changsheng;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangshengUserInfoGetDTO {
    @JsonProperty("name")
    private String name;

    @JsonProperty("balance")
    private BigDecimal balance;

    // 1-启用 0-禁用
    @JsonProperty("is_open")
    private Integer isOpen;
}
