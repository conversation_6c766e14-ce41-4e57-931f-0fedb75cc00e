package cn.iocoder.yudao.module.integration.gateway.response.yiqida;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YiqidaGoodListResponse {
    private Integer code;
    private String msg;
    private List<YiqidaGoodResponseData> data;
    private Integer sum;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class YiqidaGoodResponseData {
        /**
         * 主产品Id
         */
        private Long mainId;

        /**
         * 规格产品Id
         */
        private Long branchId;

        /**
         * 规格产品名称
         */
        private String branchName;

        /**
         * 1非活动商品 2活动商品
         */
        private Integer isPreSale;

        /**
         * 折扣
         */
        private Double discount;

        /**
         * 活动开始时间，非活动商品为null
         */
        private String preSaleSTime;

        /**
         * 活动结束时间，非活动商品为null
         */
        private String preSaleETime;

        /**
         * 预售描述
         */
        private String preSaleDesc;

        /**
         * 结束时间
         */
        private String endTime;

        /**
         * 3.官方直充  6卡密
         */
        private Integer virtualDelivery;

        /**
         * 规格图片
         */
        private String branchImg;

        /**
         * 1上架 2下架 3删除 4维护
         */
        private Integer status;

        /**
         * 库存数量
         */
        private Integer stockAmount;

        /**
         * 销量
         */
        private Integer salesCount;

        /**
         * 商品名称
         */
        private String name;

        /**
         * 充值模板
         */
        private String template;

        /**
         * 主产品图
         */
        private String mainImg;

        /**
         * 结算价格
         */
        private Double price;

        /**
         * 面值
         */
        private Double guidePrice;

        /**
         * 单个订单最大购买数量
         */
        private Integer limitCountMax;

        /**
         * 单个订单最小购买限制数量
         */
        private Integer limitCountMin;

        /**
         * 充值类型 1.厂家直冲 2.卖家直发 3.官方直充 4.扫码直充 5.卖家代充 6.卡密
         */
        private Integer delivery;

        /**
         * 带票税费
         */
        private Double taxationPrice;

        /**
         * 目录Id
         */
        private Integer catalogId;

        /**
         * 目录名称
         */
        private String catalogName;
    }
}
