package cn.iocoder.yudao.module.integration.gateway.response.wenyue;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 问月直充XML响应类
 * 处理问月直充接口返回的XML格式数据
 */
@Data
@JacksonXmlRootElement(localName = "order")
@Schema(description = "问月直充XML响应")
public class WenYueXmlResponse {

    @Schema(description = "用户ID", example = "XS090428000003")
    @JacksonXmlProperty(localName = "userid")
    private String userId;

    @Schema(description = "余额", example = "XS090428000003")
    @JacksonXmlProperty(localName = "balance")
    private BigDecimal balance;

    @Schema(description = "订单号", example = "XS090428000003")
    @JacksonXmlProperty(localName = "orderid")
    private String orderId;

    @Schema(description = "产品ID", example = "3312")
    @JacksonXmlProperty(localName = "productid")
    private String productId;

    @Schema(description = "数量", example = "1")
    @JacksonXmlProperty(localName = "num")
    private Integer num;

    @Schema(description = "订单金额", example = "98.5")
    @JacksonXmlProperty(localName = "ordercash")
    private BigDecimal orderCash;

    @Schema(description = "产品名称", example = "游戏直充")
    @JacksonXmlProperty(localName = "productname")
    private String productName;

    @Schema(description = "商户订单号", example = "2009042800001")
    @JacksonXmlProperty(localName = "sporderid")
    private String spOrderId;

    @Schema(description = "充值", example = "13590101510")
    @JacksonXmlProperty(localName = "mobile")
    private String mobile;

    @Schema(description = "结果码", example = "0")
    @JacksonXmlProperty(localName = "resultno")
    private String resultNo;

    @Schema(description = "余额", example = "12")
    @JacksonXmlProperty(localName = "fundbalance")
    private String fundBalance;

    @Schema(description = "余额", example = "12")
    @JacksonXmlProperty(localName = "merchantsubmittime")
    private String merchantSubmitTime;

    @Schema(description = "remark1", example = "12")
    @JacksonXmlProperty(localName = "remark1")
    private String remark1;

    @JsonIgnore
    public String getLogContent() {
        return "问月直充XML响应:\n" +
                "userid='" + userId + "'\n" +
                "balance='" + balance + "'\n" +
                "orderid='" + orderId + "'\n" +
                "productid='" + productId + "'\n" +
                "num=" + num + "\n" +
                "ordercash=" + orderCash + "\n" +
                "productname='" + productName + "'\n" +
                "merchantsubmittime='" + merchantSubmitTime + "'\n" +
                "sporderid='" + spOrderId + "'\n" +
                "mobile='" + mobile + "'\n" +
                "fundbalance='" + fundBalance + "'\n" +
                "remark1='" + remark1 + "'\n" +
                "resultno='" + resultNo + "'\n";
    }
}
