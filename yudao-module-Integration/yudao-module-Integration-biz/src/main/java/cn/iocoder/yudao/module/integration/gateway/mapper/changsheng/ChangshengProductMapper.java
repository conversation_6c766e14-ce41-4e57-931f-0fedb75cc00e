package cn.iocoder.yudao.module.integration.gateway.mapper.changsheng;

import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengProductInfoDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengProductListItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_RESPONSE_GOOD_SALES_STATUS_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_RESPONSE_GOOD_TYPE_ERROR;

@Mapper
public interface ChangshengProductMapper {
    ChangshengProductMapper MAPPER = Mappers.getMapper(ChangshengProductMapper.class);

    @Mappings({
            @Mapping(source = "productId", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "productName", target = "name"),
            @Mapping(source = "productType", target = "type", qualifiedByName = "changshengProductTypeToPlatformType"),
            @Mapping(source = "faceValue", target = "guidePrice", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "purchasePrice", target = "price", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "status", target = "status", qualifiedByName = "mapStatus")
    })
    SupplierProductDTO toPlatformProduct(ChangshengProductListItemDTO changshengProductListItemDTO);

    @Mappings({
            @Mapping(source = "productId", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "productName", target = "name"),
            @Mapping(source = "productType", target = "type", qualifiedByName = "changshengProductTypeToPlatformType"),
            @Mapping(source = "faceValue", target = "guidePrice", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "purchasePrice", target = "price", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "status", target = "status", qualifiedByName = "mapStatus"),
    })
    SupplierProductDTO toPlatformProduct(ChangshengProductInfoDTO changshengProductInfoDTO);

    @Named("longToString")
    default String longToString(Long value) {
        return String.valueOf(value);
    }

    @Named("mapStatus")
    default Integer mapStatus(String status) {
        return switch (status) {
            case "上架", "正常" -> 1;
            case "下架", "停用" -> 2;
            default -> throw exception(CHANGSHENG_RESPONSE_GOOD_SALES_STATUS_ERROR, "传入的销售状态为: " + status);
        };
    }

    @Named("changshengProductTypeToPlatformType")
    default Integer changshengProductTypeToPlatformType(String productType) {
        if (productType.equals("直充")) {
            return 1;
        } else if (productType.equals("卡密")) {
            return 2;
        } else {
            throw exception(CHANGSHENG_RESPONSE_GOOD_TYPE_ERROR);
        }
    }

    @Named("doubleToBigDecimal")
    default BigDecimal doubleToBigDecimal(Double value) {
        return BigDecimal.valueOf(value);
    }
}
