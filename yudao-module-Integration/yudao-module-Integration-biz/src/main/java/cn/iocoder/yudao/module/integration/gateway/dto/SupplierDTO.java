package cn.iocoder.yudao.module.integration.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierDTO {
    private Long id;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 用户名
     */
    private String username;

    /**
     * 编号
     */
    private String serialNumber;

    /**
     * 主营类型
     */
    private String mainBusinessType;

    /**
     * 渠道帐号
     */
    private String channelAccount;

    /*
     * 供应商接口URL
     */
    private String url;

    /**
     * 帐号密钥
     */
    private String accountKey;

    /**
     * 签名密钥
     */
    private String signingKey;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 供应商接口类名
     */
    private String interfaceClassName;

    /**
     * 接口类型 1-异步 2-同步
     */
    private Integer interfaceType;
}
