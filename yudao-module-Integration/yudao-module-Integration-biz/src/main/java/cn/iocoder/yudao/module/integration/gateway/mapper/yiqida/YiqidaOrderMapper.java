package cn.iocoder.yudao.module.integration.gateway.mapper.yiqida;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.params.yiqida.YiqidaRechargeRequest;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaQueryResponse;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaRechargeResponse;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_ORDER_STATUS_ERROR;

@Mapper
public interface YiqidaOrderMapper {
    YiqidaOrderMapper MAPPER = Mappers.getMapper(YiqidaOrderMapper.class);

    @Mappings({
            @Mapping(target = "commodityId", source = "productId"),
            @Mapping(target = "externalOrderNo", source = "upstreamOrderNo"),
            @Mapping(target = "buyCount", source = "quantity"),
    })
    YiqidaRechargeRequest toRequest(DirectRechargeOrderDTO directRechargeOrderDTO);

    @AfterMapping
    default void afterMapping(@MappingTarget YiqidaRechargeRequest request,
                              DirectRechargeOrderDTO directRechargeOrderDTO) {
        List<String> templates = new ArrayList<>();
        String template = directRechargeOrderDTO.getRechargeAccount();
        templates.add(template);
        request.setTemplate(templates);
    }

    @Mappings({
            @Mapping(source = "orderNo", target = "externalOrderNo"),
            @Mapping(source = "name", target = "upstreamProductName"),
    })
    DirectChargeResultDTO toResult(YiqidaRechargeResponse.OrderData data);

    @Mappings({
            @Mapping(source = "orderNo", target = "externalOrderNo"),
    })
    DirectChargeResultDTO toResult(YiqidaQueryResponse.QueryData data);

    @Mappings({
            @Mapping(source = "sendTime", target = "completeTime", qualifiedByName = "mapTime"),
            @Mapping(source = "status", target = "upstreamOrderStatus", qualifiedByName = "mapUpstreamOrderStatus"),
    })
    UpstreamOrderQueryDTO toQueryDTO(YiqidaQueryResponse.QueryData data);

    @Named("mapTime")
    default LocalDateTime mapTime(String chargeFinishTime) {
        if (Objects.isNull(chargeFinishTime)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(chargeFinishTime, formatter);
    }

    @Named("mapUpstreamOrderStatus")
    default Integer mapUpstreamOrderStatus(Integer status) {
        //订单状态（0-充值成功/1-充值中/2-充值失败/3-未处理）
        return switch (status) {
            case 4 -> 0;
            case 5 -> 2;
            case 6, 1, 2, 3 -> 1;
            default -> throw exception(YIQIDA_RESPONSE_ORDER_STATUS_ERROR);
        };
    }
}
