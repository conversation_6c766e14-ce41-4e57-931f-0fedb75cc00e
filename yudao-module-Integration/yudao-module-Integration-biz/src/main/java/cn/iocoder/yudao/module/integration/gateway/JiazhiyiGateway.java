package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.client.jiazhiyi.JiazhiyiFeignClient;
import cn.iocoder.yudao.module.integration.client.jiazhiyi.JiazhiyiFeignClientFactory;
import cn.iocoder.yudao.module.integration.enums.JiazhiyiMethodType;
import cn.iocoder.yudao.module.integration.gateway.config.JiazhiyiConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.mapper.jiazhiyi.JiazhiyiOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.params.jiazhiyi.JiazhiyiDirectRechargeRequest;
import cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi.JiazhiyiBalanceResponse;
import cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi.JiazhiyiOrderResponse;
import cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi.JiazhiyiRechargeResponse;
import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JIAZHIYI_CALL_BACK_BALANCE_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JIAZHIYI_CALL_BACK_ORDER_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JIAZHIYI_NOT_SUPPORT_PRODUCT_QUERY;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JIAZHIYI_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class JiazhiyiGateway implements SupplierGateway {

    private final JiazhiyiConfigurationProperties jiazhiyiConfigurationProperties;

    private final JiazhiyiFeignClientFactory feignClientFactory;

    private final IntegrationOrderLogService integrationOrderLogService;

    @Override
    public String getSupplierInterfaceClassName() {
        return jiazhiyiConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        throw exception(JIAZHIYI_NOT_SUPPORT_PRODUCT_QUERY);
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        return null;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入佳之易直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        JiazhiyiDirectRechargeRequest jiazhiyiDirectRechargeRequest = JiazhiyiOrderMapper.MAPPER.toRechargeRequest(directRechargeOrderDTO);
        jiazhiyiDirectRechargeRequest.setBusinessId(supplierDTO.getChannelAccount());
        jiazhiyiDirectRechargeRequest.setNoticeUrl(jiazhiyiConfigurationProperties.getCallbackUrl());
        jiazhiyiDirectRechargeRequest.generateAndSetSign(supplierDTO.getSigningKey());
        String requestParams = jiazhiyiDirectRechargeRequest.buildRequestParams();
        log.info("佳之易直充接口，requestParams为:[{}]", requestParams);
        JiazhiyiFeignClient client = feignClientFactory.createClient("https://supi.900sup.cn/Service/" + JiazhiyiMethodType.ORDER_DIRECT_ADD.getMethodUri() + requestParams);
        JiazhiyiRechargeResponse jiazhiyiRechargeResponse;
        try {
            jiazhiyiRechargeResponse = client.recharge();
            log.info("佳之易直充调用接口结果为:[{}]", jiazhiyiRechargeResponse);
        } catch (Exception e) {
            log.error("佳之易直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(JIAZHIYI_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
        if (!Objects.equals(jiazhiyiRechargeResponse.getResult(), "01")) {
            log.error("佳之易直充调用接口结果异常:[{}]", jiazhiyiRechargeResponse);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", requestParams, jiazhiyiRechargeResponse));
            throw exception(JIAZHIYI_RESPONSE_RECHARGE_ORDER_ERROR, jiazhiyiRechargeResponse.getMessage());
        }
        DirectChargeResultDTO directChargeResultDTO = DirectChargeResultDTO.builder()
                .upstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo())
                .externalOrderNo(directRechargeOrderDTO.getUpstreamOrderNo()) // 佳之易不返回自身订单号
                .supplierId(supplierDTO.getId())
                .build();
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", requestParams, jiazhiyiRechargeResponse));
        return directChargeResultDTO;
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        String sign = generateSign(supplierDTO.getChannelAccount(), upstreamOrderNo, supplierDTO.getSigningKey());
        String requestParams = String.format("?businessId=%s&userOrderId=%s&sign=%s", supplierDTO.getChannelAccount(), upstreamOrderNo, sign);
        JiazhiyiFeignClient client = feignClientFactory.createClient("https://supq.900sup.cn/Service" + JiazhiyiMethodType.QUERY_ORDER.getMethodUri() + requestParams);
        JiazhiyiOrderResponse jiazhiyiOrderResponse;
        try {
            jiazhiyiOrderResponse = client.orderQuery();
            log.info("佳之易查询订单结果为:[{}]", jiazhiyiOrderResponse);
        } catch (Exception e) {
            log.error("佳之易查询订单异常", e);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, ExceptionUtils.getMessage(e) + e);
            throw exception(JIAZHIYI_CALL_BACK_ORDER_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        List<String> normalStatusValues = Arrays.asList("01", "02", "03", "04");
        List<String> errorStatusValues = Arrays.asList("05", "06", "07");
        if (errorStatusValues.contains(jiazhiyiOrderResponse.getStatus())) {
            log.error("佳之易查询订单结果异常:[{}]", jiazhiyiOrderResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, jiazhiyiOrderResponse.getMessage());
            throw exception(JIAZHIYI_CALL_BACK_ORDER_QUERY_ERROR, jiazhiyiOrderResponse.getMessage());
        } else if (!normalStatusValues.contains(jiazhiyiOrderResponse.getStatus()) && !errorStatusValues.contains(jiazhiyiOrderResponse.getStatus())) {
            log.error("佳之易查询订单结果不在文档范围内:[{}]", jiazhiyiOrderResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, jiazhiyiOrderResponse.toString());
            throw exception(JIAZHIYI_CALL_BACK_ORDER_QUERY_ERROR, jiazhiyiOrderResponse.getMessage(), jiazhiyiOrderResponse);
        }
        UpstreamOrderQueryDTO queryDTO = JiazhiyiOrderMapper.MAPPER.toQueryDTO(jiazhiyiOrderResponse);
        queryDTO.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS,
                String.format("request:[%s], response:[%s]", requestParams, jiazhiyiOrderResponse));
        return queryDTO;
    }

    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        String sign = generateSign(supplierDTO.getChannelAccount(), supplierDTO.getSigningKey());
        String requestParams = String.format("?businessId=%s&sign=%s", supplierDTO.getChannelAccount(), sign);
        JiazhiyiFeignClient client = feignClientFactory.createClient("http://supm.900sup.cn/Service" + JiazhiyiMethodType.BALANCE_QUERY.getMethodUri() + requestParams);
        JiazhiyiBalanceResponse jiazhiyiBalanceResponse;
        try {
            jiazhiyiBalanceResponse = client.balanceQuery();
            log.info("佳之易查询余额结果BalanceQueryResponse:[{}]", jiazhiyiBalanceResponse);
        } catch (Exception e) {
            log.error("佳之易查询余额异常", e);
            throw exception(JIAZHIYI_CALL_BACK_BALANCE_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (jiazhiyiBalanceResponse.getStatus() != 1) {
            log.error("佳之易查询余额结果异常:[{}]", jiazhiyiBalanceResponse);
            throw exception(JIAZHIYI_CALL_BACK_BALANCE_QUERY_ERROR, jiazhiyiBalanceResponse.getMessage());
        }
        SupplierBalanceDTO result = SupplierBalanceDTO.builder()
                .balance(jiazhiyiBalanceResponse.getBalance())
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .serialNumber(supplierDTO.getSerialNumber())
                .channelAccount(supplierDTO.getChannelAccount())
                .build();
        log.info("佳之易查询余额结果result:[{}]", result);
        return result;
    }

    private String generateSign(String... params) {
        return MD5Utils.MD5(String.join("", params));
    }

}
