package cn.iocoder.yudao.module.integration.gateway.dto.changsheng;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangshengDirectRechargeResultDTO {
    @Schema(description = "订单编号", example = "19062837751058701652", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("order_id")
    private String orderId;

    @Schema(description = "外部订单号，每次请求必须唯一", example = "201906281030191013526", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer_order_no")
    private String customerOrderNo;

    @Schema(description = "商品Id", example = "********", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("product_id")
    private Integer productId;

    @Schema(description = "商品名称", example = "腾讯Q币直充一元", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("product_name")
    private String productName;

    @Schema(description = "充值账号", example = "888888", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("charge_account")
    private String chargeAccount;

    @Schema(description = "购买数量", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("buy_num")
    private Integer buyNum;

    @Schema(description = "订单类型：1-话费 2-流量 3-卡密 4-直充", example = "4", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("order_type")
    private Integer orderType;

    @Schema(description = "交易单价", example = "1.1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("order_price")
    private Double orderPrice;

    @Schema(description = "订单状态： （success：成功，processing：处理中，failed：失败，untreated：未处理）",
            example = "untreated", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("order_state")
    private String orderState;

    @Schema(description = "创建时间", example = "2019-07-27 16:44:30", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("create_time")
    private String createTime;

    @Schema(description = "订单完成时间，查单接口返回", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("finish_time")
    private String finishTime;

    @Schema(description = "充值区（中文）", example = "电信一区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("area")
    private String area;

    @Schema(description = "充值服（中文）", example = "逐鹿中原", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("server")
    private String server;

    @Schema(description = "计费方式（中文）", example = "Q币", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("type")
    private String type;

    @Schema(description = "运营商流水号", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("operator_serial_number")
    private String operatorSerialNumber;
}
