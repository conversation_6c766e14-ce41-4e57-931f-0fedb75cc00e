package cn.iocoder.yudao.module.integration.gateway.params.aiqiyi;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiqiyiApiResponse {
    @Schema(name = "返回码", requiredMode = Schema.RequiredMode.REQUIRED, example = "处理结果：0—成功,其他失败")
    private Integer result;

    @Schema(name = "返回信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private String msg;

    public String getLogContent() {
        return "爱奇艺充值接口返回\n" +
                "，result：" + result + "\n"
                + "，msg：" + msg;
    }
}
