package cn.iocoder.yudao.module.integration.gateway.params.fulu.goods;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluGoodsTemplateBizParams implements FuluApiBizParams {

    @Schema(description = "商品模板编号", example = "70bb5598-1aef-422e-86ef-a68ae6de79e8", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 50)
    @JsonProperty("template_id")
    private String templateId;

    @Override
    public FuluMethodType getMethodType() {
        return FuluMethodType.GOOD_TEMPLATE;
    }
}
