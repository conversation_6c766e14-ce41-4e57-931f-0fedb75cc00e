package cn.iocoder.yudao.module.integration.service;

import lombok.Getter;

@Getter
public enum OrderLogType {
    MAIN_ORDER_CREATED(0, "主订单创建成功"),
    MAIN_ORDER_FAILED(1, "主订单创建失败"),
    MAIN_ORDER_RECHARGE_SUCCESS(2, "主订单充值成功"),
    MAIN_ORDER_RECHARGE_FAILED(3, "主订单充值失败"),
    UPSTREAM_ORDER_CREATED(5, "上游订单创建成功"),
    UPSTREAM_ORDER_CREATE_FAILED(6, "上游订单创建成功，状态为失败"),
    UPSTREAM_ORDER_RECHARGE_SUCCESS(7, "上游订单充值成功"),
    Upstream_ORDER_RECHARGE_FAILED(8, "上游订单充值失败"),
    UPSTREAM_ORDER_CALL_BACK_SUCCESS(9, "上游平台回调成功"),
    UPSTREAM_ORDER_CALL_SUCCESS(10, "上游直充接口调用成功"),
    UPSTREAM_ORDER_CALL_FAILED(11, "上游直充接口调用失败"),
    DOWNSTREAM_NOTIFICATION_SUCCESS(110, "通知下游成功"),
    DOWNSTREAM_NOTIFICATION_FAILED(111, "通知下游失败"),
    DOWNSTREAM_FORCE_FAILED(112, "强制上游订单失败"),
    DOWNSTREAM_FORCE_SUCCESS(113, "强制上游订单成功"),
    ORDER_REFUND_SUCCESS(114, "订单退款成功"),
    ORDER_REORDER(115, "重新下单上游订单"),
    UPSTREAM_PRODUCT_STATUS_ERROR(116, "上游供应商商品状态不为上架"),
    UPSTREAM_PRODUCT_SEARCH_ERROR(117, "上游供应商商品查询失败"),
    UPSTREAM_PRODUCT_SEARCH_SUCCESS(118, "上游供应商商品查询成功"),
    UPSTREAM_ORDER_CALL_BACK_FAILED(119, "上游平台回调失败"),
    UPSTREAM_ORDER_QUERY_FAILED(120, "上游订单查询失败"),
    UPSTREAM_ORDER_QUERY_SUCCESS(121, "上游订单查询成功"),
    MAIN_ORDER_FORCE_SUCCESS(122, "主订单强制失败"),
    MAIN_ORDER_FORCE_FAILED(123, "主订单强制成功"),
    LOOP_INIT_FAILED(124, "处理轮询失败"),
    LOOP_SUCCESS(125, "轮询成功，找到合适的供应商商品"),
    ORDER_AUTO_REFUND_FAILED_WITH_EMPTY_MERCHANT(126, "商户查找异常， 订单自动退款失败"),
    LOOP_SKIP_NEGATIVE_PROFIT(127, "商品不允许负利润，跳过轮询");

    private final Integer code;
    private final String desc;

    OrderLogType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
