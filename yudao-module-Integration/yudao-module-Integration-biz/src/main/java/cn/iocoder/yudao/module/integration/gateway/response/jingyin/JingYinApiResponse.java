package cn.iocoder.yudao.module.integration.gateway.response.jingyin;

import cn.iocoder.yudao.module.integration.gateway.dto.jingyin.JingYinResultDTO;
import cn.iocoder.yudao.module.integration.gateway.response.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class JingYinApiResponse extends BaseResponse {
    private String resultCode;
    private String resultMsg;
    private JingYinResultDTO data;

    public String getLogContent() {
        return "上游直充接口响应为：\n" +
                "resultCode='" + resultCode + "'\n" +
                "resultMsg='" + resultMsg + "'\n" +
                "data=" + (data != null ? data.getLogContent() : "null");
    }
}
