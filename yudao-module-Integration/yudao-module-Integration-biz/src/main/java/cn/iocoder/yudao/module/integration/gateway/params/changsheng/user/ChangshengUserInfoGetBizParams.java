package cn.iocoder.yudao.module.integration.gateway.params.changsheng.user;

import cn.iocoder.yudao.module.integration.enums.ChangshengMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.ChangshengApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChangshengUserInfoGetBizParams implements ChangshengApiBizParams {
    @Override
    public ChangshengMethodType getMethodType() {
        return ChangshengMethodType.USER_INFO_QUERY;
    }
}
