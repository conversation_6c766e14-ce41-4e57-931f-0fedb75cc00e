package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.client.wenyue.WenYueFeignClient;
import cn.iocoder.yudao.module.integration.client.wenyue.WenYueFeignClientFactory;
import cn.iocoder.yudao.module.integration.gateway.config.WenYueConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.*;
import cn.iocoder.yudao.module.integration.gateway.mapper.wenyue.WenYueOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.params.wenyue.WenYueApiBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.wenyue.WenYueBalanceQueryBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.wenyue.WenYueDirectRechargeOrderBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.wenyue.WenYueQueryBizParams;
import cn.iocoder.yudao.module.integration.gateway.response.wenyue.WenYueXmlResponse;
import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class WenYueGateway implements SupplierGateway {
    private final WenYueConfigurationProperties wenYueConfigurationProperties;
    private final WenYueFeignClientFactory wenYueFeignClientFactory;
    private final IntegrationOrderLogService integrationOrderLogService;

    @Override
    public String getSupplierInterfaceClassName() {
        return wenYueConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        return List.of();
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        return null;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入问月直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        WenYueDirectRechargeOrderBizParams bizParams = buildDirectChargeParams(supplierDTO, directRechargeOrderDTO);
        WenYueXmlResponse response = callWenYueOrderApi(supplierDTO, orderNo, bizParams);

        if (response == null || response.getResultNo() == null) {
            log.error("问月直充接口返回结果为空");
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response: [%s]，上游返回结果为空，直接设置状态订单为充值中，请通过订单号查询订单结果",
                            bizParams, response == null ? "null" : response.getLogContent()));
            return buildDefaultDirectChargeResult(directRechargeOrderDTO);
        }

        if ("5006".equals(response.getResultNo())) {
            log.error("问月直充接口返回结果异常：5006，代理商订单号重复");
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response: [%s]，订单重复，请通过订单号查询订单结果", bizParams, response.getLogContent()));
            return buildDefaultDirectChargeResult(directRechargeOrderDTO);
        }

        if ("9999".equals(response.getResultNo())) {
            log.error("问月直充接口返回结果异常：9999，未知错误，提交平台技术判定");
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response: [%s]，未知错误，提交平台技术判定", bizParams, response.getLogContent()));
            return buildDefaultDirectChargeResult(directRechargeOrderDTO);
        }

        // 0表示等待充值，2表示充值中，其他表示失败，5006，9999和其他需要人工处理订单
        if (!Set.of("0", "2").contains(response.getResultNo())) {
            log.error("问月直充调用接口结果异常:[{}]", response);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", bizParams.getLogContent(), response.getLogContent()));
            throw exception(WENYUE_RESPONSE_RECHARGE_ORDER_ERROR, response.getResultNo());
        }

        DirectChargeResultDTO result = WenYueOrderMapper.MAPPER.toResultFromXml(response);
        result.setSupplierId(supplierDTO.getId());
        log.info("问月直充调用返回的DirectChargeResultDTO为:[{}]", result);

        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", bizParams.getLogContent(), response.getLogContent()));

        return result;
    }

    private WenYueXmlResponse callWenYueOrderApi(SupplierDTO supplierDTO, String orderNo, WenYueApiBizParams bizParams) {
        try {
            WenYueFeignClient client = wenYueFeignClientFactory.createClient(supplierDTO.getUrl() + bizParams.getMethodType().getMethodUri());
            WenYueXmlResponse response = client.postRequest(bizParams);
            log.info("问月直充调用接口结果为:[{}]", response);
            return response;
        } catch (Exception e) {
            log.error("问月直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(WENYUE_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
    }

    private DirectChargeResultDTO buildDefaultDirectChargeResult(DirectRechargeOrderDTO directRechargeOrderDTO) {
        return DirectChargeResultDTO.builder()
                .upstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo())
                .upstreamOrderStatus(1) // 1表示充值中
                .externalOrderNo("default-external-order-no") // 默认外部订单号
                .build();
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        String sign = generateWenYueOrderQuerySign(supplierDTO.getChannelAccount(), upstreamOrderNo, supplierDTO.getSigningKey());

        WenYueQueryBizParams bizParams = WenYueQueryBizParams.builder()
                .userId(supplierDTO.getChannelAccount())
                .sporderId(upstreamOrderNo)
                .sign(sign)
                .build();

        WenYueXmlResponse response = callWenYueOrderApi(supplierDTO, orderNo, bizParams);

        if (response == null || response.getResultNo() == null) {
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED,
                    String.format("request:[%s], response:[%s], 接口返回结果为空, 请联系上游进行排查",
                            bizParams, response == null ? "null" : response.getLogContent()));
            throw exception(WENYUE_ORDER_QUERY_ERROR, "接口返回结果为空, 请联系上游进行排查");
        }

        UpstreamOrderQueryDTO queryDTO = UpstreamOrderQueryDTO.builder()
                .upstreamOrderNo(response.getSpOrderId())
                .externalOrderNo(response.getOrderId())
                .upstreamOrderStatus(determineOrderStatus(orderNo, orderCreateTime, bizParams, response))
                .supplierId(supplierDTO.getId())
                .build();

        integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS,
                String.format("request:[%s], response:[%s]", bizParams, response.getLogContent()));

        return queryDTO;
    }

    private int determineOrderStatus(String orderNo, LocalDateTime orderCreateTime,
                                     WenYueQueryBizParams bizParams, WenYueXmlResponse response) {
        // 5007表示查询不到订单
        if ("5007".equals(response.getResultNo())) {
            Duration duration = Duration.between(orderCreateTime, LocalDateTime.now());
            if (duration.toMinutes() > 10) {
                log.warn("订单[{}]创建时间超过10分钟，但是订单查询不到，设置为失败状态", orderNo);
                integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED,
                        String.format("request:[%s], response:[%s], 订单创建时间超过10分钟,但是上游查询不到改订单,设置为失败状态",
                                bizParams, response.getLogContent()));
                return 2;
            }
            return 1;
        }

        // 其他状态码：1表示充值成功，9表示充值失败
        return "1".equals(response.getResultNo()) ? 0 : "9".equals(response.getResultNo()) ? 2 : 1;
    }


    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        String sign = generateWenYueBalanceSign(supplierDTO.getChannelAccount(), supplierDTO.getSigningKey());
        WenYueBalanceQueryBizParams bizParams = WenYueBalanceQueryBizParams.builder()
                .userId(supplierDTO.getChannelAccount())
                .sign(sign)
                .build();

        WenYueXmlResponse response;
        try {
            WenYueFeignClient client = wenYueFeignClientFactory.createClient(supplierDTO.getUrl() + bizParams.getMethodType().getMethodUri());
            response = client.postRequest(bizParams);
            log.info("查询余额 response 查询结果为:[{}]", response);
        } catch (Exception e) {
            log.error("问月查询余额结果异常", e);
            throw exception(WENYUE_USER_INFO_QUERY_ERROR, e);
        }

        if (response == null || !"1".equals(response.getResultNo())) {
            log.error("问月查询余额结果异常:[{}]", response);
            throw exception(WENYUE_USER_INFO_QUERY_ERROR, response != null ? "查询失败，结果码：" + response.getResultNo() : "接口返回结果为空，请联系上游进行排查");
        }

        log.info("问月查询到的余额结果为:[{}]", response);
        return SupplierBalanceDTO.builder()
                .balance(response.getBalance())
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .channelAccount(supplierDTO.getChannelAccount())
                .serialNumber(supplierDTO.getSerialNumber())
                .build();
    }

    private WenYueDirectRechargeOrderBizParams buildDirectChargeParams(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO) {
        WenYueDirectRechargeOrderBizParams bizParams = WenYueOrderMapper.MAPPER.toParams(directRechargeOrderDTO);
        bizParams.setUserId(supplierDTO.getChannelAccount());
        bizParams.setSporderTime(getTimeStamp());
        bizParams.setBackUrl(wenYueConfigurationProperties.getCallbackUrl());
        bizParams.setSign(generateWenYueDirectChargeSign(bizParams, supplierDTO.getSigningKey()));
        return bizParams;
    }

    private String getTimeStamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    private String generateWenYueBalanceSign(String userId, String signKey) {
        String signString = "userid=" + userId + "&key=" + signKey;
        return MD5Utils.MD5(signString).toUpperCase();
    }

    public static String generateWenYueDirectChargeSign(WenYueDirectRechargeOrderBizParams bizParams, String signKey) {
        String signString = "userid=" + bizParams.getUserId()
                + "&productid=" + bizParams.getProductId()
                + "&num=1"
                + "&areaid=" + (bizParams.getAreaId() != null ? bizParams.getAreaId() : "")
                + "&serverid=" + (bizParams.getServerId() != null ? bizParams.getServerId() : "")
                + "&account=" + bizParams.getAccount()
                + "&spordertime=" + bizParams.getSporderTime()
                + "&sporderid=" + bizParams.getSporderId()
                + "&key=" + signKey;

        return MD5Utils.MD5(signString).toUpperCase();
    }

    private String generateWenYueOrderQuerySign(String userId, String sporderId, String signKey) {
        String signString = "userid=" + userId
                + "&sporderid=" + sporderId
                + "&key=" + signKey;
        return MD5Utils.MD5(signString).toUpperCase();
    }
}
