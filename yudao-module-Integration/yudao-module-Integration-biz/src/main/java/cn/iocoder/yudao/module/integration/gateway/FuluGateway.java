package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.integration.client.fulu.FuluFeignClient;
import cn.iocoder.yudao.module.integration.client.fulu.FuluFeignClientFactory;
import cn.iocoder.yudao.module.integration.gateway.config.FuluConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluDirectRechargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluOrderInfoGetDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluProductInfoDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluProductListItemDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluUserInfoGetDTO;
import cn.iocoder.yudao.module.integration.gateway.mapper.fulu.FuluOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.mapper.fulu.FuluProductMapper;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBaseRequestBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.goods.FuluGoodInfoBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.goods.FuluGoodListBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.order.FuluDirectRechargeOrderBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.order.FuluOrderInfoGetBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.user.FuluUserInfoGetBizParams;
import cn.iocoder.yudao.module.integration.gateway.response.fulu.FuluApiResponse;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_CALL_BACK_ORDER_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_GATEWAY_SIGN_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_RESPONSE_RETURN_GOODS_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_RESPONSE_RETURN_GOOD_INFO_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_USER_INFO_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils.MD5;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class FuluGateway implements SupplierGateway {

    private final FuluFeignClientFactory fuluFeignClientFactory;

    private final FuluConfigurationProperties fuluConfigurationProperties;

    private final IntegrationOrderLogService integrationOrderLogService;


    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        FuluApiResponse fuluApiResponse;
        try {
            fuluApiResponse = postRequest(new FuluGoodListBizParams(), supplierDTO);
        } catch (Exception e) {
            log.error("福禄获取商品列表异常", e);
            throw exception(FULU_RESPONSE_RETURN_GOODS_FAILED, ExceptionUtils.getMessage(e));
        }
        if (fuluApiResponse.getCode() != 0) {
            log.error("福禄获取商品列表结果异常:[{}]", fuluApiResponse);
            throw exception(FULU_RESPONSE_RETURN_GOODS_FAILED, fuluApiResponse.getMessage());
        }
        List<FuluProductListItemDTO> fuluProductListItemDTOS = JsonUtils.parseObject(fuluApiResponse.getResult(), new TypeReference<>() {
        });
        return fuluProductListItemDTOS.stream()
                .map(FuluProductMapper.MAPPER::toPlatformProduct)
                .map(item -> item.setSupplierId(supplierDTO.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        FuluApiResponse fuluApiResponse;
        try {
            fuluApiResponse = postRequest(new FuluGoodInfoBizParams(String.valueOf(productId)), supplierDTO);
        } catch (Exception e) {
            log.error("福禄获取商品信息异常", e);
            throw exception(FULU_RESPONSE_RETURN_GOOD_INFO_FAILED, ExceptionUtils.getMessage(e));
        }
        if (fuluApiResponse.getCode() != 0) {
            log.error("福禄获取商品信息结果异常:[{}]", fuluApiResponse);
            throw exception(FULU_RESPONSE_RETURN_GOOD_INFO_FAILED, fuluApiResponse.getMessage());
        }
        FuluProductInfoDTO productInfoDTO = JsonUtils.parseObject(fuluApiResponse.getResult(), new TypeReference<>() {
        });
        SupplierProductDTO platformProduct = FuluProductMapper.MAPPER.toPlatformProduct(productInfoDTO);
        platformProduct.setSupplierId(supplierDTO.getId());
        return platformProduct;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入福禄直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        FuluDirectRechargeOrderBizParams fuluDirectRechargeOrderBizParams = FuluOrderMapper.MAPPER.toParams(directRechargeOrderDTO);
        FuluApiResponse fuluApiResponse;
        try {
            fuluApiResponse = postRequest(fuluDirectRechargeOrderBizParams, supplierDTO);
            log.info("福禄直充调用接口结果为:[{}]", fuluApiResponse);
        } catch (Exception e) {
            log.error("福禄直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(FULU_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
        if (fuluApiResponse.getCode() != 0) {
            log.error("福禄直充调用接口结果异常:[{}]", fuluApiResponse);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", fuluDirectRechargeOrderBizParams.getLogContent(), fuluApiResponse.getLogContent()));
            throw exception(FULU_RESPONSE_RECHARGE_ORDER_ERROR, fuluApiResponse.getMessage());
        }
        verifyResultSign(fuluApiResponse.getResult(), fuluApiResponse.getSign());
        FuluDirectRechargeResultDTO fuluDirectRechargeResultDTO = JsonUtils.parseObject(fuluApiResponse.getResult(), new TypeReference<>() {
        });
        DirectChargeResultDTO result = FuluOrderMapper.MAPPER.toResult(fuluDirectRechargeResultDTO);
        log.info("福禄直充调用接口结果为:[{}]", result);
        result.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", fuluDirectRechargeOrderBizParams.toString(), fuluApiResponse));
        return result;
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        FuluApiResponse fuluApiResponse;
        FuluOrderInfoGetBizParams fuluApiBizParams;
        try {
            fuluApiBizParams = FuluOrderInfoGetBizParams.builder().customerOrderNo(upstreamOrderNo).build();
            fuluApiResponse = postRequest(fuluApiBizParams, supplierDTO);
            log.info("查询订单fuluApiResponse 查询结果为:[{}]", fuluApiResponse);
        } catch (Exception e) {
            log.error("福禄查询订单异常", e);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, ExceptionUtils.getMessage(e) + e);
            throw exception(FULU_CALL_BACK_ORDER_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (fuluApiResponse.getCode() != 0) {
            log.error("福禄查询订单结果异常:[{}]", fuluApiResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, fuluApiResponse.getMessage());
            throw exception(FULU_CALL_BACK_ORDER_QUERY_ERROR, fuluApiResponse.getMessage());
        }
        FuluOrderInfoGetDTO fuluOrderInfoGetDTO = JsonUtils.parseObject(fuluApiResponse.getResult(), new TypeReference<>() {
        });
        log.info("福禄查询到的订单结果为:[{}]", fuluOrderInfoGetDTO);
        UpstreamOrderQueryDTO queryDTO = FuluOrderMapper.MAPPER.toQueryDTO(fuluOrderInfoGetDTO);
        queryDTO.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS,
                String.format("request:[%s], response:[%s]", fuluApiBizParams, fuluApiResponse));
        return queryDTO;
    }

    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        FuluApiResponse fuluApiResponse;
        try {
            fuluApiResponse = postRequest(FuluUserInfoGetBizParams.builder().build(), supplierDTO);
            log.info("查询余额fuluApiResponse 查询结果为:[{}]", fuluApiResponse);
        } catch (Exception e) {
            log.error("福禄用户信息查询异常", e);
            throw exception(FULU_USER_INFO_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        FuluUserInfoGetDTO fuluUserInfoGetDTO = JsonUtils.parseObject(fuluApiResponse.getResult(), new TypeReference<>() {
        });
        log.info("福禄查询到的余额结果为:[{}]", fuluUserInfoGetDTO);
        return SupplierBalanceDTO.builder()
                .balance(fuluUserInfoGetDTO.getBalance())
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .channelAccount(supplierDTO.getChannelAccount())
                .serialNumber(supplierDTO.getSerialNumber())
                .build();
    }


    private FuluApiResponse postRequest(FuluApiBizParams fuluApiBizParams, SupplierDTO supplierDTO) {
        // sign值需要后续生成后设置，在初始化时不设置
        FuluApiBaseRequestBizParams bizParams = buildFuluApiBaseRequestBizParams(fuluApiBizParams, supplierDTO);
        bizParams.setSign(generateSign(bizParams, supplierDTO.getSigningKey()));
        //TODO 这里需要增加验签逻辑 暂时没有找到验签逻辑说明 签名逻辑按照文档写 计算结果和示例对不上 verifyResultSign
        FuluFeignClient client = fuluFeignClientFactory.createClient(supplierDTO.getUrl());
        return client.postRequest(bizParams);
    }

    private FuluApiBaseRequestBizParams buildFuluApiBaseRequestBizParams(FuluApiBizParams fuluApiBizParams, SupplierDTO supplierDTO) {
        return FuluApiBaseRequestBizParams.builder()
                .appKey(supplierDTO.getAccountKey())
                .method(fuluApiBizParams.getMethodType().getMethodName())
                .timestamp(getTimeStamp())
                .version(fuluConfigurationProperties.getVersion())
                .format(fuluConfigurationProperties.getFormat())
                .charset(fuluConfigurationProperties.getCharset())
                .signType(fuluConfigurationProperties.getSignType())
                .appAuthToken(fuluConfigurationProperties.getAppAuthToken())
                .bizContent(JsonUtils.toJsonString(fuluApiBizParams))
                .build();
    }


    private void verifyResultSign(String result, String sign) {
        char[] charArray = result.toCharArray();
        Arrays.sort(charArray);
        String outputSignOriginalStr = new String(charArray) + fuluConfigurationProperties.getAppSecret();
        if (!MD5(outputSignOriginalStr).equals(sign)) {
            throw exception(FULU_GATEWAY_SIGN_FAILED);
        }
    }

    // 根据文档算法
    private String generateSign(FuluApiBaseRequestBizParams fuluApiBaseRequestBizParams, String appSecret) {
        Map<String, String> map = Arrays.stream(fuluApiBaseRequestBizParams.getClass().getDeclaredFields())
                .filter(field -> {
                    try {
                        field.setAccessible(true);
                        //sign先不设置，先过滤掉sign这个没有设置的字段
                        return field.get(fuluApiBaseRequestBizParams) != null;
                    } catch (IllegalAccessException e) {
                        log.error("读取字段值失败", e);
                        throw exception(FULU_GATEWAY_SIGN_FAILED);
                    }
                })
                .collect(Collectors.toMap(this::getFieldKey, field -> getFieldValue(fuluApiBaseRequestBizParams, field)));
        char[] str = JSONObject.toJSONString(map, SerializerFeature.WriteMapNullValue).toCharArray();
        Arrays.sort(str);

        String outputSignOriginalStr = new String(str) + appSecret;
        return MD5(outputSignOriginalStr);
    }

    private String getFieldKey(Field field) {
        JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
        return jsonProperty != null ? jsonProperty.value() : field.getName();
    }

    private String getFieldValue(FuluApiBaseRequestBizParams fuluApiBaseRequestBizParams, Field field) {
        try {
            field.setAccessible(true);
            return field.get(fuluApiBaseRequestBizParams).toString();
        } catch (IllegalAccessException e) {
            log.error("获取字段值失败", e);
            throw exception(FULU_GATEWAY_SIGN_FAILED);
        }
    }

    private String getTimeStamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }


    @Override
    public String getSupplierInterfaceClassName() {
        return fuluConfigurationProperties.getInterfaceClassName();
    }


}
