package cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JacksonXmlRootElement(localName = "root")
public class JiazhiyiBalanceResponse {
    @JacksonXmlProperty(localName = "businessId")
    private String businessId;

    @JacksonXmlProperty(localName = "balance")
    private BigDecimal balance;  // 或使用 BigDecimal

    @JacksonXmlProperty(localName = "status")
    private Integer status;

    @JacksonXmlProperty(localName = "mes")
    private String message;

    @JacksonXmlProperty(localName = "sign")
    private String sign;

}
