package cn.iocoder.yudao.module.integration.gateway.response.yiqida;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YiqidaRechargeResponse {

    private Integer code;
    private String msg;
    private OrderData data;
    private Integer sum;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class OrderData {
        /**
         * 亿奇达订单号
         */
        private String orderNo;

        /**
         * 亿奇达订单ID
         */
        private Long orderId;

        /**
         * 交付方式：
         * 3-官方直充
         * 6-卡密
         */
        private Integer virtualDelivery;

        /**
         * 品牌/规格ID
         */
        private Long branchId;

        /**
         * 产品名称
         */
        private String name;

        /**
         * 实际支付金额（单位：元）
         */
        private Double actualPrice;

        /**
         * 预售说明（为空表示非预售）
         */
        private String preSaleDesc;
    }
}
