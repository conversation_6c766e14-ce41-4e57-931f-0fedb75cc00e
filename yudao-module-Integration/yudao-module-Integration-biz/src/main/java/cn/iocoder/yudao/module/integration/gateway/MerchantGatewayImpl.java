package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.gateway.dto.OrderCallBackDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class MerchantGatewayImpl implements MerchantGateway {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public String notifyMerchant(String callbackUrl, OrderCallBackDTO callBackReqVO) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        try {
            HttpEntity<String> request = new HttpEntity<>(objectMapper.writeValueAsString(callBackReqVO), headers);
            ResponseEntity<String> response = restTemplate.postForEntity(callbackUrl, request, String.class);

            log.info("商户回调结果: {}", response.getBody());
            if (!"success".equals(response.getBody())) {
                return "failure";
            }

            return "success";
        } catch (Exception e) {
            log.error("通知商户失败", e);
            return "failure";
        }
    }
}
