package cn.iocoder.yudao.module.integration.gateway.response.yiqida;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YiqidaBalanceQueryResponse {
    private Integer code;
    private String msg;
    private BalanceInfo data;
    private Integer sum;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BalanceInfo {
        private String avatar;
        private String nickname;
        private String proxyTree;
        private String email;
        private BusinessInfo business;
        private String Id;
        private BigDecimal balance;
        private String creditBalance;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BusinessInfo {
        private String userName;
        private String Id;
        private String logo;
    }
}
