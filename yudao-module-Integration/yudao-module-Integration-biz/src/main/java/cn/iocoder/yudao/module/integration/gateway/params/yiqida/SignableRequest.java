package cn.iocoder.yudao.module.integration.gateway.params.yiqida;

import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;


public interface SignableRequest {
    default String generateSignContent(long timestamp, String signKey) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);  // 启用缩进
        String jsonString;
        try {
            jsonString = objectMapper.writeValueAsString(this);
            return MD5Utils.MD5(timestamp + jsonString + signKey);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }
}
