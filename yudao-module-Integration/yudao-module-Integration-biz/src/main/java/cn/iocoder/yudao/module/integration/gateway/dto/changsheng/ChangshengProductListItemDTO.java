package cn.iocoder.yudao.module.integration.gateway.dto.changsheng;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangshengProductListItemDTO {
    // 商品Id
    @JsonProperty("product_id")
    private Long productId;
    // 商品名称
    @JsonProperty("product_name")
    private String productName;
    // 库存类型：卡密、直充
    @JsonProperty("product_type")
    private String productType;
    // 面值
    @JsonProperty("face_value")
    private Double faceValue;
    // 单价（单位：元）
    @JsonProperty("purchase_price")
    private Double purchasePrice;
    // 销售状态
    @JsonProperty("status")
    private String status;
}
