package cn.iocoder.yudao.module.integration.gateway.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JINGYIN_GATEWAY_SIGN_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.WENYUE_GATEWAY_SIGN_FAILED;

@Slf4j
public class Utils {
    /**
     * map 转 字符串
     *
     * @param params
     * @return
     */
    public static String generateSortedQueryString(Map<String, ?> params, String key) {
        List<String> paramNames = new ArrayList<>(params.keySet());
        Collections.sort(paramNames);

        StringBuilder sb = new StringBuilder();
        for (String paramName : paramNames) {
            if (!sb.isEmpty()) {
                sb.append("&");
            }
            sb.append(paramName).append("=").append(params.get(paramName));
        }
        if (StringUtils.isNotEmpty(key)) {
            sb.append(key);
        }
        return sb.toString();
    }

    /**
     * 实体类转MAP
     *
     * @param entity
     * @return
     */
    public static Map<String, String> convertEntityToMap(Object entity) {
        Map<String, String> map = new HashMap<>();

        Field[] fields = entity.getClass().getDeclaredFields();

        for (Field field : fields) {
            try {
                field.setAccessible(true); // 设置字段可访问
                Object value = field.get(entity); // 获取字段的值
                // 值为空  签名字段不参与
                if (value != null && StringUtils.isNotEmpty(String.valueOf(value)) && !field.getName().equals("signKey")) {
                    map.put(field.getName(), String.valueOf(value));
                }
            } catch (IllegalAccessException e) {
                log.error("转换entity to map 失败， {}", entity);
                e.printStackTrace();
            }
        }

        return map;
    }


    //景银签名生成,需要排除掉 sign 字段和null字段，empty string不能排除
    public static String generateJingYinSign(Object params, String signKey) {
        Map<String, String> paramMap = new TreeMap<>();

        for (Field field : params.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(params);
                paramMap.put(field.getName(), value == null ? null : value.toString());
            } catch (IllegalAccessException e) {
                throw exception(JINGYIN_GATEWAY_SIGN_FAILED);
            }
        }

        StringBuilder signBuilder = new StringBuilder();
        paramMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !"sign".equals(entry.getKey()))
                .forEach(entry -> signBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&"));

        return MD5Utils.MD5(signBuilder + "key=" + signKey);
    }
}
