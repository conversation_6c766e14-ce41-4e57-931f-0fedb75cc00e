package cn.iocoder.yudao.module.integration.gateway.response.youkayun;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class YoukayunGoodListResponse {
    private Integer code;

    private String msg;

    private YoukayunGoodListData data;

    @Data
    public static class YoukayunGoodListData {
        private List<YoukayunGoodBriefData> data;
        private Integer count;
    }

    @Data
    public static class YoukayunGoodBriefData {
        /*
         * 商品id
         */
        private Integer id;
        /*
         * 商品名称
         */
        @JsonProperty("goods_name")
        private String goodsName;
        /*
         * 商品类型 1卡密商品 2代充商品
         */
        @JsonProperty("goods_type")
        private Integer goodsType;

        /**
         * 1为上架 3为下架
         */
        private Integer status;

        /**
         * 库存数量
         */
        @JsonProperty("stock_num")
        private Integer stockNum;

        /*
         * 	起售数量
         */
        @JsonProperty("start_count")
        private Integer startCount;

        /**
         * 商品价格
         */
        @JsonProperty("goods_price")
        private String goodsPrice;
    }
}
