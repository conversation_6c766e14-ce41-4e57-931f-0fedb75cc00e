package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.client.aiyiqi.mongolia.AiqiyiMongoliaFeignClient;
import cn.iocoder.yudao.module.integration.client.aiyiqi.mongolia.AiqiyiMongoliaFeignClientFactory;
import cn.iocoder.yudao.module.integration.gateway.config.AiqiyiMongoliaConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.*;
import cn.iocoder.yudao.module.integration.gateway.params.aiyiqi.mongolia.AiqiyiMongoliaApiParams;
import cn.iocoder.yudao.module.integration.gateway.params.aiyiqi.mongolia.AiqiyiMongoliaApiResponse;
import cn.iocoder.yudao.module.integration.gateway.utils.Utils;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.AIQIYI_NOT_SUPPORT_BALANCE_QUERY;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils.calculateMD5;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class AiqiyiMongoliaGateway implements SupplierGateway {
    private final AiqiyiMongoliaFeignClientFactory aiqiyiMongoliaFeignClientFactory;

    private final IntegrationOrderLogService integrationOrderLogService;

    private final AiqiyiMongoliaConfigurationProperties aiqiyiMongoliaConfigurationProperties;


    @Override
    public String getSupplierInterfaceClassName() {
        return aiqiyiMongoliaConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        return List.of();
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        return null;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入爱艺奇内蒙古内蒙古直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);

        AiqiyiMongoliaApiParams params = buildAiqiyiMongoliaApiParams(directRechargeOrderDTO);
        params.setSign(generateSignature(params, supplierDTO.getSigningKey()));

        AiqiyiMongoliaFeignClient client = aiqiyiMongoliaFeignClientFactory.createClient(supplierDTO.getUrl());

        log.info("in aiqiyi gateway,the url:[{}]", supplierDTO.getUrl());
        log.info("in aiqiyi gateway,the url encode:[{}]", encodeChineseInUrlExplicit(supplierDTO.getUrl()));
        AiqiyiMongoliaApiResponse aiqiyiMongoliaApiResponse;

        try {
            aiqiyiMongoliaApiResponse = client.postRequest(params);
        } catch (Exception e) {
            log.error("爱艺奇内蒙古直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }

        if (aiqiyiMongoliaApiResponse == null) {
            log.error("爱艺奇内蒙古直充接口返回结果为空");
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response: 接口返回结果为空， 请联系上游进行排查", params));
            throw exception(AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR, "爱艺奇内蒙古直充接口返回结果为空");
        }

        if (!"A00000".equals(aiqiyiMongoliaApiResponse.getCode())) {
            log.info("爱艺奇内蒙古直充接口返回错误码:[{}],错误信息:[{}]", aiqiyiMongoliaApiResponse.getCode(), aiqiyiMongoliaApiResponse.getMsg());
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", params, aiqiyiMongoliaApiResponse.getLogContent()));
            throw exception(AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR, aiqiyiMongoliaApiResponse.getMsg());
        }

        log.info("爱艺奇内蒙古直充接口返回正常码:[{}],信息:[{}]", aiqiyiMongoliaApiResponse.getCode(), aiqiyiMongoliaApiResponse.getMsg());
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", params, aiqiyiMongoliaApiResponse));
        return getDirectChargeResultDTO(supplierDTO, directRechargeOrderDTO);
    }

    private AiqiyiMongoliaApiParams buildAiqiyiMongoliaApiParams(DirectRechargeOrderDTO directRechargeOrderDTO) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String effectiveTime = LocalDateTime.now().format(formatter);
        String expireTime = LocalDateTime.now().plusMonths(1).format(formatter);

        return AiqiyiMongoliaApiParams.builder()
                .transactionID(directRechargeOrderDTO.getUpstreamOrderNo())
                .spID("bestv")
                .userID(directRechargeOrderDTO.getRechargeAccount())
                .stbID(directRechargeOrderDTO.getRechargeAccount())
                .mobileNO(directRechargeOrderDTO.getRechargeAccount())
                .opType(1)
                .productID(directRechargeOrderDTO.getProductId())
                .productPrice(0)
                .contentID("0")
                .effectiveTime(effectiveTime)
                .expireTime(expireTime)
                .orderContinue(1)
                .orderSource(2)
                .productType(2)
                .build();
    }

    private String encodeChineseInUrlExplicit(String url) {
        Pattern pattern = Pattern.compile("([\\u4e00-\\u9fa5]+)");
        Matcher matcher = pattern.matcher(url);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String matchedChinese = matcher.group(1);
            String encoded = URLEncoder.encode(matchedChinese, StandardCharsets.UTF_8)
                    .replace("+", "%20");
            matcher.appendReplacement(result, encoded);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    private String generateSignature(AiqiyiMongoliaApiParams params, String signKey) {
        Map<String, String> paramsMap = Utils.convertEntityToMap(params);
        return calculateMD5(Utils.generateSortedQueryString(paramsMap, signKey));
    }

    private static DirectChargeResultDTO getDirectChargeResultDTO(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO) {
        return DirectChargeResultDTO.builder()
                .upstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo())
                .rechargeAccount(directRechargeOrderDTO.getRechargeAccount())
                .quantity(directRechargeOrderDTO.getQuantity())
                .unitSalePrice(BigDecimal.valueOf(15)) // 价格设置为15
                .productId(directRechargeOrderDTO.getProductId())
                .externalOrderNo("")
                .upstreamProductName("")
                .orderType(4) // 4-直充
                .upstreamOrderStatus(0) // aiqiyi 这里一直返回成功
                .createTime("")
                .finishTime(LocalDateTime.now())
                .supplierId(supplierDTO.getId())
                .build();
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        // aiqiyi 暂不支持查询订单状态 这里只要查询就返回成功
        return UpstreamOrderQueryDTO.builder()
                .upstreamOrderStatus(0) // aiqiyi 这里一直返回成功
                .externalOrderNo("")
                .completeTime(LocalDateTime.now())
                .quantity(1) // 数量均为1
                .unitSalePrice(BigDecimal.valueOf(15L)) // 价格设置为15
                .build();
    }

    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        throw exception(AIQIYI_NOT_SUPPORT_BALANCE_QUERY);
    }
}
