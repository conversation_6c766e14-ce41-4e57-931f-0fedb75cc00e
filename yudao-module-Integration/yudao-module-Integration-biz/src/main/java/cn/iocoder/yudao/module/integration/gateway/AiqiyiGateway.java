package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.client.aiqiyi.AiqiyiFeignClient;
import cn.iocoder.yudao.module.integration.client.aiqiyi.AiqiyiFeignClientFactory;
import cn.iocoder.yudao.module.integration.gateway.config.AiqiyiConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.mapper.aiqiyi.AiqiyiOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.params.aiqiyi.AiqiyiApiParams;
import cn.iocoder.yudao.module.integration.gateway.params.aiqiyi.AiqiyiApiResponse;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.AIQIYI_NOT_SUPPORT_BALANCE_QUERY;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils.calculateMD5;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class AiqiyiGateway implements SupplierGateway {
    private final AiqiyiFeignClientFactory aiqiyiFeignClientFactory;

    private final IntegrationOrderLogService integrationOrderLogService;

    private final AiqiyiConfigurationProperties aiqiyiConfigurationProperties;


    @Override
    public String getSupplierInterfaceClassName() {
        return aiqiyiConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        return List.of();
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        return null;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入爱艺奇直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        AiqiyiApiParams params = AiqiyiOrderMapper.MAPPER.toParams(directRechargeOrderDTO);
        params.setPlatform("YY"); // 来源渠道，爱艺奇直充固定为 YY
        params.setSignature(generateSignature(params, supplierDTO));
        AiqiyiFeignClient client = aiqiyiFeignClientFactory.createClient(supplierDTO.getUrl());
        log.info("in aiqiyi gateway,the url:[{}]", supplierDTO.getUrl());
        log.info("in aiqiyi gateway,the url encode:[{}]", encodeChineseInUrlExplicit(supplierDTO.getUrl()));
        AiqiyiApiResponse aiqiyiApiResponse;
        try {
            aiqiyiApiResponse = client.postRequest(params);
        } catch (Exception e) {
            log.error("爱艺奇直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
        if (aiqiyiApiResponse == null) {
            log.error("爱艺奇直充接口返回结果为空");
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response: 接口返回结果为空， 请联系上游进行排查", params));
            throw exception(AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR, "爱艺奇直充接口返回结果为空");
        }
        if (aiqiyiApiResponse.getResult() != 0) {
            log.info("爱艺奇直充接口返回错误码:[{}],错误信息:[{}]", aiqiyiApiResponse.getResult(), aiqiyiApiResponse.getMsg());
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", params, aiqiyiApiResponse.getLogContent()));
            throw exception(AIQIYI_RESPONSE_RECHARGE_ORDER_ERROR, aiqiyiApiResponse.getMsg());
        }
        log.info("爱艺奇直充接口返回正常码:[{}],信息:[{}]", 0, aiqiyiApiResponse.getMsg());
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", params, aiqiyiApiResponse));
        return getDirectChargeResultDTO(supplierDTO, directRechargeOrderDTO);
    }

    private String encodeChineseInUrlExplicit(String url) {
        Pattern pattern = Pattern.compile("([\\u4e00-\\u9fa5]+)");
        Matcher matcher = pattern.matcher(url);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String matchedChinese = matcher.group(1);
            String encoded = URLEncoder.encode(matchedChinese, StandardCharsets.UTF_8)
                    .replace("+", "%20");
            matcher.appendReplacement(result, encoded);
        }
        matcher.appendTail(result);
        return result.toString();
    }

    private String generateSignature(AiqiyiApiParams params, SupplierDTO supplierDTO) {
        return calculateMD5(params.getMobile() + supplierDTO.getSigningKey());
    }

    private static DirectChargeResultDTO getDirectChargeResultDTO(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO) {
        return DirectChargeResultDTO.builder()
                .upstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo())
                .rechargeAccount(directRechargeOrderDTO.getRechargeAccount())
                .quantity(directRechargeOrderDTO.getQuantity())
                .unitSalePrice(BigDecimal.valueOf(15L)) // 价格设置为15
                .productId(directRechargeOrderDTO.getProductId())
                .externalOrderNo("")
                .upstreamProductName("")
                .orderType(4) // 4-直充
                .upstreamOrderStatus(0) // aiqiyi 这里一直返回成功
                .createTime("")
                .finishTime(LocalDateTime.now())
                .supplierId(supplierDTO.getId())
                .build();
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        // aiqiyi 暂不支持查询订单状态 这里只要查询就返回成功
        return UpstreamOrderQueryDTO.builder()
                .upstreamOrderStatus(0) // aiqiyi 这里一直返回成功
                .externalOrderNo("")
                .completeTime(LocalDateTime.now())
                .quantity(1) // 数量均为1
                .unitSalePrice(BigDecimal.valueOf(15L)) // 价格设置为15
                .build();
    }

    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        throw exception(AIQIYI_NOT_SUPPORT_BALANCE_QUERY);
    }
}
