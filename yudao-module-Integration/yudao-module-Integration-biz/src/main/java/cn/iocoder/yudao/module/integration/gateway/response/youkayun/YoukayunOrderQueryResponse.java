package cn.iocoder.yudao.module.integration.gateway.response.youkayun;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class YoukayunOrderQueryResponse {
    private Integer code;

    private String msg;

    private JsonNode data;

    public YoukayunOrderQueryResponseData parseSuccessData() throws JsonProcessingException {
        if (code == 1000) {
            return new ObjectMapper().treeToValue(data, YoukayunOrderQueryResponseData.class);
        }
        return null;
    }

    public List<?> parseErrorData() {
        if (code != 1000 && data.isArray()) {
            return new ObjectMapper().convertValue(data, List.class);
        }
        return Collections.emptyList();
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class YoukayunOrderQueryResponseData {
        @JsonProperty("goods_name")
        private String goodsName;

        @JsonProperty("goods_id")
        private Integer goodsId;

        @JsonProperty("goods_type")
        private Integer goodsType;

        @JsonProperty("ordersn")
        private String orderSn;

        @JsonProperty("outer_order_no")
        private String outerOrderNo;

        /**
         * 1-等待处理
         * 2-处理中
         * 3-交易成功
         * 4-交易失败（撤回未退款）
         * 5-成功已退款
         * 说明：订单成功状态为 3， 订单失败状态为5，交易失败不能做退款处理
         */
        @JsonProperty("status")
        private Integer status;

        @JsonProperty("quantity")
        private String quantity;

        @JsonProperty("total_price")
        private String totalPrice;

        @JsonProperty("create_time")
        private String createTime;

        @JsonProperty("recharge_account")
        private String rechargeAccount;

        @JsonProperty("cards")
        private List<Card> cards;
    }


    @Data
    public static class Card {
        @JsonProperty("card_no")
        private String cardNo;

        @JsonProperty("card_password")
        private String cardPassword;

        @JsonProperty("end_time")
        private String endTime;
    }
}
