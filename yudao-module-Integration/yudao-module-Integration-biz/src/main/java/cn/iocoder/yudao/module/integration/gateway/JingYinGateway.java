
package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.client.jingyin.JingYinFeignClient;
import cn.iocoder.yudao.module.integration.client.jingyin.JingYinFeignClientFactory;
import cn.iocoder.yudao.module.integration.gateway.config.JingYinConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.*;
import cn.iocoder.yudao.module.integration.gateway.mapper.jingyin.JingYinOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.params.jingyin.JingYinApiBizParams;
import cn.iocoder.yudao.module.integration.gateway.response.jingyin.JingYinApiResponse;
import cn.iocoder.yudao.module.integration.gateway.utils.Utils;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class JingYinGateway implements SupplierGateway {
    private final JingYinConfigurationProperties jingYinConfigurationProperties;
    private final JingYinFeignClientFactory jingYinFeignClientFactory;
    private final IntegrationOrderLogService integrationOrderLogService;

    @Override
    public String getSupplierInterfaceClassName() {
        return jingYinConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        return List.of();
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        return null;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入银景直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        JingYinApiBizParams bizParams = buildDirectChargeParams(supplierDTO, directRechargeOrderDTO);
        JingYinApiResponse jingYinApiResponse = callJingYinOrderApi(bizParams, supplierDTO, orderNo);

        if (jingYinApiResponse == null) {
            log.error("景银直充接口返回结果为空");
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response: null，上游返回结果为空, 直接设置状态订单为充值中，请通过订单号查询订单结果", bizParams));
            return buildDefaultDirectChargeResult(directRechargeOrderDTO);
        }

        if (!jingYinApiResponse.getResultCode().equals("0")) {
            log.error("景银直充调用接口结果异常:[{}]", jingYinApiResponse);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", bizParams.getLogContent(), jingYinApiResponse.getLogContent()));
            throw exception(JINGYIN_RESPONSE_RECHARGE_ORDER_ERROR, jingYinApiResponse.getResultCode());
        }

        DirectChargeResultDTO result = JingYinOrderMapper.MAPPER.toResult(jingYinApiResponse.getData());
        log.info("景银直充调用返回的DirectChargeResultDTO为:[{}]", result);
        result.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", bizParams.getLogContent(), jingYinApiResponse));

        return result;
    }

    private JingYinApiResponse callJingYinOrderApi(JingYinApiBizParams params, SupplierDTO supplier, String orderNo) {
        try {
            JingYinFeignClient client = jingYinFeignClientFactory.createClient(supplier.getUrl());
            JingYinApiResponse response = client.postRequest(params);
            log.info("景银直充调用接口结果为:[{}]", response);
            return response;
        } catch (Exception e) {
            log.error("景银直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(JINGYIN_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
    }

    private DirectChargeResultDTO buildDefaultDirectChargeResult(DirectRechargeOrderDTO directRechargeOrderDTO) {
        return DirectChargeResultDTO.builder()
                .upstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo())
                .upstreamOrderStatus(1) // 1表示充值中
                .externalOrderNo("default-external-order-no") // 默认外部订单号
                .build();
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        JingYinApiBizParams bizParams = JingYinApiBizParams.builder()
                .action("queryOrder")
                .requestTime(getTimeStamp())
                .merAccount(supplierDTO.getChannelAccount())
                .merOrderNo(upstreamOrderNo)
                .build();
        bizParams.setSign(Utils.generateJingYinSign(bizParams, supplierDTO.getSigningKey()));

        JingYinApiResponse jingYinApiResponse = callJingYinOrderApi(bizParams, supplierDTO, orderNo);

        if (jingYinApiResponse == null) {
            log.error("景银查询订单结果异常:接口返回结果为空");
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED,
                    String.format("request:[%s], response: 接口返回结果为空， 请联系上游进行排查", bizParams));
            throw exception(JINGYIN_ORDER_QUERY_ERROR, "接口返回结果为空，请联系上游进行排查");
        }

        if (!jingYinApiResponse.getResultCode().equals("0")) {
            log.error("景银查询订单结果异常:[{}]", jingYinApiResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, jingYinApiResponse.getResultMsg());
            throw exception(JINGYIN_ORDER_QUERY_ERROR, jingYinApiResponse.getResultMsg());
        }

        log.info("景银查询到的订单结果为:[{}]", jingYinApiResponse.getData());
        UpstreamOrderQueryDTO queryDTO = JingYinOrderMapper.MAPPER.toQueryDTO(jingYinApiResponse.getData());

        queryDTO.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS,
                String.format("request:[%s], response:[%s]", bizParams, jingYinApiResponse));
        return queryDTO;
    }

    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        JingYinApiResponse jingYinApiResponse;
        JingYinApiBizParams bizParams = JingYinApiBizParams.builder().action("queryBalance")
                .requestTime(getTimeStamp())
                .merAccount(supplierDTO.getChannelAccount())
                .build();
        bizParams.setSign(Utils.generateJingYinSign(bizParams, supplierDTO.getSigningKey()));

        try {
            JingYinFeignClient client = jingYinFeignClientFactory.createClient(supplierDTO.getUrl());
            jingYinApiResponse = client.postRequest(bizParams);
            log.info("查询余额 jingYinApiResponse 查询结果为:[{}]", jingYinApiResponse);
        } catch (Exception e) {
            log.error("景银查询余额结果异常", e);
            throw exception(JINGYIN_USER_INFO_QUERY_ERROR, e);
        }

        if (jingYinApiResponse == null || !jingYinApiResponse.getResultCode().equals("0")) {
            log.error("景银查询余额结果异常:[{}]", jingYinApiResponse);
            throw exception(JINGYIN_USER_INFO_QUERY_ERROR, jingYinApiResponse != null ? jingYinApiResponse.getResultMsg() : "接口返回结果为空，请联系上游进行排查");
        }

        log.info("景银查询到的余额结果为:[{}]", jingYinApiResponse.getData());
        return SupplierBalanceDTO.builder()
                .balance(jingYinApiResponse.getData().getBalance())
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .channelAccount(supplierDTO.getChannelAccount())
                .serialNumber(supplierDTO.getSerialNumber())
                .build();
    }

    private JingYinApiBizParams buildDirectChargeParams(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO) {
        JingYinApiBizParams bizParams = JingYinOrderMapper.MAPPER.toParams(directRechargeOrderDTO);
        bizParams.setAction("placeOrder");
        bizParams.setBusinessType(13);
        bizParams.setRequestTime(getTimeStamp());
        bizParams.setMerAccount(supplierDTO.getChannelAccount());
        bizParams.setNotifyUrl(jingYinConfigurationProperties.getCallbackUrl());
        bizParams.setNotifyType(1); //通知方式为Json
        bizParams.setSign(Utils.generateJingYinSign(bizParams, supplierDTO.getSigningKey()));
        return bizParams;
    }

    private String getTimeStamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
