package cn.iocoder.yudao.module.integration.gateway.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class OrderCallBackDTO {
    @Schema(description = "系统订单号")
    private String orderNo;

    @Schema(description = "商户订单号")
    private String merchantOrderNo;

    @Schema(description = "单价")
    private String unitSalePrice;

    @Schema(description = "商户采卡数量")
    private Integer quantity;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "签名秘钥")
    private String signKey;

    @JsonIgnore
    public String getLogContent() {
        return "通知下游参数:\n" +
                "orderNo='" + orderNo + "'\n" +
                "merchantOrderNo='" + merchantOrderNo + "'\n" +
                "unitSalePrice='" + unitSalePrice + "'\n" +
                "quantity='" + quantity + "'\n" +
                "orderStatus='" + orderStatus + "'\n" +
                "signKey=" + signKey + "\n";
    }
}
