package cn.iocoder.yudao.module.integration.gateway;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.integration.client.yiqida.YiqidaFeignClient;
import cn.iocoder.yudao.module.integration.client.yiqida.YiqidaFeignClientFactory;
import cn.iocoder.yudao.module.integration.enums.YiqidaMethodType;
import cn.iocoder.yudao.module.integration.gateway.config.YiqidaConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.mapper.yiqida.YiqidaOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.mapper.yiqida.YiqidaProductMapper;
import cn.iocoder.yudao.module.integration.gateway.params.yiqida.SignableRequest;
import cn.iocoder.yudao.module.integration.gateway.params.yiqida.YiqidaGoodInfoRequest;
import cn.iocoder.yudao.module.integration.gateway.params.yiqida.YiqidaGoodListPageRequest;
import cn.iocoder.yudao.module.integration.gateway.params.yiqida.YiqidaOrderQueryRequest;
import cn.iocoder.yudao.module.integration.gateway.params.yiqida.YiqidaRechargeRequest;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaBalanceQueryResponse;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaGoodInfoResponse;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaGoodListResponse;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaQueryResponse;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaRechargeResponse;
import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_CALL_BACK_BALANCE_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_CALL_BACK_ORDER_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_CHANNEL_ACCOUNT_IS_EMPTY;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_RECHARGE_ORDER_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_RECHARGE_ORDER_FAILED_ORDER_QUERY_NOT_FOUND;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_RECHARGE_ORDER_REPEAT;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_RETURN_GOODS_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_RETURN_GOOD_INFO_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_SIGN_KEY_IS_EMPTY;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class YiqidaGateway implements SupplierGateway {
    private final YiqidaConfigurationProperties yiqidaConfigurationProperties;

    private final IntegrationOrderLogService integrationOrderLogService;

    private final YiqidaFeignClientFactory yiqidaFeignClientFactory;

    @Override
    public String getSupplierInterfaceClassName() {
        return yiqidaConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        List<YiqidaGoodListResponse.YiqidaGoodResponseData> allData = getAllYiqidaGoodResponseData(supplierDTO);
        return allData.stream()
                .filter(yiqidaGoodResponseData -> yiqidaGoodResponseData.getStatus().equals(1)) //商品列表只保留上架状态的商品
                .map(YiqidaProductMapper.MAPPER::toPlatformProduct)
                .map(item -> item.setSupplierId(supplierDTO.getId()))
                .toList();
    }

    @NotNull
    private List<YiqidaGoodListResponse.YiqidaGoodResponseData> getAllYiqidaGoodResponseData(SupplierDTO supplierDTO) {
        List<YiqidaGoodListResponse.YiqidaGoodResponseData> allData = new ArrayList<>();
        int currentPage = 1;
        Integer totalSum = null; // 记录总记录数
        int totalFetched = 0;    // 已获取总数

        while (true) {
            YiqidaGoodListPageRequest request = YiqidaGoodListPageRequest.builder()
                    .page(currentPage)
                    .size(300)
                    .build();

            String requestParams = generateRequestParams(supplierDTO, request);
            YiqidaFeignClient client = yiqidaFeignClientFactory.createClient(
                    supplierDTO.getUrl() + YiqidaMethodType.GOOD_LIST.getMethodUri() + requestParams
            );

            YiqidaGoodListResponse response;
            try {
                response = client.getGoods(request);
            } catch (Exception e) {
                log.error("亿奇达商品列表请求异常 page={}", currentPage, e);
                throw exception(YIQIDA_RESPONSE_RETURN_GOODS_FAILED, ExceptionUtils.getMessage(e));
            }

            // 响应有效性检查
            if (isResponseFailed(response)) {
                log.warn("亿奇达商品列表异常响应 page={}, response={}", currentPage, response);
                throw exception(YIQIDA_RESPONSE_RETURN_GOODS_FAILED, response != null ? response.getMsg() : "空响应");
            }

            // 首次请求记录总数
            if (totalSum == null && response.getSum() != null) {
                totalSum = response.getSum();
                log.info("总记录数 sum={}", totalSum);
            }

            List<YiqidaGoodListResponse.YiqidaGoodResponseData> currentData = response.getData();
            if (CollectionUtil.isEmpty(currentData)) {
                log.info("遇到空数据页，停止翻页 page={}", currentPage);
                break;
            }

            allData.addAll(currentData);
            totalFetched += currentData.size();

            // 终止条件判断
            boolean shouldContinue = (totalSum == null) ?
                    !currentData.isEmpty() :  // 未获取总数时，只要本页有数据就继续
                    (totalFetched < totalSum); // 已知总数时，比较已获取量

            if (!shouldContinue) {
                log.info("分页终止 totalSum={}, totalFetched={}", totalSum, totalFetched);
                break;
            }

            currentPage++;
        }
        return allData;
    }

    private boolean isResponseFailed(YiqidaGoodListResponse response) {
        return response == null ||
                response.getCode() != 200 ||
                response.getData() == null; // 允许空列表（空列表是合法响应）
    }

    private boolean isResponseFailed(YiqidaGoodInfoResponse response) {
        return response == null ||
                response.getCode() != 200 ||
                response.getData() == null; // 允许空列表（空列表是合法响应）
    }


    private String generateRequestParams(SupplierDTO supplierDTO, SignableRequest request) {
        validateSignParam(supplierDTO);
        long timestamp = System.currentTimeMillis();
        log.info("timestamp={}", timestamp);
        log.info("request json={}", JsonUtils.toJsonString(request));
        String sign = request.generateSignContent(timestamp, supplierDTO.getSigningKey());
        log.info("亿奇达计算签名的body为:[{}], 计算签名的结果为:[{}]", request, sign);
        return String.format("?timestamp=%s&sign=%s&userName=%s", timestamp, sign, supplierDTO.getChannelAccount());
    }

    private static void validateSignParam(SupplierDTO supplierDTO) {
        if (StringUtils.isEmpty(supplierDTO.getSigningKey())) {
            throw exception(YIQIDA_SIGN_KEY_IS_EMPTY);
        }
        if (StringUtils.isEmpty(supplierDTO.getChannelAccount())) {
            throw exception(YIQIDA_CHANNEL_ACCOUNT_IS_EMPTY);
        }
    }

    private String generateRequestParams(SupplierDTO supplierDTO) {
        validateSignParam(supplierDTO);
        long timestamp = System.currentTimeMillis();
        String sign = MD5Utils.MD5(timestamp + supplierDTO.getSigningKey());
        log.info("亿奇达计算签名的结果为:[{}]", sign);
        return String.format("?timestamp=%s&sign=%s&userName=%s", timestamp, sign, supplierDTO.getChannelAccount());
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        YiqidaGoodInfoRequest request = YiqidaGoodInfoRequest.builder().id(productId).build();
        String requestParams = generateRequestParams(supplierDTO, request);
        YiqidaFeignClient client = yiqidaFeignClientFactory.createClient(supplierDTO.getUrl() + YiqidaMethodType.GOOD_INFO.getMethodUri() + requestParams);
        YiqidaGoodInfoResponse yiqidaGoodInfoResponse;
        try {
            yiqidaGoodInfoResponse = client.getGood(request);
        } catch (Exception e) {
            log.error("亿奇达获取商品详情请求异常", e);
            throw exception(YIQIDA_RESPONSE_RETURN_GOOD_INFO_FAILED, ExceptionUtils.getMessage(e));
        }
        if (isResponseFailed(yiqidaGoodInfoResponse)) {
            throw exception(YIQIDA_RESPONSE_RETURN_GOOD_INFO_FAILED, yiqidaGoodInfoResponse != null ? yiqidaGoodInfoResponse.getMsg() : null);
        }
        SupplierProductDTO platformProduct = YiqidaProductMapper.MAPPER.toPlatformProduct(yiqidaGoodInfoResponse.getData());
        platformProduct.setSupplierId(supplierDTO.getId());
        return platformProduct;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入亿奇达直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        // TODO 需要set回调地址
        YiqidaRechargeRequest request = YiqidaOrderMapper.MAPPER.toRequest(directRechargeOrderDTO);
        request.setCallbackUrl(yiqidaConfigurationProperties.getCallbackUrl());
        log.info("在亿奇达直充, request:[{}]", request);
        String requestParams = generateRequestParams(supplierDTO, request);
        YiqidaFeignClient client = yiqidaFeignClientFactory.createClient(supplierDTO.getUrl() + YiqidaMethodType.ORDER_DIRECT_ADD.getMethodUri() + requestParams);
        YiqidaRechargeResponse yiqidaRechargeResponse;
        try {
            yiqidaRechargeResponse = client.recharge(request);
            log.info("亿奇达直充调用接口结果yiqidaRechargeResponse:[{}]", yiqidaRechargeResponse);
        } catch (FeignException.GatewayTimeout | FeignException.InternalServerError |
                 FeignException.ServiceUnavailable e) {
            log.error("亿奇达直充调用接口发生feign异常.", e);
            YiqidaOrderQueryRequest queryRequest = YiqidaOrderQueryRequest.builder().externalOrderNo(directRechargeOrderDTO.getUpstreamOrderNo()).orderTime("0").build();
            String queryRequestParams = generateRequestParams(supplierDTO, request);
            YiqidaFeignClient queryClient = yiqidaFeignClientFactory.createClient(supplierDTO.getUrl() + YiqidaMethodType.QUERY_ORDER.getMethodUri() + queryRequestParams);
            YiqidaQueryResponse yiqidaQueryResponse = queryClient.query(queryRequest);
            if (yiqidaQueryResponse.getCode() == 200 && yiqidaQueryResponse.getData() != null) {
                String yiqidaOrderNo = Optional.ofNullable(yiqidaQueryResponse.getData().getOrderNo())
                        .orElseThrow(() -> exception(YIQIDA_RESPONSE_RECHARGE_ORDER_FAILED_ORDER_QUERY_NOT_FOUND));
                if (Objects.nonNull(yiqidaOrderNo) && StringUtils.isNotBlank(yiqidaOrderNo)) {
                    return YiqidaOrderMapper.MAPPER.toResult(yiqidaQueryResponse.getData());
                }
            }
            throw exception(YIQIDA_RESPONSE_RECHARGE_ORDER_ERROR, e);
        } catch (Exception e) {
            log.error("亿奇达直充调用接口发生异常.", e);
            // 这里需要 “下单请求出现web网络异常请不要直接失败订单 请调用查询接口确认”
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(YIQIDA_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
        if (yiqidaRechargeResponse.getCode() != 200) {
            log.error("亿奇达直充调用接口结果异常:[{}]", yiqidaRechargeResponse);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", request, yiqidaRechargeResponse));
            if (yiqidaRechargeResponse.getCode() == 506) {
                throw exception(YIQIDA_RESPONSE_RECHARGE_ORDER_FAILED, yiqidaRechargeResponse.getMsg());
            }
            if (yiqidaRechargeResponse.getCode() == 1001) {
                throw exception(YIQIDA_RESPONSE_RECHARGE_ORDER_REPEAT, yiqidaRechargeResponse.getMsg());
            }
            throw exception(YIQIDA_RESPONSE_RECHARGE_ORDER_ERROR, yiqidaRechargeResponse.getMsg());
        }
        DirectChargeResultDTO result = YiqidaOrderMapper.MAPPER.toResult(yiqidaRechargeResponse.getData());
        log.info("亿奇达直充调用接口结果result:[{}]", result);
        //这里和fulu不同，返回值里没有upstreamOrderNo，所以需要手动set下
        result.setUpstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo());
        result.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", request, yiqidaRechargeResponse));
        return result;
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        YiqidaOrderQueryRequest request = YiqidaOrderQueryRequest.builder().externalOrderNo(upstreamOrderNo).orderTime("0").build();
        String requestParams = generateRequestParams(supplierDTO, request);
        YiqidaFeignClient client = yiqidaFeignClientFactory.createClient(supplierDTO.getUrl() + YiqidaMethodType.QUERY_ORDER.getMethodUri() + requestParams);
        YiqidaQueryResponse yiqidaQueryResponse;
        try {
            yiqidaQueryResponse = client.query(request);
        } catch (Exception e) {
            log.error("亿奇达查询订单异常", e);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, ExceptionUtils.getMessage(e) + e);
            throw exception(YIQIDA_CALL_BACK_ORDER_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (yiqidaQueryResponse.getCode() != 200) {
            log.error("亿奇达查询订单结果异常:[{}]", yiqidaQueryResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, yiqidaQueryResponse.getMsg());
            throw exception(YIQIDA_CALL_BACK_ORDER_QUERY_ERROR, yiqidaQueryResponse.getMsg());
        }
        UpstreamOrderQueryDTO queryDTO = YiqidaOrderMapper.MAPPER.toQueryDTO(yiqidaQueryResponse.getData());
        queryDTO.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS,
                String.format("request:[%s], response:[%s]", requestParams, yiqidaQueryResponse));
        return queryDTO;
    }

    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        String requestParams = generateRequestParams(supplierDTO);
        YiqidaFeignClient client = yiqidaFeignClientFactory.createClient(supplierDTO.getUrl() + YiqidaMethodType.BALANCE_QUERY.getMethodUri() + requestParams);
        YiqidaBalanceQueryResponse yiqidaBalanceQueryResponse;
        try {
            yiqidaBalanceQueryResponse = client.balanceQuery();
            log.info("亿奇达查询余额结果yiqidaBalanceQueryResponse:[{}]", yiqidaBalanceQueryResponse);
        } catch (Exception e) {
            log.error("亿奇达查询余额异常", e);
            throw exception(YIQIDA_CALL_BACK_BALANCE_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (yiqidaBalanceQueryResponse.getCode() != 200) {
            log.error("亿奇达查询余额结果异常:[{}]", yiqidaBalanceQueryResponse);
            throw exception(YIQIDA_CALL_BACK_ORDER_QUERY_ERROR, yiqidaBalanceQueryResponse.getMsg());
        }
        SupplierBalanceDTO result = SupplierBalanceDTO.builder()
                .balance(yiqidaBalanceQueryResponse.getData().getBalance())
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .serialNumber(supplierDTO.getSerialNumber())
                .channelAccount(supplierDTO.getChannelAccount())
                .build();
        log.info("亿奇达查询余额结果result:[{}]", result);
        return result;
    }
}
