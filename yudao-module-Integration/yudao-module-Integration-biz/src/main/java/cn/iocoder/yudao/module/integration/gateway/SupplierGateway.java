package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface SupplierGateway {

    String getSupplierInterfaceClassName();

    List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO);

    SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO);

    DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo);

    UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO);

    SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO);
}
