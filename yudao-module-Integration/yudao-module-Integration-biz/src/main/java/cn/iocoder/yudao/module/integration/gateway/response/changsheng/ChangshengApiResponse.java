package cn.iocoder.yudao.module.integration.gateway.response.changsheng;

import cn.iocoder.yudao.module.integration.gateway.response.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ChangshengApiResponse extends BaseResponse {
    private String message;
    private String result;
    private String sign;

    public String getLogContent() {
        return "上游直充接口响应:\n" +
                "code=" + getCode() + "\n" +
                "message='" + getMessage() + "'\n" +
                "result='" + getResult() + "'\n" +
                "sign='" + getSign() + "'";
    }
}
