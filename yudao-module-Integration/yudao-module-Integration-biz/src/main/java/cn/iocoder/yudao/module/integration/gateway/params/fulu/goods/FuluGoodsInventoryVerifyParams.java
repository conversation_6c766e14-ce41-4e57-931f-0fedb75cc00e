package cn.iocoder.yudao.module.integration.gateway.params.fulu.goods;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluGoodsInventoryVerifyParams implements FuluApiBizParams {
    @Schema(description = "库存状态", example = "充足", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 10)
    @JsonProperty("stock_status")
    private String stockStatus;

    @Schema(description = "商品编号", example = "12817240", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("product_id")
    private Integer productId;

    @Override
    public FuluMethodType getMethodType() {
        return FuluMethodType.GOOD_INVENTORY_VERIFY;
    }
}
