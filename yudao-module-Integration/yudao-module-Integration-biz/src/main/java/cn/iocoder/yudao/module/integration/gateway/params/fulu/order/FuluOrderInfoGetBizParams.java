package cn.iocoder.yudao.module.integration.gateway.params.fulu.order;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import static cn.iocoder.yudao.module.integration.enums.FuluMethodType.ORDER_INFO_GET;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluOrderInfoGetBizParams implements FuluApiBizParams {

    @Schema(description = "外部订单号", example = "201906281030191013526", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer_order_no")
    private String customerOrderNo;

    @Override
    public FuluMethodType getMethodType() {
        return ORDER_INFO_GET;
    }
}
