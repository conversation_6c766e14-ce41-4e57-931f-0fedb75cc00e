package cn.iocoder.yudao.module.integration.gateway.dto.jingyin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JingYinResultDTO {
    @Schema(description = "商户账号，10位")
    private String merAccount;

    @Schema(description = "商户用户账号，30位")
    private String merUserAccount;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "商户订单号，30位")
    private String merOrderNo;

    @Schema(description = "商户订单时间 yyyy-MM-dd HH:mm:ss")
    private String merOrderTime;

    @Schema(description = "充值缴费系统订单号，50位")
    private String orderNo;

    @Schema(description = "系统接收订单时间 yyyy-MM-dd HH:mm:ss")
    private String orderTime;

    @Schema(description = "订单结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Schema(description = "订单涉及总金额（厘）")
    private Long orderAmount;

    @Schema(description = "实际应付金额（厘）")
    private Long payAmount;

    @Schema(description = "最终结算金额（厘）")
    private Long discountAmount;

    @Schema(description = "成功金额（厘）")
    private Double successAmount;

    @Schema(description = "订单详情，100位")
    private String orderDetail;

    @Schema(description = "订单状态")
    private Integer orderState;

    @Schema(description = "订单状态描述，50位")
    private String orderStateDesc;

    @Schema(description = "运营商凭证，50位")
    private String voucher;

    @Schema(description = "官方失败结果，JSON字符串，500位")
    private String officialResult;

    @Schema(description = "卡密信息，加密字符串")
    private String cardInfo;

    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "信用余额")
    private BigDecimal creditBalance;

    @Schema(description = "实际余额")
    private BigDecimal actualBalance;

    public String getLogContent() {
        return "上游订单结果:\n" +
                "merAccount='" + merAccount + "'\n" +
                "merUserAccount='" + merUserAccount + "'\n" +
                "businessType=" + businessType + "\n" +
                "merOrderNo='" + merOrderNo + "'\n" +
                "merOrderTime='" + merOrderTime + "'\n" +
                "orderNo='" + orderNo + "'\n" +
                "orderTime='" + orderTime + "'\n" +
                "endTime='" + endTime + "'\n" +
                "orderAmount=" + orderAmount + "\n" +
                "payAmount=" + payAmount + "\n" +
                "discountAmount=" + discountAmount + "\n" +
                "successAmount=" + successAmount + "\n" +
                "orderDetail='" + orderDetail + "'\n" +
                "orderState=" + orderState + "\n" +
                "orderStateDesc='" + orderStateDesc + "'\n" +
                "voucher='" + voucher + "'\n" +
                "officialResult='" + officialResult + "'\n" +
                "cardInfo='" + cardInfo + "'\n";
    }
}
