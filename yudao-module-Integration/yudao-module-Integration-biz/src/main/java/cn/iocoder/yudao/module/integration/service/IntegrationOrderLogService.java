package cn.iocoder.yudao.module.integration.service;

import cn.iocoder.yudao.module.integration.dal.dataobject.IntegrationOrderLogDO;
import cn.iocoder.yudao.module.integration.dal.mysql.IntegrationOrderLogMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class IntegrationOrderLogService {
    @Resource
    private IntegrationOrderLogMapper integrationOrderLogMapper;

    public void insertOrderLog(String orderId, OrderLogType orderLogType, String logContent) {
        IntegrationOrderLogDO integrationOrderLogDO = IntegrationOrderLogDO.builder()
                .mainOrderNo(orderId)
                .logType(orderLogType.getCode())
                .logContent(logContent)
                .createTime(LocalDateTime.now())
                .build();
        integrationOrderLogMapper.insert(integrationOrderLogDO);
    }
}
