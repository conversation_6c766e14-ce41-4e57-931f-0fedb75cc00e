package cn.iocoder.yudao.module.integration.gateway.params.changsheng;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangshengApiBaseRequestBizParams {

    @JsonProperty("app_key")
    private String appKey;

    @JsonProperty("sign")
    private String sign;

    @JsonProperty("method")
    private String method;
    //时间戳，格式为：yyyy-MM-dd HH:mm:ss
    @JsonProperty("timestamp")
    private String timestamp;

    @JsonProperty("version")
    private String version;

    @JsonProperty("format")
    private String format;

    @JsonProperty("charset")
    private String charset;

    @JsonProperty("sign_type")
    private String signType;

    @JsonProperty("app_auth_token")
    private String appAuthToken;

    @JsonProperty("biz_content")
    private String bizContent;

}
