package cn.iocoder.yudao.module.integration.gateway;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.integration.client.changsheng.ChangshengFeignClient;
import cn.iocoder.yudao.module.integration.client.changsheng.ChangshengFeignClientFactory;
import cn.iocoder.yudao.module.integration.gateway.config.ChangshengConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengDirectRechargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengOrderInfoGetDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengProductInfoDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengProductListItemDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.changsheng.ChangshengUserInfoGetDTO;
import cn.iocoder.yudao.module.integration.gateway.mapper.changsheng.ChangshengOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.mapper.changsheng.ChangshengProductMapper;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.ChangshengApiBaseRequestBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.ChangshengApiBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.goods.ChangshengGoodInfoBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.goods.ChangshengGoodListBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.order.ChangshengDirectRechargeOrderBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.order.ChangshengOrderInfoGetBizParams;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.user.ChangshengUserInfoGetBizParams;
import cn.iocoder.yudao.module.integration.gateway.response.changsheng.ChangshengApiResponse;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Repository;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_CALL_BACK_ORDER_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_GATEWAY_SIGN_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_RESPONSE_RETURN_GOODS_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_RESPONSE_RETURN_GOOD_INFO_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_USER_INFO_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils.MD5;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class ChangshengGateway implements SupplierGateway {
    private final ChangshengConfigurationProperties changshengConfigurationProperties;
    private final ChangshengFeignClientFactory changshengFeignClientFactory;
    private final IntegrationOrderLogService integrationOrderLogService;

    @Override
    public String getSupplierInterfaceClassName() {
        return changshengConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        ChangshengApiResponse changshengApiResponse;
        try {
            changshengApiResponse = postRequest(new ChangshengGoodListBizParams(), supplierDTO);
        } catch (Exception e) {
            log.error("昌盛获取商品列表异常", e);
            throw exception(CHANGSHENG_RESPONSE_RETURN_GOODS_FAILED, ExceptionUtils.getMessage(e));
        }
        if (changshengApiResponse.getCode() != 0) {
            log.error("昌盛获取商品列表结果异常:[{}]", changshengApiResponse);
            throw exception(CHANGSHENG_RESPONSE_RETURN_GOODS_FAILED, changshengApiResponse.getMessage());
        }
        List<ChangshengProductListItemDTO> changshengProductListItemDTOS = JsonUtils.parseObject(changshengApiResponse.getResult(), new TypeReference<>() {
        });
        return changshengProductListItemDTOS.stream()
                .map(ChangshengProductMapper.MAPPER::toPlatformProduct)
                .map(item -> item.setSupplierId(supplierDTO.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        ChangshengApiResponse changshengApiResponse;
        try {
            changshengApiResponse = postRequest(new ChangshengGoodInfoBizParams(String.valueOf(productId)), supplierDTO);
        } catch (Exception e) {
            log.error("昌盛获取商品信息异常", e);
            throw exception(CHANGSHENG_RESPONSE_RETURN_GOOD_INFO_FAILED, ExceptionUtils.getMessage(e));
        }
        if (changshengApiResponse.getCode() != 0) {
            log.error("昌盛获取商品信息结果异常:[{}]", changshengApiResponse);
            throw exception(CHANGSHENG_RESPONSE_RETURN_GOOD_INFO_FAILED, changshengApiResponse.getMessage());
        }
        ChangshengProductInfoDTO productInfoDTO = JsonUtils.parseObject(changshengApiResponse.getResult(), new TypeReference<>() {
        });
        SupplierProductDTO platformProduct = ChangshengProductMapper.MAPPER.toPlatformProduct(productInfoDTO);
        platformProduct.setSupplierId(supplierDTO.getId());
        return platformProduct;
    }

    @Override
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入昌盛直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        ChangshengDirectRechargeOrderBizParams changshengDirectRechargeOrderBizParams = ChangshengOrderMapper.MAPPER.toParams(directRechargeOrderDTO);
        ChangshengApiResponse changshengApiResponse;
        try {
            changshengApiResponse = postRequest(changshengDirectRechargeOrderBizParams, supplierDTO);
            log.info("昌盛直充调用接口结果为:[{}]", changshengApiResponse);
        } catch (Exception e) {
            log.error("昌盛直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(CHANGSHENG_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
        if (changshengApiResponse.getCode() != 0) {
            log.error("昌盛直充调用接口结果异常:[{}]", changshengApiResponse);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", changshengDirectRechargeOrderBizParams.getLogContent(), changshengApiResponse.getLogContent()));
            throw exception(CHANGSHENG_RESPONSE_RECHARGE_ORDER_ERROR, changshengApiResponse.getMessage());
        }
        ChangshengDirectRechargeResultDTO changshengDirectRechargeResultDTO = JsonUtils.parseObject(changshengApiResponse.getResult(), new TypeReference<>() {
        });
        DirectChargeResultDTO result = ChangshengOrderMapper.MAPPER.toResult(changshengDirectRechargeResultDTO);
        log.info("昌盛直充调用接口结果为:[{}]", result);
        result.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", changshengDirectRechargeOrderBizParams.toString(), changshengApiResponse));
        return result;
    }

    @Override
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        ChangshengApiResponse changshengApiResponse;
        ChangshengOrderInfoGetBizParams changshengOrderInfoGetBizParams;
        try {
            changshengOrderInfoGetBizParams = ChangshengOrderInfoGetBizParams.builder().customerOrderNo(upstreamOrderNo).build();
            changshengApiResponse = postRequest(changshengOrderInfoGetBizParams, supplierDTO);
            log.info("查询订单changshengApiResponse 查询结果为:[{}]", changshengApiResponse);
        } catch (Exception e) {
            log.error("昌盛查询订单异常", e);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, ExceptionUtils.getMessage(e) + e);
            throw exception(CHANGSHENG_CALL_BACK_ORDER_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (changshengApiResponse.getCode() != 0) {
            log.error("昌盛查询订单结果异常:[{}]", changshengApiResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, changshengApiResponse.getMessage());
            throw exception(CHANGSHENG_CALL_BACK_ORDER_QUERY_ERROR, changshengApiResponse.getMessage());
        }
        ChangshengOrderInfoGetDTO changshengOrderInfoGetDTO = JsonUtils.parseObject(changshengApiResponse.getResult(), new TypeReference<>() {
        });
        log.info("昌盛查询到的订单结果为:[{}]", changshengOrderInfoGetDTO);
        UpstreamOrderQueryDTO queryDTO = ChangshengOrderMapper.MAPPER.toQueryDTO(changshengOrderInfoGetDTO);
        queryDTO.setSupplierId(supplierDTO.getId());
        integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS,
                String.format("request:[%s], response:[%s]", changshengOrderInfoGetBizParams, changshengApiResponse));
        return queryDTO;
    }

    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        ChangshengApiResponse changshengApiResponse;
        try {
            changshengApiResponse = postRequest(ChangshengUserInfoGetBizParams.builder().build(), supplierDTO);
            log.info("查询余额changshengApiResponse 查询结果为:[{}]", changshengApiResponse);
        } catch (Exception e) {
            log.error("昌盛用户信息查询异常", e);
            throw exception(CHANGSHENG_USER_INFO_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        ChangshengUserInfoGetDTO changshengUserInfoGetDTO = JsonUtils.parseObject(changshengApiResponse.getResult(), new TypeReference<>() {
        });
        log.info("昌盛查询到的余额结果为:[{}]", changshengUserInfoGetDTO);
        return SupplierBalanceDTO.builder()
                .balance(changshengUserInfoGetDTO.getBalance())
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .channelAccount(supplierDTO.getChannelAccount())
                .serialNumber(supplierDTO.getSerialNumber())
                .build();
    }

    private ChangshengApiResponse postRequest(ChangshengApiBizParams changshengApiBizParams, SupplierDTO supplierDTO) {
        // sign值需要后续生成后设置，在初始化时不设置
        ChangshengApiBaseRequestBizParams bizParams = buildChangshengApiBaseRequestBizParams(changshengApiBizParams, supplierDTO);
        bizParams.setSign(generateSign(bizParams, supplierDTO.getSigningKey()));
        //TODO 这里需要增加验签逻辑 暂时没有找到验签逻辑说明 签名逻辑按照文档写 计算结果和示例对不上 verifyResultSign
        ChangshengFeignClient client = changshengFeignClientFactory.createClient(supplierDTO.getUrl());
        return client.postRequest(bizParams);
    }

    private String generateSign(ChangshengApiBaseRequestBizParams changshengApiBaseRequestBizParams, String appSecret) {
        Map<String, String> map = Arrays.stream(changshengApiBaseRequestBizParams.getClass().getDeclaredFields())
                .filter(field -> {
                    try {
                        field.setAccessible(true);
                        //sign先不设置，先过滤掉sign这个没有设置的字段
                        return field.get(changshengApiBaseRequestBizParams) != null;
                    } catch (IllegalAccessException e) {
                        log.error("读取字段值失败", e);
                        throw exception(CHANGSHENG_GATEWAY_SIGN_FAILED);
                    }
                })
                .collect(Collectors.toMap(this::getFieldKey, field -> getFieldValue(changshengApiBaseRequestBizParams, field)));
        char[] str = JSONObject.toJSONString(map, SerializerFeature.WriteMapNullValue).toCharArray();
        Arrays.sort(str);

        String outputSignOriginalStr = new String(str) + appSecret;
        return MD5(outputSignOriginalStr);
    }

    private String getFieldKey(Field field) {
        JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
        return jsonProperty != null ? jsonProperty.value() : field.getName();
    }

    private String getFieldValue(ChangshengApiBaseRequestBizParams changshengApiBaseRequestBizParams, Field field) {
        try {
            field.setAccessible(true);
            return field.get(changshengApiBaseRequestBizParams).toString();
        } catch (IllegalAccessException e) {
            log.error("获取字段值失败", e);
            throw exception(CHANGSHENG_GATEWAY_SIGN_FAILED);
        }
    }

    private ChangshengApiBaseRequestBizParams buildChangshengApiBaseRequestBizParams(ChangshengApiBizParams changshengApiBizParams, SupplierDTO supplierDTO) {
        return ChangshengApiBaseRequestBizParams.builder()
                .appKey(supplierDTO.getChannelAccount())
                .method(changshengApiBizParams.getMethodType().getMethodName())
                .timestamp(getTimeStamp())
                .version(changshengConfigurationProperties.getVersion())
                .format(changshengConfigurationProperties.getFormat())
                .charset(changshengConfigurationProperties.getCharset())
                .signType(changshengConfigurationProperties.getSignType())
                .appAuthToken(changshengConfigurationProperties.getAppAuthToken())
                .bizContent(JsonUtils.toJsonString(changshengApiBizParams))
                .build();
    }

    private String getTimeStamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
