package cn.iocoder.yudao.module.integration.gateway.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SupplierBalanceDTO {
    @Schema(description = "供应商ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long supplierId;

    private String serialNumber;

    private String company;

    private String channelAccount;

    @Schema(description = "渠道余额", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal balance;
}
