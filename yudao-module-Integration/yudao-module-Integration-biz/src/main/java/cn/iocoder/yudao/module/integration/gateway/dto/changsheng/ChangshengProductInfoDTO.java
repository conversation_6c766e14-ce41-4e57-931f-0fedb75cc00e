package cn.iocoder.yudao.module.integration.gateway.dto.changsheng;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 福禄返回商品详情的数据结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangshengProductInfoDTO {
    @JsonProperty("product_id")
    private Integer productId;

    @JsonProperty("product_name")
    private String productName;

    @JsonProperty("face_value")
    private Double faceValue;

    @JsonProperty("product_type")
    private String productType;

    @JsonProperty("purchase_price")
    private Double purchasePrice;

    @JsonProperty("template_id")
    private String templateId;

    @JsonProperty("status")
    private String status;

}
