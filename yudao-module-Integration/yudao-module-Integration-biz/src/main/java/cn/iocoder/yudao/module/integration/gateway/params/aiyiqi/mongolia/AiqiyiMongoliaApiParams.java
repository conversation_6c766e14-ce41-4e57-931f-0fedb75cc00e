package cn.iocoder.yudao.module.integration.gateway.params.aiyiqi.mongolia;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiqiyiMongoliaApiParams {

    //stbID userID mobileNO 都用手机号
    @Schema(name = "事务编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("transactionID")
    private String transactionID;

    @Schema(name = "SP厂商编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("spID")
    private String spID;

    @Schema(name = "用户账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("userID")
    private String userID;

    @Schema(name = "机顶盒编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("stbID")
    private String stbID;

    @Schema(name = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("mobileNO")
    private String mobileNO;

    @Schema(name = "同步类型：1.订购 2.取消续订或立即退订", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("opType")
    private Integer opType;

    @Schema(name = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("productID")
    private String productID;

    @Schema(name = "扣费金额 单位：分", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("productPrice")
    private Integer productPrice;

    @Schema(name = "内容编码，如果订购PPV内容，该字段必传", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("contentID")
    private String contentID;

    @Schema(name = "生效时间格式： YYYYMMDDHHMISS", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("effectiveTime")
    private String effectiveTime;

    @Schema(name = "失效时间格式： YYYYMMDDHHMISS", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("expireTime")
    private String expireTime;

    @Schema(name = "是否自动续订，默认为自动续订 0：不自动续订， 1：自动续订", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("orderContinue")
    private Integer orderContinue;

    @Schema(name = "订购来源，默认为0，即营业厅 0：营业厅门户 1：线上 2：其他", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("orderSource")
    private Integer orderSource;

    @Schema(name = "1：按次、2：包月、3：连续包月、4：包年", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("productType")
    private Integer productType;

    @Schema(name = "鉴权编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sign")
    private String sign;
}
