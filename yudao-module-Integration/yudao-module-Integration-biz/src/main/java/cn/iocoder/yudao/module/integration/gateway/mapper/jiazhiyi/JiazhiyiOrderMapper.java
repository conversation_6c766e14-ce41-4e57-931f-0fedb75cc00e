package cn.iocoder.yudao.module.integration.gateway.mapper.jiazhiyi;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.params.jiazhiyi.JiazhiyiDirectRechargeRequest;
import cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi.JiazhiyiOrderResponse;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JIAZHIYI_RESPONSE_ORDER_STATUS_ERROR;

@Mapper
public interface JiazhiyiOrderMapper {
    JiazhiyiOrderMapper MAPPER = Mappers.getMapper(JiazhiyiOrderMapper.class);

    @Mappings({
            @Mapping(source = "status", target = "upstreamOrderStatus", qualifiedByName = "mapUpstreamOrderStatus"),
    })
    UpstreamOrderQueryDTO toQueryDTO(JiazhiyiOrderResponse jiazhiyiOrderResponse);


    @Mappings({
            @Mapping(source = "productId", target = "goodsId"),
            @Mapping(source = "upstreamOrderNo", target = "userOrderId"),
            @Mapping(source = "rechargeAccount", target = "userName"),
            @Mapping(source = "productName", target = "gameName"),
            @Mapping(source = "quantity", target = "goodsNum"),
    })
    JiazhiyiDirectRechargeRequest toRechargeRequest(DirectRechargeOrderDTO directRechargeOrderDTO);

    //    0-充值成功/1-充值中/2-充值失败/3-未处理
    @Named("mapUpstreamOrderStatus")
    default Integer mapUpstreamOrderStatus(String status) {
        //订单状态（0-充值成功/1-充值中/2-充值失败/3-未处理）
        return switch (status) {
            case "01" -> 0;
            case "02", "04" -> 2;
            case "03" -> 1;
            default -> throw exception(JIAZHIYI_RESPONSE_ORDER_STATUS_ERROR);
        };
    }

    @AfterMapping
    default void setCompleteTime(@MappingTarget UpstreamOrderQueryDTO dto) {
        dto.setCompleteTime(LocalDateTime.now());
    }
}
