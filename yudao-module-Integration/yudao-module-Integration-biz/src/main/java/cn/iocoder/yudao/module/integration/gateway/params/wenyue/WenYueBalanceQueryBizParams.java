package cn.iocoder.yudao.module.integration.gateway.params.wenyue;

import cn.iocoder.yudao.module.integration.enums.WenYueMethodType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import static cn.iocoder.yudao.module.integration.enums.WenYueMethodType.BALANCE_QUERY;


@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WenYueBalanceQueryBizParams implements WenYueApiBizParams {
    @Schema(description = "商户编号（非登录名），平台方提供", example = "merchant123", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("userid")
    private String userId;

    @Schema(description = "签名验证摘要串", example = "abcdef123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sign")
    private String sign;

    @JsonIgnore
    public String getLogContent() {
        return "问月直充接口订单参数:\n" +
                "userid='" + userId + "'\n" +
                "sign='" + sign + "'";
    }

    @Override
    public WenYueMethodType getMethodType() {
        return BALANCE_QUERY;
    }
}
