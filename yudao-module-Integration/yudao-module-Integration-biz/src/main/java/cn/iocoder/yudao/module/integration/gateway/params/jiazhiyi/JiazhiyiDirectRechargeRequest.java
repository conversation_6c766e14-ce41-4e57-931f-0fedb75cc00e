package cn.iocoder.yudao.module.integration.gateway.params.jiazhiyi;

import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.TreeMap;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JiazhiyiDirectRechargeRequest {
    // 必填字段
    @JiazhiyiParam(value = "businessId", order = 1, required = true)
    private String businessId;      // 商户号，由SUP系统分配
    @JiazhiyiParam(value = "userOrderId", order = 2, required = true)
    private String userOrderId;     // 订单号，商户自定义，最大长度不超过32位
    @JiazhiyiParam(value = "goodsId", order = 3, required = true)
    private String goodsId;         // 商品编号，SUP系统商品编号
    @JiazhiyiParam(value = "userName", order = 4, required = true)
    private String userName;        // 充值账号
    @JiazhiyiParam(value = "gameName", order = 5, required = false)
    private String gameName;        // 商品名称
    @JiazhiyiParam(value = "goodsNum", order = 8, required = true)
    private Integer goodsNum;       // 充值数量，单位是元
    @JiazhiyiParam(value = "noticeUrl", order = 9, required = true)
    private String noticeUrl;       // 异步通知地址

    // 选填字段
    @JiazhiyiParam(value = "gameAcct", order = 10)
    private String gameAcct;        // 游戏账号
    @JiazhiyiParam(value = "gameArea", order = 11)
    private String gameArea;        // 游戏区
    @JiazhiyiParam(value = "gameSrv", order = 12)
    private String gameSrv;         // 游戏服
    @JiazhiyiParam(value = "orderIp", order = 13)
    private String orderIp;         // 充值ip

    // 签名
    @JiazhiyiParam(value = "sign", order = 14, required = true)
    private String sign;            // MD5签名

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class ParamInfo {
        String name;
        String value;
        boolean required;
        int order;
    }

    public void generateAndSetSign(String key) {
        String signStr = this.businessId + this.userOrderId + this.goodsId + this.goodsNum + (this.orderIp != null ? this.orderIp : "") + key;
        this.sign = MD5Utils.MD5(signStr);
    }

    public String buildRequestParams() {
        // 收集所有带注解的字段
        Map<Integer, ParamInfo> params = collectParams(this);
        // 验证必填参数
        validateRequiredParams(params);
        // 构建参数字符串
        return buildParamString(params);
    }

    private Map<Integer, ParamInfo> collectParams(JiazhiyiDirectRechargeRequest request) {
        Map<Integer, ParamInfo> params = new TreeMap<>();
        Class<?> clazz = request.getClass();
        while (clazz != null) {
            for (Field field : clazz.getDeclaredFields()) {
                JiazhiyiParam annotation = field.getAnnotation(JiazhiyiParam.class);
                if (annotation == null) continue;

                try {
                    field.setAccessible(true);
                    Object value = field.get(request);

                    ParamInfo paramInfo = new ParamInfo();
                    paramInfo.name = annotation.value().isEmpty() ? field.getName() : annotation.value();
                    paramInfo.value = convertToString(value, annotation.defaultValue());
                    paramInfo.required = annotation.required();
                    paramInfo.order = annotation.order();

                    params.put(paramInfo.order, paramInfo);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Failed to access field: " + field.getName(), e);
                }
            }
            clazz = clazz.getSuperclass();
        }
        return params;
    }

    private void validateRequiredParams(Map<Integer, ParamInfo> params) {
        for (ParamInfo param : params.values()) {
            if (param.required && (param.value == null || param.value.isEmpty())) {
                throw new IllegalArgumentException("参数['" + param.name + "' ]不能为空");
            }
        }
    }

    private String buildParamString(Map<Integer, ParamInfo> params) {
        StringBuilder sb = new StringBuilder();

        for (ParamInfo param : params.values()) {
            if (param.value == null || param.value.isEmpty()) {
                continue; // 跳过空值参数
            }
            if (!sb.isEmpty()) {
                sb.append("&");
            }
            sb.append(param.name).append("=").append(param.value);
        }
        return "?" + sb;
    }

    private String convertToString(Object value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        return value.toString();
    }
}
