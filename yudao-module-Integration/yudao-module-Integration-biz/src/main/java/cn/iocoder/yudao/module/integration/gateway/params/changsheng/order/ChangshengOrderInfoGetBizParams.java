package cn.iocoder.yudao.module.integration.gateway.params.changsheng.order;

import cn.iocoder.yudao.module.integration.enums.ChangshengMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.changsheng.ChangshengApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChangshengOrderInfoGetBizParams implements ChangshengApiBizParams {

    @Schema(description = "外部订单号", example = "201906281030191013526", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("customer_order_no")
    private String customerOrderNo;

    @Override
    public ChangshengMethodType getMethodType() {
        return ChangshengMethodType.ORDER_INFO_GET;
    }
}
