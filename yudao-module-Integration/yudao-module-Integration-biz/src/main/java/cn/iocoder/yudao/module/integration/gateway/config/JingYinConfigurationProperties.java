package cn.iocoder.yudao.module.integration.gateway.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "supplier.jingyin.api")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JingYinConfigurationProperties {
    private String callbackUrl;
    private String interfaceClassName;
}
