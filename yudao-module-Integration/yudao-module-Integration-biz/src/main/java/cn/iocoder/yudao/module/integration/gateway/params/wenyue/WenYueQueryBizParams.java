package cn.iocoder.yudao.module.integration.gateway.params.wenyue;

import cn.iocoder.yudao.module.integration.enums.WenYueMethodType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WenYueQueryBizParams implements WenYueApiBizParams {
    @Schema(description = "商户编号（非登录名），平台方提供", example = "merchant123", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("userid")
    private String userId;

    @Schema(description = "代理商系统订单号（流水号）要求不可重复，每笔只可同时提交一次", example = "ORDER123456789", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sporderid")
    private String sporderId;

    @Schema(description = "签名验证摘要串", example = "abcdef123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sign")
    private String sign;

    @JsonIgnore
    public String getLogContent() {
        return "问月直充接口订单参数:\n" +
                "userid='" + userId + "'\n" +
                "sporderid='" + sporderId + "'\n" +
                "sign='" + sign + "'";
    }

    @Override
    public WenYueMethodType getMethodType() {
        return WenYueMethodType.ORDER_INFO_GET;
    }
}
