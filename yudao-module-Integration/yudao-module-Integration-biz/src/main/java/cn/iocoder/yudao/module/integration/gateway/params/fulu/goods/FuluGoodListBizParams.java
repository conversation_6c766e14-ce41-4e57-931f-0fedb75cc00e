package cn.iocoder.yudao.module.integration.gateway.params.fulu.goods;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluGoodListBizParams implements FuluApiBizParams {

    @Schema(description = "商品编号", example = "10000001", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("product_id")
    private Integer productId;

    @Schema(description = "商品名称", example = "ww腾讯Q币直充一元", maxLength = 200, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("product_name")
    private String productName;

    @Schema(description = "库存类型：卡密、直充", example = "直充", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("product_type")
    private String productType;

    @Schema(description = "面值", example = "1.0000", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("face_value")
    private Double faceValue;

    @Schema(description = "商品分类Id（一级）", example = "219", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("first_category_id")
    private Integer firstCategoryId;

    @Schema(description = "商品分类Id（二级）", example = "708", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("second_category_id")
    private Integer secondCategoryId;

    @Schema(description = "商品分类Id（三级）", example = "709", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("third_category_id")
    private Integer thirdCategoryId;

    @Override
    public FuluMethodType getMethodType() {
        return FuluMethodType.GOOD_LIST;
    }
}
