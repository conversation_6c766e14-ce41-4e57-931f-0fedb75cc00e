package cn.iocoder.yudao.module.integration.gateway.mapper.youkayun;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.params.youkayun.YoukayunRechargeRequest;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunOrderQueryResponse;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_ORDER_STATUS_ERROR;

@Mapper
public interface YoukayunOrderMapper {
    YoukayunOrderMapper MAPPER = Mappers.getMapper(YoukayunOrderMapper.class);

    @Mappings({
            @Mapping(source = "status", target = "upstreamOrderStatus", qualifiedByName = "mapUpstreamOrderStatus"),
    })
    UpstreamOrderQueryDTO toQueryDTO(YoukayunOrderQueryResponse.YoukayunOrderQueryResponseData data);

    @Mappings({
            @Mapping(target = "goodsid", source = "productId"),
            @Mapping(target = "outOrderNo", source = "upstreamOrderNo"),
            @Mapping(target = "quantity", source = "quantity"),
            @Mapping(target = "accountName", source = "rechargeAccount")
    })
    YoukayunRechargeRequest toRechargeRequest(DirectRechargeOrderDTO directRechargeOrderDTO);

    @Named("mapUpstreamOrderStatus")
    default Integer mapUpstreamOrderStatus(Integer status) {
        //0-充值成功/1-充值中/2-充值失败/3-未处理
        return switch (status) {
            case 3 -> 0;
            case 2 -> 1;
            case 1 -> 3;
            case 4, 5 -> 2;
            default -> throw exception(YIQIDA_RESPONSE_ORDER_STATUS_ERROR);
        };
    }

    @AfterMapping
    default void setFinishTime(@MappingTarget UpstreamOrderQueryDTO dto) {
        dto.setCompleteTime(LocalDateTime.now());
    }
}
