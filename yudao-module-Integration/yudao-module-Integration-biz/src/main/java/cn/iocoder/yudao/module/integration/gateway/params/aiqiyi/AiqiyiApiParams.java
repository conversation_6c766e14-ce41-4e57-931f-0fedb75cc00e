package cn.iocoder.yudao.module.integration.gateway.params.aiqiyi;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiqiyiApiParams {

    @Schema(name = "来源渠道", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("platform")
    private String platform;

    @Schema(name = "手机号MD5值", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("signature")
    private String signature;

    @Schema(name = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("mobile")
    private String mobile;

    @Schema(name = "每次请求唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("req_id")
    private String reqId;

    @Schema(name = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("order_code")
    private String orderCode;
}
