package cn.iocoder.yudao.module.integration.gateway.mapper.wenyue;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.params.wenyue.WenYueDirectRechargeOrderBizParams;
import cn.iocoder.yudao.module.integration.gateway.response.wenyue.WenYueXmlResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WenYueOrderMapper {
    WenYueOrderMapper MAPPER = Mappers.getMapper(WenYueOrderMapper.class);

    @Mappings({
            @Mapping(source = "quantity", target = "num"),
            @Mapping(source = "rechargeAccount", target = "account"),
            @Mapping(source = "upstreamOrderNo", target = "sporderId"),
            @Mapping(source = "chargeIp", target = "userIp"),
    })
    WenYueDirectRechargeOrderBizParams toParams(DirectRechargeOrderDTO directRechargeOrderDTO);

    @Mappings({
            @Mapping(source = "orderId", target = "externalOrderNo"),
            @Mapping(source = "spOrderId", target = "upstreamOrderNo"),
    })
    DirectChargeResultDTO toResultFromXml(WenYueXmlResponse wenYueXmlResponse);
}
