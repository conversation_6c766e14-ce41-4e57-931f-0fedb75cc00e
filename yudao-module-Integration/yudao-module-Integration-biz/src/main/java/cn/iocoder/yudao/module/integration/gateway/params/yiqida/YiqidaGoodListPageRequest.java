package cn.iocoder.yudao.module.integration.gateway.params.yiqida;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.ALWAYS)
public class YiqidaGoodListPageRequest implements SignableRequest {
    @JsonProperty("page")
    private Integer page;
    @JsonProperty("size")
    private Integer size;
}
