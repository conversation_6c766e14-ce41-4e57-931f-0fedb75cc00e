package cn.iocoder.yudao.module.integration.gateway.response.yiqida;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YiqidaQueryResponse {
    /**
     * 状态码（200表示成功）
     */
    private Integer code;

    /**
     * 状态描述
     */
    private String msg;

    /**
     * 总数（分页场景使用）
     */
    private Integer sum;

    /**
     * 业务数据（泛型承载差异化结构）
     */
    private QueryData data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class QueryData {
        /**
         * 亿奇达订单号
         */
        private String orderNo;

        /**
         * 下单IP地址
         */
        private String orderIP;

        /**
         * 支付时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        private String payTime;

        /**
         * 支付类型（需文档补充具体值含义）
         */
        private Integer payType;

        /**
         * 订单完成时间（未完成时为null）
         */
        private String sendTime;

        /**
         * 交付方式：3-官方直充 6-卡密
         */
        private Integer virtualDelivery;

        /**
         * 发货类型（需文档补充具体值含义）
         */
        private Integer sendType;

        /**
         * 实际支付金额（单位：元）
         */
        private Double payPrice;

        /**
         * 订单状态（5表示成功，需文档补充其他状态值）
         */
        private Integer status;

        /**
         * 充值模板数据（JSON字符串）
         */
        private String templateText;

        /**
         * 充值模板ID
         */
        private Integer templateId;

        /**
         * 卡密数据列表（非卡密订单时为null）
         */
        @Builder.Default
        private List<CarmiItem> carmi = new ArrayList<>();

        /**
         * 商家备注（如充值成功提示）
         */
        private String sellerMessage;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CarmiItem {
        /**
         * 卡号/券码
         */
        private String number;

        /**
         * 到期时间
         */
        private String endTime;

        /**
         * 密码（卡密类商品）
         */
        private String pwd;

        /**
         * 链接（直充类商品）
         */
        private String url;

        /**
         * 卡密类型：1-卡密 2-图片
         */
        private String stockType;
    }
}
