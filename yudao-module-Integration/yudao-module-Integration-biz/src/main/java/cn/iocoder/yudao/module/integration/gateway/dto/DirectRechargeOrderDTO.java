package cn.iocoder.yudao.module.integration.gateway.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 这个类暂时和FuluDirectRechargeBizParams完全一致，下单需要构建required的字段
 */
@Schema(description = "直充数据结构")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DirectRechargeOrderDTO {
    @Schema(description = "商品编号", example = "********", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productId;

    //TODO 需要set值
    @Schema(description = "商品名称", example = "********", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    @Schema(description = "外部订单号(使用upstream order Id, 每次自动生成)", example = "201906281030191013526", requiredMode = Schema.RequiredMode.REQUIRED)
    private String upstreamOrderNo;

    @Schema(description = "充值账号", example = "888888", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeAccount;

    //TODO 需要set值
    @Schema(description = "充值账号类型", example = "QQ", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeAccountType;

    @Schema(description = "购买数量", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer quantity;

    @Schema(description = "充值游戏名称", example = "三国群英传", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargeGameName;

    @Schema(description = "充值游戏区", example = "电信一区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargeGameRegion;

    @Schema(description = "充值游戏服", example = "逐鹿中原", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargeGameSrv;

    @Schema(description = "充值类型", example = "Q币", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargeType;

    @Schema(description = "充值密码", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargePassword;

    @Schema(description = "下单真实Ip", example = "*************", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargeIp;

    @Schema(description = "联系QQ", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String contactQq;

    @Schema(description = "联系电话", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String contactTel;

    @Schema(description = "剩余数量", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer remainingNumber;

    @Schema(description = "充值游戏角色", example = "赵云", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String chargeGameRole;

    @Schema(description = "外部销售价", example = "1.00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Double customerPrice;

    @Schema(description = "店铺类型", example = "淘宝", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String shopType;

    @Schema(description = "透传字段", example = "C564982164", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String externalBizId;
}
