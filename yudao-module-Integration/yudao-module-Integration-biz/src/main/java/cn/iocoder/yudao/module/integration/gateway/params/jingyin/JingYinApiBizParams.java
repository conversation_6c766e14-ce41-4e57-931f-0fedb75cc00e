package cn.iocoder.yudao.module.integration.gateway.params.jingyin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JingYinApiBizParams {
    @Schema(description = "请求类型", example = "recharge", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty
    private String action;

    @Schema(description = "请求时间", example = "**************", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 14)
    @JsonProperty
    private String requestTime;

    @Schema(description = "商户账号", example = "yourMerchantAccount", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 50)
    @JsonProperty
    private String merAccount;

    //12表示话费充值；13表示其他数字权益产品
    @Schema(description = "业务类型", example = "12", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty
    private Integer businessType;

    @Schema(description = "商户订单号", example = "*****************", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 30)
    @JsonProperty
    private String merOrderNo;

    @Schema(description = "充值账号", example = "***********", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 50)
    @JsonProperty
    private String rechargeAccount;

    //businessType为13时必传，1078为直充测试产品编号；1079为卡密测试产品编号
    @Schema(description = "产品编号", example = "1078", requiredMode = Schema.RequiredMode.NOT_REQUIRED, maxLength = 10)
    @JsonProperty
    private String productId;

    @Schema(description = "充值量", example = "100", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty
    private Long rechargeValue;

    @Schema(description = "用户IP", example = "127.0.0.1", requiredMode = Schema.RequiredMode.NOT_REQUIRED, maxLength = 15)
    @JsonProperty
    private String customerIp;

    @Schema(description = "回调地址", example = "https://yourdomain.com/notify", requiredMode = Schema.RequiredMode.NOT_REQUIRED, maxLength = 200)
    @JsonProperty
    private String notifyUrl;

    //V2通知方式：0、表单方式；1、JSON方式，默认0；
    @Schema(description = "通知方式", example = "0", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty
    private Integer notifyType = 1; // 1：通知方式为JSON

    @Schema(description = "签名", example = "abcdef123456", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64)
    @JsonProperty
    private String sign;

    @JsonIgnore
    public String getLogContent() {
        return "上游直充接口订单参数:\n" +
                "action='" + action + "'\n" +
                "requestTime='" + requestTime + "'\n" +
                "merAccount='" + merAccount + "'\n" +
                "businessType=" + businessType + "\n" +
                "merOrderNo='" + merOrderNo + "'\n" +
                "rechargeAccount='" + rechargeAccount + "'\n" +
                "productId='" + productId + "'\n" +
                "rechargeValue=" + rechargeValue + "\n" +
                "customerIp='" + customerIp + "'\n" +
                "notifyUrl='" + notifyUrl + "'\n" +
                "notifyType=" + notifyType + "\n" +
                "sign='" + sign + "'\n";
    }

}