package cn.iocoder.yudao.module.integration.gateway.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "supplier.youkayun.api")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YoukayunConfigurationProperties {
    private String interfaceClassName;
    private String callbackUrl;
}
