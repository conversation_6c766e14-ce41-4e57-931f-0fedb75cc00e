package cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@JacksonXmlRootElement(localName = "root")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JiazhiyiRechargeResponse {

    @JacksonXmlProperty(localName = "result")
    private String result;

    @JacksonXmlProperty(localName = "mes")
    private String message;

}
