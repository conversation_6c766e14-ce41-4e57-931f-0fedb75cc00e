package cn.iocoder.yudao.module.integration.gateway.mapper.fulu;

import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluProductInfoDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.fulu.FuluProductListItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_RESPONSE_GOOD_SALES_STATUS_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.FULU_RESPONSE_GOOD_TYPE_ERROR;

@Mapper
public interface FuluProductMapper {
    FuluProductMapper MAPPER = Mappers.getMapper(FuluProductMapper.class);

    @Mappings({
            @Mapping(source = "productId", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "productName", target = "name"),
            @Mapping(source = "productType", target = "type", qualifiedByName = "fuluProductTypeToPlatformType"),
            @Mapping(source = "faceValue", target = "guidePrice", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "purchasePrice", target = "price", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "salesStatus", target = "status", qualifiedByName = "salesStatusToStatus")
    })
    SupplierProductDTO toPlatformProduct(FuluProductListItemDTO fuluProductListItemDTO);


    @Mappings({
            @Mapping(source = "productId", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "productName", target = "name"),
            @Mapping(source = "productType", target = "type", qualifiedByName = "fuluProductTypeToPlatformType"),
            @Mapping(source = "faceValue", target = "guidePrice", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "purchasePrice", target = "price", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "salesStatus", target = "status", qualifiedByName = "salesStatusToStatus"),
            @Mapping(source = "fourCategoryIcon", target = "image")
    })
    SupplierProductDTO toPlatformProduct(FuluProductInfoDTO fuluProductInfoDTO);

    @Named("longToString")
    default String longToString(Long value) {
        return String.valueOf(value);
    }

    @Named("salesStatusToStatus")
    default Integer salesStatusToStatus(String salesStatus) {
        return switch (salesStatus) {
            case "上架" -> 1;
            case "下架" -> 2;
            case "维护" -> 3;
            case "库存维护" -> 4;
            default -> throw exception(FULU_RESPONSE_GOOD_SALES_STATUS_ERROR);
        };
    }

    @Named("fuluProductTypeToPlatformType")
    default Integer fuluProductTypeToPlatformType(String productType) {
        if (productType.equals("直充")) {
            return 1;
        } else if (productType.equals("卡密")) {
            return 2;
        } else {
            throw exception(FULU_RESPONSE_GOOD_TYPE_ERROR);
        }
    }

    @Named("doubleToBigDecimal")
    default BigDecimal doubleToBigDecimal(Double value) {
        return BigDecimal.valueOf(value);
    }
}
