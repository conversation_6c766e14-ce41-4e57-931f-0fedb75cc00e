package cn.iocoder.yudao.module.integration.gateway.mapper.youkayun;

import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunGoodInfoResponse;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunGoodListResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_RESPONSE_GOOD_SALES_STATUS_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_RESPONSE_GOOD_TYPE_ERROR;

@Mapper
public interface YoukayunProductMapper {
    YoukayunProductMapper MAPPER = Mappers.getMapper(YoukayunProductMapper.class);

    @Mappings({
            @Mapping(source = "id", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "goodsName", target = "name"),
            @Mapping(source = "goodsType", target = "type", qualifiedByName = "youkayunProductTypeToPlatformType"),
            @Mapping(source = "faceValue", target = "guidePrice", qualifiedByName = "stringToBigDecimal"),
            @Mapping(source = "goodsPrice", target = "price", qualifiedByName = "stringToBigDecimal"),
            @Mapping(source = "status", target = "status", qualifiedByName = "salesStatusToStatus"),
    })
    SupplierProductDTO toPlatformProduct(YoukayunGoodInfoResponse.YoukayunGoodInfo youkayunGoodInfo);

    @Mappings({
            @Mapping(source = "id", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "goodsName", target = "name"),
            @Mapping(source = "goodsType", target = "type", qualifiedByName = "youkayunProductTypeToPlatformType"),
            @Mapping(source = "goodsPrice", target = "price", qualifiedByName = "stringToBigDecimal"),
            @Mapping(source = "status", target = "status", qualifiedByName = "salesStatusToStatus"),
    })
    SupplierProductDTO toPlatformProduct(YoukayunGoodListResponse.YoukayunGoodBriefData youkayunGoodBriefData);

    @Named("longToString")
    default String longToString(Long value) {
        return String.valueOf(value);
    }

    @Named("youkayunProductTypeToPlatformType")
    default Integer youkayunProductTypeToPlatformType(Integer value) {
        if (value == 1) {
            return 2;
        }
        if (value == 2) {
            return 1;
        }
        throw exception(YOUKAYUN_RESPONSE_GOOD_TYPE_ERROR);
    }

    @Named("stringToBigDecimal")
    default BigDecimal stringToBigDecimal(String value) {
        return BigDecimal.valueOf(Double.parseDouble(value));
    }

    @Named("salesStatusToStatus")
    default Integer salesStatusToStatus(Integer value) {
        //1 - 上架 3 - 下架
        if (value == 1) {
            return 1;
        }
        if (value == 3) {
            return 2;
        }
        throw exception(YOUKAYUN_RESPONSE_GOOD_SALES_STATUS_ERROR);
    }
}
