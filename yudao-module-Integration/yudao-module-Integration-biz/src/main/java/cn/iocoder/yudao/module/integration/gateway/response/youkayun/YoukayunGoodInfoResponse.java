package cn.iocoder.yudao.module.integration.gateway.response.youkayun;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class YoukayunGoodInfoResponse {

    private Integer code;

    private String msg;

    private YoukayunGoodInfo data;

    @Data
    public static class YoukayunGoodInfo {
        /*
         * 商品id
         */
        private Integer id;
        /*
         * 商品名称
         */
        @JsonProperty("goods_name")
        private String goodsName;
        /*
         * 商品类型 1卡密商品 2代充商品
         */
        @JsonProperty("goods_type")
        private Integer goodsType;
        /*
         * 	起售数量
         */
        @JsonProperty("start_count")
        private Integer startCount;
        /*
         * 	最多购买数量
         */
        @JsonProperty("end_count")
        private Integer endCount;
        /**
         * 下单数量必须是此数值的整数倍
         */
        @JsonProperty("rate_count")
        private Integer rateCount;
        /**
         * 商品面值
         */
        @JsonProperty("face_value")
        private String faceValue;
        /**
         * 商品详情
         */
        @JsonProperty("goods_info")
        private String goodsInfo;
        /**
         * 库存数量
         */
        @JsonProperty("stock_num")
        private Integer stockNum;
        /**
         * 商品价格
         */
        @JsonProperty("goods_price")
        private String goodsPrice;
        /**
         * 1为上架 3为下架
         */
        private Integer status;
    }

}
