package cn.iocoder.yudao.module.integration.gateway.response.jiazhiyi;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@JacksonXmlRootElement(localName = "root")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JiazhiyiOrderResponse {
    @JacksonXmlProperty(localName = "businessId")
    private String businessId;

    @JacksonXmlProperty(localName = "userOrderId")
    private String userOrderId;

    @JacksonXmlProperty(localName = "status")
    private String status; // 示例中值为 "01"，可以是字符串或枚举类型

    @JacksonXmlProperty(localName = "payoffPriceTotal")
    private BigDecimal payoffPriceTotal; // 或使用 BigDecimal 更精确

    @JacksonXmlProperty(localName = "mes")
    private String message;

    @JacksonXmlProperty(localName = "kmInfo")
    private String kmInfo; // 当前 XML 中为空，但保留字段

    @JacksonXmlProperty(localName = "sign")
    private String sign;

    @JacksonXmlProperty(localName = "voucherNo")
    private String voucherNo;
}
