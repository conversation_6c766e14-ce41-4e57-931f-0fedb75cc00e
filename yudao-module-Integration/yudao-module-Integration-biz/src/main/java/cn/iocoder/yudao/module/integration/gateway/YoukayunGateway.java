package cn.iocoder.yudao.module.integration.gateway;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.module.integration.client.youkayun.YoukayunFeignClient;
import cn.iocoder.yudao.module.integration.client.youkayun.YoukayunFeignClientFactory;
import cn.iocoder.yudao.module.integration.enums.YoukayunMethodType;
import cn.iocoder.yudao.module.integration.gateway.config.YoukayunConfigurationProperties;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierBalanceDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.mapper.youkayun.YoukayunOrderMapper;
import cn.iocoder.yudao.module.integration.gateway.mapper.youkayun.YoukayunProductMapper;
import cn.iocoder.yudao.module.integration.gateway.params.youkayun.YoukayunBaseRequest;
import cn.iocoder.yudao.module.integration.gateway.params.youkayun.YoukayunGoodInfoRequest;
import cn.iocoder.yudao.module.integration.gateway.params.youkayun.YoukayunGoodsPageRequest;
import cn.iocoder.yudao.module.integration.gateway.params.youkayun.YoukayunOrderQueryRequest;
import cn.iocoder.yudao.module.integration.gateway.params.youkayun.YoukayunRechargeRequest;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunBalanceQueryResponse;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunGoodInfoResponse;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunGoodListResponse;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunOrderQueryResponse;
import cn.iocoder.yudao.module.integration.gateway.response.youkayun.YoukayunRechargeResponse;
import cn.iocoder.yudao.module.integration.service.IntegrationOrderLogService;
import cn.iocoder.yudao.module.integration.service.OrderLogType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;
import org.springframework.util.MultiValueMap;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_CALL_BACK_BALANCE_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_CALL_BACK_ORDER_QUERY_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_RESPONSE_PARSE_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_RESPONSE_RECHARGE_ORDER_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_RESPONSE_RETURN_GOODS_FAILED;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YOUKAYUN_RESPONSE_RETURN_GOOD_INFO_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_FAILED;
import static cn.iocoder.yudao.module.integration.service.OrderLogType.UPSTREAM_ORDER_CALL_SUCCESS;

@RequiredArgsConstructor
@Repository
@Slf4j
public class YoukayunGateway implements SupplierGateway {

    private final YoukayunConfigurationProperties youkayunConfigurationProperties;

    private final YoukayunFeignClientFactory youkayunFeignClientFactory;

    private final IntegrationOrderLogService integrationOrderLogService;

    @Override
    public String getSupplierInterfaceClassName() {
        return youkayunConfigurationProperties.getInterfaceClassName();
    }

    @Override
    public List<SupplierProductDTO> getSupplierProductList(SupplierDTO supplierDTO) {
        List<YoukayunGoodListResponse.YoukayunGoodBriefData> allData = getAllYiqidaGoodResponseData(supplierDTO);
        return allData.stream()
                .filter(yiqidaGoodResponseData -> yiqidaGoodResponseData.getStatus().equals(1)) //商品列表只保留上架状态的商品
                .map(YoukayunProductMapper.MAPPER::toPlatformProduct)
                .map(item -> item.setSupplierId(supplierDTO.getId()))
                .toList();
    }

    @NotNull
    private List<YoukayunGoodListResponse.YoukayunGoodBriefData> getAllYiqidaGoodResponseData(SupplierDTO supplierDTO) {
        List<YoukayunGoodListResponse.YoukayunGoodBriefData> allData = new ArrayList<>();
        int currentPage = 1;
        Integer totalSum = null; // 记录总记录数
        int totalFetched = 0;    // 已获取总数

        while (true) {
            YoukayunGoodsPageRequest request = YoukayunGoodsPageRequest.builder()
                    .page(currentPage)
                    .limit(100)
                    .userid(supplierDTO.getChannelAccount())
                    .build();

            MultiValueMap<String, String> multiValueMap = request.generateSignedFormData(supplierDTO.getSigningKey());
            YoukayunFeignClient client = youkayunFeignClientFactory.createClient(
                    supplierDTO.getUrl() + YoukayunMethodType.GOOD_LIST.getMethodUri()
            );

            YoukayunGoodListResponse response;
            try {
                response = client.queryGoodList(multiValueMap);
            } catch (Exception e) {
                log.error("优卡云商品列表请求异常 page={}", currentPage, e);
                throw exception(YOUKAYUN_RESPONSE_RETURN_GOODS_FAILED, ExceptionUtils.getMessage(e));
            }

            // 响应有效性检查
            if (isResponseFailed(response)) {
                log.warn("优卡云商品列表异常响应 page={}, response={}", currentPage, response);
                throw exception(YOUKAYUN_RESPONSE_RETURN_GOODS_FAILED, response != null ? response.getMsg() : "空响应");
            }

            // 首次请求记录总数
            if (totalSum == null && response.getData().getCount() != null) {
                totalSum = response.getData().getCount();
                log.info("总记录数 sum={}", totalSum);
            }

            List<YoukayunGoodListResponse.YoukayunGoodBriefData> currentData = response.getData().getData();
            if (CollectionUtil.isEmpty(currentData)) {
                log.info("优卡云遇到空数据页，停止翻页 page={}", currentPage);
                break;
            }

            allData.addAll(currentData);
            totalFetched += currentData.size();

            // 终止条件判断
            boolean shouldContinue = (totalSum == null) ?
                    !currentData.isEmpty() :  // 未获取总数时，只要本页有数据就继续
                    (totalFetched < totalSum); // 已知总数时，比较已获取量

            if (!shouldContinue) {
                log.info("优卡云分页终止 totalSum={}, totalFetched={}", totalSum, totalFetched);
                break;
            }

            currentPage++;
        }
        return allData;
    }

    private boolean isResponseFailed(YoukayunGoodListResponse response) {
        return response == null ||
                response.getCode() != 1000 ||
                response.getData() == null; // 允许空列表（空列表是合法响应）
    }

    @Override
    public SupplierProductDTO getSupplierProduct(String productId, SupplierDTO supplierDTO) {
        YoukayunGoodInfoRequest request = YoukayunGoodInfoRequest.builder().goodsid(productId).userid(supplierDTO.getChannelAccount()).build();
        MultiValueMap<String, String> multiValueMap = request.generateSignedFormData(supplierDTO.getSigningKey());
        YoukayunFeignClient client = youkayunFeignClientFactory.createClient(supplierDTO.getUrl() + YoukayunMethodType.GOOD_INFO.getMethodUri());
        YoukayunGoodInfoResponse youkayunGoodInfoResponse;
        try {
            youkayunGoodInfoResponse = client.queryGoodInfo(multiValueMap);
        } catch (Exception e) {
            log.error("优卡云获取商品详情请求异常", e);
            throw exception(YOUKAYUN_RESPONSE_RETURN_GOOD_INFO_FAILED, ExceptionUtils.getMessage(e));
        }
        if (youkayunGoodInfoResponse.getCode() != 1000) {
            throw exception(YOUKAYUN_RESPONSE_RETURN_GOOD_INFO_FAILED, youkayunGoodInfoResponse.getMsg());
        }
        SupplierProductDTO platformProduct = YoukayunProductMapper.MAPPER.toPlatformProduct(youkayunGoodInfoResponse.getData());
        platformProduct.setSupplierId(supplierDTO.getId());
        return platformProduct;
    }

    @Override
    @SneakyThrows
    public DirectChargeResultDTO directRecharge(SupplierDTO supplierDTO, DirectRechargeOrderDTO directRechargeOrderDTO, String orderNo) {
        log.info("进入优卡云直充,订单号为:[{}],直充对象为:[{}]", orderNo, directRechargeOrderDTO);
        YoukayunRechargeRequest youkayunRechargeRequest = YoukayunOrderMapper.MAPPER.toRechargeRequest(directRechargeOrderDTO);
        youkayunRechargeRequest.setUserid(supplierDTO.getChannelAccount());
        youkayunRechargeRequest.setCallBackUrl(youkayunConfigurationProperties.getCallbackUrl());
        MultiValueMap<String, String> multiValueMap = youkayunRechargeRequest.generateSignedFormData(supplierDTO.getSigningKey());
        log.info("优卡云直充接口，requestParams为:[{}]", youkayunRechargeRequest);
        YoukayunFeignClient client = youkayunFeignClientFactory.createClient(supplierDTO.getUrl() + YoukayunMethodType.ORDER_DIRECT_ADD.getMethodUri());
        YoukayunRechargeResponse youkayunRechargeResponse;
        try {
            youkayunRechargeResponse = client.recharge(multiValueMap);
            log.info("优卡云直充调用接口结果为:[{}]", youkayunRechargeResponse);
        } catch (Exception e) {
            log.error("优卡云直充调用接口发生异常.", e);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED, ExceptionUtils.getMessage(e));
            throw exception(YOUKAYUN_RESPONSE_RECHARGE_ORDER_ERROR, e);
        }
        if (!Objects.equals(youkayunRechargeResponse.getCode(), 1000)) {
            log.error("优卡云直充调用接口结果异常:[{}]", youkayunRechargeResponse);
            integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_FAILED,
                    String.format("request:[%s], response:[%s]", youkayunRechargeRequest, youkayunRechargeResponse));
            throw exception(YOUKAYUN_RESPONSE_RECHARGE_ORDER_ERROR, youkayunRechargeResponse.getMsg());
        }
        YoukayunRechargeResponse.YoukayunRechargeData youkayunRechargeData
                = Optional.ofNullable(youkayunRechargeResponse.parseSuccessData())
                .orElseThrow(() -> exception(YOUKAYUN_RESPONSE_PARSE_ERROR, youkayunRechargeResponse.toString()));
        DirectChargeResultDTO directChargeResultDTO = DirectChargeResultDTO.builder()
                .upstreamOrderNo(directRechargeOrderDTO.getUpstreamOrderNo())
                .externalOrderNo(youkayunRechargeData.getOrderSn())
                .supplierId(supplierDTO.getId())
                .build();
        integrationOrderLogService.insertOrderLog(orderNo, UPSTREAM_ORDER_CALL_SUCCESS,
                String.format("request:[%s], response:[%s]", youkayunRechargeRequest, youkayunRechargeResponse));
        return directChargeResultDTO;
    }

    @Override
    @SneakyThrows
    public UpstreamOrderQueryDTO queryOrder(String orderNo, LocalDateTime orderCreateTime, String upstreamOrderNo, SupplierDTO supplierDTO) {
        YoukayunOrderQueryRequest request = YoukayunOrderQueryRequest.builder().outerOrderId(upstreamOrderNo).userid(supplierDTO.getChannelAccount()).build();
        MultiValueMap<String, String> multiValueMap = request.generateSignedFormData(supplierDTO.getSigningKey());
        YoukayunFeignClient client = youkayunFeignClientFactory.createClient(supplierDTO.getUrl() + YoukayunMethodType.QUERY_ORDER.getMethodUri());
        YoukayunOrderQueryResponse youkayunOrderQueryResponse;
        try {
            youkayunOrderQueryResponse = client.queryOrder(multiValueMap);
        } catch (Exception e) {
            log.error("优卡云查询订单异常", e);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, ExceptionUtils.getMessage(e) + e);
            throw exception(YOUKAYUN_CALL_BACK_ORDER_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (youkayunOrderQueryResponse.getCode() != 1000) {
            log.error("优卡云查询订单异常:[{}]", youkayunOrderQueryResponse);
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_FAILED, youkayunOrderQueryResponse.getMsg());
            throw exception(YOUKAYUN_CALL_BACK_ORDER_QUERY_ERROR, youkayunOrderQueryResponse.getMsg());
        } else {
            YoukayunOrderQueryResponse.YoukayunOrderQueryResponseData data
                    = Optional.ofNullable(youkayunOrderQueryResponse.parseSuccessData())
                    .orElseThrow(() -> exception(YOUKAYUN_RESPONSE_PARSE_ERROR, youkayunOrderQueryResponse.toString()));
            UpstreamOrderQueryDTO queryDTO = YoukayunOrderMapper.MAPPER.toQueryDTO(data);
            queryDTO.setSupplierId(supplierDTO.getId());
            integrationOrderLogService.insertOrderLog(orderNo, OrderLogType.UPSTREAM_ORDER_QUERY_SUCCESS, String.format("request:[%s], response:[%s]", multiValueMap, youkayunOrderQueryResponse));
            return queryDTO;
        }

    }

    @Override
    public SupplierBalanceDTO balanceQuery(SupplierDTO supplierDTO) {
        YoukayunFeignClient client = youkayunFeignClientFactory.createClient(supplierDTO.getUrl() + YoukayunMethodType.BALANCE_QUERY.getMethodUri());
        YoukayunBaseRequest youkayunBaseRequest = YoukayunBaseRequest.builder().userid(supplierDTO.getChannelAccount()).build();
        MultiValueMap<String, String> multiValueMap = youkayunBaseRequest.generateSignedFormData(supplierDTO.getSigningKey());
        YoukayunBalanceQueryResponse youkayunBalanceQueryResponse;
        try {
            youkayunBalanceQueryResponse = client.balance(multiValueMap);
            log.info("优卡云查询余额结果yiqidaBalanceQueryResponse:[{}]", youkayunBalanceQueryResponse);
        } catch (Exception e) {
            log.error("优卡云查询余额异常", e);
            throw exception(YOUKAYUN_CALL_BACK_BALANCE_QUERY_ERROR, ExceptionUtils.getMessage(e));
        }
        if (youkayunBalanceQueryResponse.getCode() != 1000) {
            log.error("优卡云查询余额结果异常:[{}]", youkayunBalanceQueryResponse);
            throw exception(YOUKAYUN_CALL_BACK_BALANCE_QUERY_ERROR, youkayunBalanceQueryResponse.getMsg());
        }
        SupplierBalanceDTO result = SupplierBalanceDTO.builder()
                .balance(BigDecimal.valueOf(Double.parseDouble(youkayunBalanceQueryResponse.getData().getMoney())))
                .supplierId(supplierDTO.getId())
                .company(supplierDTO.getCompany())
                .serialNumber(supplierDTO.getSerialNumber())
                .channelAccount(supplierDTO.getChannelAccount())
                .build();
        log.info("优卡云查询余额结果result:[{}]", result);
        return result;
    }
}
