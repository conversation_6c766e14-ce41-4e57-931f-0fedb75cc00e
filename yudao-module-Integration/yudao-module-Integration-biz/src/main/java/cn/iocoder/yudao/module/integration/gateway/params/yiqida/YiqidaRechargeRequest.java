package cn.iocoder.yudao.module.integration.gateway.params.yiqida;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.ALWAYS)
public class YiqidaRechargeRequest implements SignableRequest {
    /**
     * 商品编号 （必填）
     */
    private String commodityId;

    /**
     * 第三方外部单号（通常用于关联业务系统） （非必填）
     */
    @JsonProperty("external_orderno")
    private String externalOrderNo;

    /**
     * 购买数量 （必填）
     */
    private Integer buyCount;

    /**
     * 备注信息（可选）
     */
    private String remark;

    /**
     * 异步回调地址（如无需回调可为空）
     */
    private String callbackUrl;

    /**
     * 外部销售价（单位：分/元等，根据业务确定）
     */
    @Builder.Default
    private Integer externalSellPrice = 0;

    /**
     * 充值模板参数（例如：URL编码的充值账号）
     */
    private List<String> template;
}
