package cn.iocoder.yudao.module.integration.gateway.params.yiqida;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.ALWAYS)
public class YiqidaOrderQueryRequest implements SignableRequest {
    /**
     * 外部订单号 upstreamOrderNo
     */
    @JsonProperty("external_orderno")
    private String externalOrderNo;

    /**
     * 亿奇达订单号
     */
    private String orderNo;

    /**
     * 代表查询订单状态的时间范围，单位（天），默认在近10天范围内查找该订单，要查全部日期请传0，该参数字段非必传针对高并发客户建议传小一点或者启用异步回调。
     */
    private String orderTime;
}
