package cn.iocoder.yudao.module.integration.gateway.params.jiazhiyi;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface JiazhiyiParam {
    /**
     * 参数名称（默认为字段名）
     */
    String value() default "";

    /**
     * 参数顺序（数值越小越靠前）
     */
    int order() default Integer.MAX_VALUE;

    /**
     * 是否必填参数
     */
    boolean required() default false;

    /**
     * 当值为null时的默认值
     */
    String defaultValue() default "";
}
