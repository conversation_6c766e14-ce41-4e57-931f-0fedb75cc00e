package cn.iocoder.yudao.module.integration.gateway.mapper.aiqiyi;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.params.aiqiyi.AiqiyiApiParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AiqiyiOrderMapper {
    AiqiyiOrderMapper MAPPER = Mappers.getMapper(AiqiyiOrderMapper.class);

    @Mappings({
            @Mapping(source = "upstreamOrderNo", target = "orderCode"),
            @Mapping(source = "upstreamOrderNo", target = "reqId"),
            @Mapping(source = "rechargeAccount", target = "mobile")
    })
    AiqiyiApiParams toParams(DirectRechargeOrderDTO directRechargeOrderDTO);
}
