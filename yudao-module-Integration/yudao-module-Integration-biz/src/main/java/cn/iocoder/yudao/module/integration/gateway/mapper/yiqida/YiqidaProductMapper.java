package cn.iocoder.yudao.module.integration.gateway.mapper.yiqida;

import cn.iocoder.yudao.module.integration.gateway.dto.SupplierProductDTO;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaGoodInfoResponse;
import cn.iocoder.yudao.module.integration.gateway.response.yiqida.YiqidaGoodListResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_GOOD_SALES_STATUS_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_GOOD_TYPE_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.YIQIDA_RESPONSE_PRE_SALE_TYPE_ERROR;

@Mapper
public interface YiqidaProductMapper {

    YiqidaProductMapper MAPPER = Mappers.getMapper(YiqidaProductMapper.class);

    @Mappings({
            @Mapping(source = "mainId", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "virtualDelivery", target = "type", qualifiedByName = "yiqidaProductTypeToPlatformType"),
            @Mapping(source = "guidePrice", target = "guidePrice", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "price", target = "price", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "status", target = "status", qualifiedByName = "salesStatusToStatus"),
            @Mapping(source = "isPreSale", target = "isPreSale", qualifiedByName = "isPreSaleMap"),
    })
    SupplierProductDTO toPlatformProduct(YiqidaGoodListResponse.YiqidaGoodResponseData yiqidaGoodResponseData);

    @Mappings({
            @Mapping(source = "mainId", target = "productId", qualifiedByName = "longToString"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "virtualDelivery", target = "type", qualifiedByName = "yiqidaProductTypeToPlatformType"),
            @Mapping(source = "guidePrice", target = "guidePrice", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "price", target = "price", qualifiedByName = "doubleToBigDecimal"),
            @Mapping(source = "status", target = "status", qualifiedByName = "salesStatusToStatus"),
            @Mapping(source = "isPreSale", target = "isPreSale", qualifiedByName = "isPreSaleMap"),
    })
    SupplierProductDTO toPlatformProduct(YiqidaGoodInfoResponse.YiqidaGoodResponseData yiqidaGoodResponseData);

    @Named("isPreSaleMap")
    default Boolean isPreSaleMap(Integer isPreSale) {
        if (isPreSale == 1) {
            return false;
        } else if (isPreSale == 2) {
            return true;
        } else {
            throw exception(YIQIDA_RESPONSE_PRE_SALE_TYPE_ERROR);
        }
    }

    @Named("longToString")
    default String longToString(Long value) {
        return String.valueOf(value);
    }

    @Named("yiqidaProductTypeToPlatformType")
    default Integer yiqidaProductTypeToPlatformType(Integer type) {
        if (type == 3) {
            return 1;
        } else if (type == 6) {
            return 2;
        } else {
            throw exception(YIQIDA_RESPONSE_GOOD_TYPE_ERROR);
        }
    }

    @Named("doubleToBigDecimal")
    default BigDecimal doubleToBigDecimal(Double value) {
        return BigDecimal.valueOf(value);
    }

    @Named("salesStatusToStatus")
    default Integer salesStatusToStatus(Integer salesStatus) {
        if (salesStatus == 1) {
            return 1;
        } else if (salesStatus == 2) {
            return 2;
        } else if (salesStatus == 4) {
            return 3;
        } else {
            throw exception(YIQIDA_RESPONSE_GOOD_SALES_STATUS_ERROR);
        }
    }
}
