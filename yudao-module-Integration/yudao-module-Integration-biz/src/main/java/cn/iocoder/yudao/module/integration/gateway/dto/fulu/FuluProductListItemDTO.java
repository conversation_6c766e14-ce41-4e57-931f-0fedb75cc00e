package cn.iocoder.yudao.module.integration.gateway.dto.fulu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 福禄商品列表返回商品信息的数据结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FuluProductListItemDTO {
    // 商品Id
    @JsonProperty("product_id")
    private Long productId;
    // 商品名称
    @JsonProperty("product_name")
    private String productName;
    // 库存类型：卡密、直充
    @JsonProperty("product_type")
    private String productType;
    // 面值
    @JsonProperty("face_value")
    private Double faceValue;
    // 单价（单位：元）
    @JsonProperty("purchase_price")
    private Double purchasePrice;
    // 销售状态
    @JsonProperty("sales_status")
    private String salesStatus;
    // 库存状态：断货、警报、充足（商品列表显示的商品都是非断货状态的商品，断货商品会自动下架）
    @JsonProperty("stock_status")
    private String stockStatus;
    // 商品模板Id，可能为空
    @JsonProperty("template_id")
    private String templateId;
    // 商品详情
    @JsonProperty("details")
    private String details;
}
