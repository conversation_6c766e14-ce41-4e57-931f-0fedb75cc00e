package cn.iocoder.yudao.module.integration.gateway.params.fulu.goods;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluGoodInfoBizParams implements FuluApiBizParams {

    @Schema(description = "商品编号", example = "10000001", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 10)
    @JsonProperty("product_id")
    private String productId;

    @Schema(description = "商品详情输出格式 0.纯文本 1.json格式。不传默认以纯文本方式显示", example = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("detail_format")
    private Integer detailFormat;

    @Override
    public FuluMethodType getMethodType() {
        return FuluMethodType.GOOD_INFO;
    }

    public FuluGoodInfoBizParams() {

    }

    public FuluGoodInfoBizParams(String productId) {
        this.productId = productId;
    }

    public FuluGoodInfoBizParams(String productId, Integer detailFormat) {
        this.productId = productId;
        this.detailFormat = detailFormat;
    }
}
