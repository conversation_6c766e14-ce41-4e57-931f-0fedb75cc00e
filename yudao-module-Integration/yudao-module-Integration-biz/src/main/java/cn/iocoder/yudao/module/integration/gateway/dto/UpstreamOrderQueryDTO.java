package cn.iocoder.yudao.module.integration.gateway.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpstreamOrderQueryDTO {
    @Schema(description = "外部订单编号, 供应商下单后返回", example = "19062837751058701652", requiredMode = Schema.RequiredMode.REQUIRED)
    private String externalOrderNo;

    @Schema(description = "上游订单编号", example = "201906281030191013526", requiredMode = Schema.RequiredMode.REQUIRED)
    private String upstreamOrderNo;

    @Schema(description = "商品Id", example = "********", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer productId;

    @Schema(description = "商品名称", example = "腾讯Q币直充一元", requiredMode = Schema.RequiredMode.REQUIRED)
    private String upstreamProductName;

    @Schema(description = "充值账号", example = "888888", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeAccount;

    @Schema(description = "充值数量", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer quantity;

    @Schema(description = "订单类型：1-话费 2-流量 3-卡密 4-直充", example = "4", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer orderType;

    @Schema(description = "交易单价", example = "1.1", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal unitSalePrice;

    @Schema(description = "订单状态： 0-充值成功/1-充值中/2-充值失败/3-未处理",
            example = "untreated", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer upstreamOrderStatus;

    @Schema(description = "创建时间", example = "2019-07-27 16:44:30", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createTime;

    @Schema(description = "订单完成时间，查单接口返回", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime completeTime;

    @Schema(description = "充值区（中文）", example = "电信一区", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String area;

    @Schema(description = "充值服（中文）", example = "逐鹿中原", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String server;

    @Schema(description = "计费方式（中文）", example = "Q币", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String type;

    @Schema(description = "运营商流水号", example = "", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String operatorSerialNumber;

    @Schema(description = "供应商ID", example = "", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long supplierId;
}
