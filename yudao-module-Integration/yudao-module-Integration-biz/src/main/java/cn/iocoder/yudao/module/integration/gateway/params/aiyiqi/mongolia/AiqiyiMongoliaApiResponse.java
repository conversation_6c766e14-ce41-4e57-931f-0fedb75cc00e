package cn.iocoder.yudao.module.integration.gateway.params.aiyiqi.mongolia;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiqiyiMongoliaApiResponse {
    @Schema(name = "返回码", requiredMode = Schema.RequiredMode.REQUIRED, example = "处理结果：A00000—成功,其他失败")
    private String code;

    @Schema(name = "返回信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private String msg;

    @Schema(name = "返回时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String timestamp;

    public String getLogContent() {
        return "爱奇艺充值接口返回\n" +
                "，code：" + code + "\n"
                + ", msg：" + msg + "\n"
                + ", timestamp" + timestamp;
    }
}
