package cn.iocoder.yudao.module.integration.gateway.mapper.jingyin;

import cn.iocoder.yudao.module.integration.gateway.dto.DirectChargeResultDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.DirectRechargeOrderDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.UpstreamOrderQueryDTO;
import cn.iocoder.yudao.module.integration.gateway.dto.jingyin.JingYinResultDTO;
import cn.iocoder.yudao.module.integration.gateway.params.jingyin.JingYinApiBizParams;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.CHANGSHENG_RESPONSE_ORDER_STATUS_ERROR;
import static cn.iocoder.yudao.module.integration.enums.ErrorCodeConstants.JINGYIN_RESPONSE_ORDER_STATUS_ERROR;

@Mapper
public interface JingYinOrderMapper {
    JingYinOrderMapper MAPPER = Mappers.getMapper(JingYinOrderMapper.class);

    @Mappings({
            @Mapping(source = "productId", target = "productId"),
            @Mapping(source = "upstreamOrderNo", target = "merOrderNo"),
            @Mapping(source = "quantity", target = "rechargeValue"),
            @Mapping(source = "chargeIp", target = "customerIp"),
    })
    JingYinApiBizParams toParams(DirectRechargeOrderDTO directRechargeOrderDTO);

    @Mappings({
            @Mapping(source = "orderNo", target = "externalOrderNo"),
            @Mapping(source = "merOrderNo", target = "upstreamOrderNo"),
            @Mapping(source = "businessType", target = "orderType"),
            @Mapping(source = "orderTime", target = "createTime"),
            @Mapping(source = "endTime", target = "finishTime", qualifiedByName = "mapTime"),
    })
    DirectChargeResultDTO toResult(JingYinResultDTO jingYinResultDTO);

    @Mappings({
            @Mapping(source = "merOrderNo", target = "upstreamOrderNo"),
            @Mapping(source = "orderState", target = "upstreamOrderStatus", qualifiedByName = "mapUpstreamOrderStatus"),
            @Mapping(source = "endTime", target = "completeTime", qualifiedByName = "mapTime"),
            @Mapping(source = "orderNo", target = "externalOrderNo"),
            @Mapping(source = "orderTime", target = "createTime", qualifiedByName = "mapTime"),
    })
    UpstreamOrderQueryDTO toQueryDTO(JingYinResultDTO jingYinResultDTO);

    @Named("mapToBigDecimal")
    default BigDecimal mapToBigDecimal(String price) {
        return new BigDecimal(price).setScale(2, RoundingMode.HALF_UP);
    }

    @Named("mapLongToBigDecimal")
    default BigDecimal mapLongToBigDecimal(Long value) {
        if (value == null) return BigDecimal.ZERO;
        // 假设厘为单位，转为元
        return BigDecimal.valueOf(value).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
    }

    @Named("mapStringToInt")
    default Integer mapStringToInt(String value) {
        return Integer.valueOf(value);
    }

    @Named("mapTime")
    default LocalDateTime mapTime(String chargeFinishTime) {
        if (Objects.isNull(chargeFinishTime)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(chargeFinishTime, formatter);
    }

    @Named("mapUpstreamOrderStatus")
    default Integer mapUpstreamOrderStatus(Integer orderState) {
        return switch (orderState) {
            case 24 -> 0;
            case 20 -> 1;
            case 23, -1 -> 2;
            case 0 -> 3;
            default -> throw exception(JINGYIN_RESPONSE_ORDER_STATUS_ERROR);
        };
    }
}