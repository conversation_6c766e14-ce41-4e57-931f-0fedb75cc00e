package cn.iocoder.yudao.module.integration.gateway.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "supplier.changsheng.api")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChangshengConfigurationProperties {
    private String domain;
    private String appKey;
    private String version;
    private String format;
    private String charset;
    private String signType;
    private String appAuthToken;
    private String appSecret;
    private String uniqueTag;
    private String interfaceClassName;
}
