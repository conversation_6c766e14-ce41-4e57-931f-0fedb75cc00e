package cn.iocoder.yudao.module.integration.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierProductDTO {
    /**
     * 供应商 ID
     */
    private Long supplierId;

    /**
     * 主产品 ID
     */
    private String productId;

    /**
     * 规格产品 ID
     */
    private String skuId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 规格产品名称
     */
    private String skuName;

    /**
     * 产品编号
     */
    private String sku;

    /**
     * 是否预售
     * 1 - 非活动商品
     * 2 - 活动商品
     */
    private Boolean isPreSale;

    /**
     * 活动开始时间
     */
    private String preSaleStartTime;

    /**
     * 活动结束时间
     */
    private String preSaleEndTime;

    /**
     * 商品下架时间
     */
    private String endTime;

    /**
     * 倍充
     */
    private Boolean multiCharge;

    /**
     * 商品图片
     */
    private String image;

    /**
     * 商品状态
     * 1 - 上架
     * 2 - 下架
     * 3 - 维护
     */
    private Integer status;

    /**
     * 库存数量
     */
    private Integer stockAmount;

    /**
     * 充值模板
     */
    private String template;

    /**
     * 结算价格
     */
    private BigDecimal price;

    /**
     * 面值
     */
    private BigDecimal guidePrice;

    /**
     * 单个订单最大购买数量
     */
    private Integer limitCountMax;

    /**
     * 单个订单最小购买数量
     */
    private Integer limitCountMin;

    /**
     * 充值类型
     * 1 - 充值
     * 2 - 卡密
     */
    private Integer type;
}
