package cn.iocoder.yudao.module.integration.gateway.params.youkayun;

import cn.iocoder.yudao.module.integration.gateway.utils.MD5Utils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

@Data
@SuperBuilder
public class YoukayunBaseRequest implements YoukayunSignableRequest {
    private String userid;
    private String sign;

    @Override
    public String generateSign(String signKey) {
        try {
            // 使用TreeMap自动按键名排序（ASCII升序）
            Map<String, String> sortedParams = new TreeMap<>();

            // 反射获取所有字段，包括父类
            Class<?> clazz = this.getClass();
            while (clazz != null && clazz != Object.class) {
                for (Field field : clazz.getDeclaredFields()) {
                    JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
                    String fieldName = jsonProperty != null ? jsonProperty.value() : field.getName();
                    if ("sign".equals(fieldName)) continue; // 跳过sign字段

                    field.setAccessible(true);
                    Object value = field.get(this);

                    // 过滤空值（null或空字符串）
                    if (value == null || (value instanceof String && ((String) value).isEmpty())) {
                        continue;
                    }

                    // 处理参数值
                    String paramValue;
                    if (value instanceof Collection || value.getClass().isArray()) {
                        paramValue = new ObjectMapper().writeValueAsString(value); // 数组/集合转JSON
                    } else {
                        paramValue = value.toString();
                    }

                    sortedParams.put(fieldName, paramValue);
                }
                clazz = clazz.getSuperclass();
            }

            // 拼接签名字符串
            StringBuilder signText = new StringBuilder();
            for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
                if (!signText.isEmpty()) {
                    signText.append("&");
                }
                signText.append(entry.getKey()).append("=").append(entry.getValue());
            }

            // 计算MD5
            return MD5Utils.MD5(signText + signKey);

        } catch (IllegalAccessException | JsonProcessingException e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    public MultiValueMap<String, String> generateSignedFormData(String signKey) {
        // 生成签名
        String sign = generateSign(signKey);

        // 转换为表单数据
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        try {
            Class<?> clazz = this.getClass();
            while (clazz != null) {
                for (Field field : clazz.getDeclaredFields()) {
                    field.setAccessible(true);
                    Object value = field.get(this);
                    JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
                    String fieldName = jsonProperty != null ? jsonProperty.value() : field.getName();

                    if ("sign".equals(fieldName)) continue;
                    if (value == null || (value instanceof String && ((String) value).isEmpty())) continue;

                    formData.add(fieldName, convertValue(value));
                }
                clazz = clazz.getSuperclass();
            }
        } catch (IllegalAccessException | JsonProcessingException e) {
            throw new RuntimeException("Form data conversion failed", e);
        }

        // 添加签名字段
        formData.add("sign", sign);
        return formData;
    }

    private String convertValue(Object value) throws JsonProcessingException {
        if (value instanceof Collection || value.getClass().isArray()) {
            return new ObjectMapper().writeValueAsString(value);
        }
        return value.toString();
    }
}
