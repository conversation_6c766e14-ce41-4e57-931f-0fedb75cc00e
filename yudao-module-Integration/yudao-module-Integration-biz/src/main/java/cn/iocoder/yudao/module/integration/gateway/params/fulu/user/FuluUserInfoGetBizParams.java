package cn.iocoder.yudao.module.integration.gateway.params.fulu.user;

import cn.iocoder.yudao.module.integration.enums.FuluMethodType;
import cn.iocoder.yudao.module.integration.gateway.params.fulu.FuluApiBizParams;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FuluUserInfoGetBizParams implements FuluApiBizParams {
    @Override
    public FuluMethodType getMethodType() {
        return FuluMethodType.USER_INFO_QUERY;
    }
}
