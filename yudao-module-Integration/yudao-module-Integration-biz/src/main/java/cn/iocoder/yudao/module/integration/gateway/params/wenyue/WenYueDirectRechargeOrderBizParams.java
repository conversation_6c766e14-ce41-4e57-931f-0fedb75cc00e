package cn.iocoder.yudao.module.integration.gateway.params.wenyue;

import cn.iocoder.yudao.module.integration.enums.WenYueMethodType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import static cn.iocoder.yudao.module.integration.enums.WenYueMethodType.ORDER_DIRECT_ADD;


@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WenYueDirectRechargeOrderBizParams implements WenYueApiBizParams {
    @Schema(description = "商户编号（非登录名），平台方提供", example = "merchant123", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("userid")
    private String userId;

    @Schema(description = "平台商品编号", example = "product001", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("productid")
    private String productId;

    @Schema(description = "订单商品数量", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("num")
    private String num;

    @Schema(description = "游戏账户所在区编号，国网电费时传用户手机号", example = "area001", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("areaid")
    private String areaId;

    @Schema(description = "游戏账户所在服编号", example = "server001", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("serverid")
    private String serverId;

    @Schema(description = "需充值账号", example = "***********", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("account")
    private String account;

    @Schema(description = "买家IP由合作商提供，南网电费此字段传身份证号（不参与签名）", example = "127.0.0.1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("userip")
    private String userIp;

    @Schema(description = "代理商订单时间(yyyyMMddHHmmss)如**************", example = "**************", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("spordertime")
    private String sporderTime;

    @Schema(description = "代理商系统订单号（流水号）要求不可重复，每笔只可同时提交一次", example = "ORDER123456789", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sporderid")
    private String sporderId;

    @Schema(description = "签名验证摘要串", example = "abcdef123456", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("sign")
    private String sign;

    @Schema(description = "回调URL（不参与签名）", example = "https://yourdomain.com/callback", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonProperty("back_url")
    private String backUrl;

    @JsonIgnore
    public String getLogContent() {
        return "问月直充接口订单参数:\n" +
                "userid='" + userId + "'\n" +
                "productid='" + productId + "'\n" +
                "num='" + num + "'\n" +
                "areaid='" + areaId + "'\n" +
                "serverid='" + serverId + "'\n" +
                "account='" + account + "'\n" +
                "userip='" + userIp + "'\n" +
                "spordertime='" + sporderTime + "'\n" +
                "sporderid='" + sporderId + "'\n" +
                "sign='" + sign + "'\n" +
                "back_url='" + backUrl + "'\n";
    }

    @Override
    public WenYueMethodType getMethodType() {
        return ORDER_DIRECT_ADD;
    }
}
