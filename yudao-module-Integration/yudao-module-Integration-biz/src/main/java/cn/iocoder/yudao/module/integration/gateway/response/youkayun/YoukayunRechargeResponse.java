package cn.iocoder.yudao.module.integration.gateway.response.youkayun;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YoukayunRechargeResponse {
    private Integer code;
    private String msg;
    private JsonNode data;

    public YoukayunRechargeResponse.YoukayunRechargeData parseSuccessData() throws JsonProcessingException {
        if (code == 1000) {
            return new ObjectMapper().treeToValue(data, YoukayunRechargeResponse.YoukayunRechargeData.class);
        }
        return null;
    }

    public List<?> parseErrorData() {
        if (code != 1000 && data.isArray()) {
            return new ObjectMapper().convertValue(data, List.class);
        }
        return Collections.emptyList();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class YoukayunRechargeData {
        @JsonProperty("ordersn")
        private String orderSn;

        private Integer quantity;

        @JsonProperty("outorderno")
        private String outOrderNo;

        private String money;
    }
}
