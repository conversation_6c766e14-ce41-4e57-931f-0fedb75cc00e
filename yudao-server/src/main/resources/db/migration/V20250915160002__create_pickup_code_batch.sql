CREATE TABLE `pickup_code_batch` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '批次ID',
    `batch_number` VARCHAR(64) NOT NULL COMMENT '批次号（格式: BATCH-YYYYMMDDHHmmss-XXXX）',
    `voucher_template_id` BIGINT NOT NULL COMMENT '提货券模板ID',
    `total_quantity` INT NOT NULL COMMENT '生成总数量',
    `redeemed_quantity` INT NOT NULL DEFAULT 0 COMMENT '已核销数量',
    `voided_quantity` INT NOT NULL DEFAULT 0 COMMENT '已作废数量',
    `remark` VARCHAR(512) DEFAULT NULL COMMENT '批次备注',
    `status` INT NOT NULL DEFAULT 1 COMMENT '状态：0=生效中，1=已作废',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_batch_number` (`batch_number`),
    KEY `idx_voucher_template_id` (`voucher_template_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提货码批次表';