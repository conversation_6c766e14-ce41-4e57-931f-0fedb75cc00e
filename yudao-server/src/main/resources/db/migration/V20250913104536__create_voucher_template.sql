CREATE TABLE `voucher_template` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(255) NOT NULL COMMENT '卡券名称',
    `description` VARCHAR(512) NOT NULL COMMENT '卡券描述',
    `image` VARCHAR(512) DEFAULT NULL COMMENT '卡券图片 URL',
    `daily_limit` INT DEFAULT NULL COMMENT '每天限制提货量',
    `limit_exceed_message` VARCHAR(255) DEFAULT NULL COMMENT '限制量超提示信息',
    `market_price` DECIMAL(10, 2) NOT NULL COMMENT '卡券市场价',
    `withdraw_type` INT DEFAULT NULL COMMENT '提货方式：1=按金额，2=按次数',
    `rule` JSON DEFAULT NULL COMMENT '提货规则，使用JSON格式存储',
    `status` INT DEFAULT NULL COMMENT '状态：1=上架，2=下架',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡券模板表';

CREATE TABLE `voucher_template_platform_product` (
    `voucher_template_id` BIGINT NOT NULL COMMENT '提货券模板ID（联合主键）',
    `platform_product_id` BIGINT NOT NULL COMMENT '平台商品ID（联合主键）',
    PRIMARY KEY (`voucher_template_id`, `platform_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提货券模板与平台商品关联表';

