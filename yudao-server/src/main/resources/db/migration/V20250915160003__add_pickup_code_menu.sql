select id
into @parent_id
from `system_menu`
where name = '商品管理';

insert into `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`,
                           `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `create_time`)
values ('提货码批次管理', 'product:voucher-pickup-code-batch:list', 2, 5, @parent_id, 'voucher-pickup-code-batch:list',
        'ep:document',
        'product/VoucherPickupCodeBatch/index',
        'VoucherPickupCodeBatch', 0, b'1', b'1', b'1', current_timestamp);


insert into `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`,
                           `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `create_time`)
values ('提货码管理', 'product:voucher-pickup-code:list', 2, 5, @parent_id, 'voucher-pickup-code:list',
        'ep:document',
        'product/VoucherPickupCode/index',
        'VoucherPickupCode', 0, b'1', b'1', b'1', current_timestamp);

select id
into @voucher_pickup_code_batch_parent_id
from `system_menu`
where component_name = 'VoucherPickupCodeBatch';

select id
into @voucher_pickup_code_parent_id
from `system_menu`
where component_name = 'VoucherPickupCode';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
values ('提货码批次查询', 'product:voucher-pickup-code-batch:query', 3, 1, @voucher_pickup_code_batch_parent_id, '', '',
        '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码批次创建', 'product:voucher-pickup-code-batch:create', 3, 2, @voucher_pickup_code_batch_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码批次更新', 'product:voucher-pickup-code-batch:update', 3, 3, @voucher_pickup_code_batch_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码批次删除', 'product:voucher-pickup-code-batch:delete', 3, 4, @voucher_pickup_code_batch_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码查询', 'product:voucher-pickup-code:query', 3, 1, @voucher_pickup_code_parent_id, '', '',
        '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码创建', 'product:voucher-pickup-code:create', 3, 2, @voucher_pickup_code_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码更新', 'product:voucher-pickup-code:update', 3, 3, @voucher_pickup_code_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false),
       ('提货码删除', 'product:voucher-pickup-code:delete', 3, 4, @voucher_pickup_code_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false);
