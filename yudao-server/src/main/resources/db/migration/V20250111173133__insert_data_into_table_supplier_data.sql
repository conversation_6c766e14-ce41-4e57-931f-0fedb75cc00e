INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商管理', '', 1, 1, 0, '/supplier', 'ep:avatar', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

select id
into @parent_id
from `system_menu`
where name = '供应商管理';

INSERT INTO `system_menu` (name, permission, type, sort, `parent_id`, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商', 'supplier:data:list', 2, 2, @parent_id, 'supplier', 'ep:goods-filled', 'supplier/supplier/index',
        'SystemSupplier', 0, true, true, true, '1', current_timestamp, '1', current_timestamp, false);

select id
into @supplier_parent_id
from `system_menu`
where name = '供应商';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商新增', 'supplier:data:create', 3, 2, @supplier_parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商查询', 'supplier:data:query', 3, 1, @supplier_parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商启用', 'supplier:data:update-enabled', 3, 5, @supplier_parent_id, '', '', '', '', 0, true, true, true,
        '1',
        current_timestamp, '1', current_timestamp, false);
INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('删除供应商', 'supplier:data:delete', 3, 3, @supplier_parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('修改供应商数据', 'supplier:data:update', 3, 4, @supplier_parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);