-- 为商户商品表添加提货券模板ID字段
ALTER TABLE `merchant_product` 
ADD COLUMN `voucher_template_id` BIGINT NULL COMMENT '提货券模板ID（当绑定提货券时使用，与platform_product_id互斥）' AFTER `platform_product_id`;

-- 添加索引
CREATE INDEX `idx_voucher_template_id` ON `merchant_product` (`voucher_template_id`);

-- 为提货券商品类型添加字典数据
INSERT INTO `system_dict_data` (`dict_type`, `label`, `value`, `sort`, `status`, `remark`, `creator`)
VALUES ('product_type', '提货券', '4', 4, 0, '提货券商品', '1');

-- 添加菜单权限（如果还没有的话）
INSERT IGNORE INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
SELECT '商户提货券绑定', 'merchant:voucher:bind', 3, 15, id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false
FROM `system_menu` WHERE name = '商户列表' LIMIT 1;
