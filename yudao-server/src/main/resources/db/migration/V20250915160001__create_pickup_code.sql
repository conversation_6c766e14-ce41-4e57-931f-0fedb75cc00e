CREATE TABLE `pickup_code` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code` VARCHAR(32) NOT NULL COMMENT '提货码（格式: PC-YYYYMMDD-XXXXXXXX）',
    `voucher_template_id` BIGINT NOT NULL COMMENT '所属提货券ID',
    `batch_id` BIGINT NOT NULL COMMENT '批次ID',
    `batch_number` VARCHAR(64) NOT NULL COMMENT '批次号',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `redeemable_time` JSON DEFAULT NULL COMMENT '可兑换时间规则（从提货券复制）',
    `pickup_rule` JSON DEFAULT NULL COMMENT '提货规则（从提货券复制）',
    `redemption_status` INT NOT NULL DEFAULT 0 COMMENT '核销状态：0=未核销，1=已核销',
    `redemption_time` DATETIME DEFAULT NULL COMMENT '核销时间',
    `redemption_user` VARCHAR(64) DEFAULT NULL COMMENT '核销用户',
    `code_status` INT NOT NULL DEFAULT 1 COMMENT '提货码状态：1=正常，2=作废',
    `remark` VARCHAR(512) DEFAULT NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`),
    KEY `idx_voucher_template_id` (`voucher_template_id`),
    KEY `idx_batch_id` (`batch_id`),
    KEY `idx_batch_number` (`batch_number`),
    KEY `idx_redemption_status` (`redemption_status`),
    KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提货码表';