DROP TABLE IF EXISTS `supplier_data`;
CREATE TABLE `supplier_data`
(
    `id`                 bigint(20)                               NOT NULL AUTO_INCREMENT COMMENT '供应商id',
    `company`            varchar(100) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '公司名称',
    `username`           varchar(128) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '用户名',
    `serial_number`      varchar(64) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '' COMMENT '编号',
    `main_business_type` varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '主营业务',
    `channel_account`    varchar(256) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '渠道帐号',
    `account_key`        varchar(255) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '帐号密钥',
    `signing_key`        varchar(255) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '签名密钥',
    `enabled`            tinyint(1)                               NOT NULL DEFAULT '0' COMMENT '是否启用',
    `create_time`        datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `creator`            varchar(64) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '' COMMENT '创建者',
    `updater`            varchar(64) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '' COMMENT '更新者',
    `deleted`            bit(1)                                   NOT NULL DEFAULT b'0' COMMENT '是否删除',

    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='供应商数据表';
