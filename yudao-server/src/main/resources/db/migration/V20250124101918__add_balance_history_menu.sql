select id
into @parent_id
from `system_menu`
where name = '商户管理';

insert into `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
values ('加款管理', 'merchant:balance-history:query', 2, 2, @parent_id, 'balance-history', 'ep:money',
        'merchant/BalanceHistory/Index', 'BalanceHistory', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

insert into `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
values ('授信管理', 'merchant:credit-limit-history:query', 2, 3, @parent_id, 'credit-limit-history', 'ep:credit-card',
        'merchant/CreditLimitHistory/Index', 'CreditLimitHistory', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
