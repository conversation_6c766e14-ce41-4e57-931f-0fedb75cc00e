select id
into @parent_id
from `system_menu`
where name = '商户列表';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户查询', 'merchant:data:query', 3, 1, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户新增', 'merchant:data:create', 3, 2, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户修改', 'merchant:data:update', 3, 3, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户删除', 'merchant:data:delete', 3, 4, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
