select id
into @parent_id
from `system_menu`
where name = '商品管理';

delete
from `system_menu`
where component_name = 'SystemSupplierPriceChange';

insert into `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`,
                           `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `create_time`)
values ('供应商商品价格变动记录', 'supplier-product:price-change:list', 2, 2, @parent_id, 'supplier-price-change/list',
        'ep:document',
        'product/SupplierPriceChange/index',
        'SystemSupplierPriceChange', 0, b'1', b'1', b'1', current_timestamp);

select id
into @supplier_price_change_parent_id
from `system_menu`
where component_name = 'SystemSupplierPriceChange';

delete
from `system_menu`
where name IN ('供应商商品价格变动查询', '供应商商品价格变动接收');

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商商品价格变动查询', 'supplier-product:price-change:query', 3, 5, @supplier_price_change_parent_id, '', '',
        '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商商品价格变动接收', 'supplier-product:price-change:accept', 3, 5, @supplier_price_change_parent_id, '',
        '', '', '', 0, true,
        true, true, '1',
        current_timestamp, '1', current_timestamp, false);