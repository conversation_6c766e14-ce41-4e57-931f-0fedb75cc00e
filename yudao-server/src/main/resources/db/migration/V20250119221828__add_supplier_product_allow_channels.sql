DROP TABLE IF EXISTS product_allowed_channels;
CREATE TABLE `supplier_product_allowed_channels` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `channel_value` varchar(100) NOT NULL COMMENT '渠道值（对应字典的value字段）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品与允许渠道关系表';

