drop table if exists merchant_recharge_request;
create table merchant_recharge_request
(
    id              BIGINT auto_increment primary key comment '申请编号',
    merchant_id     BIGINT         not null comment '商户ID',
    recharge_type   varchar(32)    not null comment '充值类型',
    amount_of_money DECIMAL(10, 2) not null comment '充值金额',
    remark          varchar(255) collate utf8mb4_unicode_ci default '' comment '备注',
    `creator`       varchar(64) collate utf8mb4_unicode_ci  default '' comment '创建者',
    `create_time`   datetime       not null                 default current_timestamp comment '创建时间',
    `updater`       varchar(64) collate utf8mb4_unicode_ci  default '' comment '更新者',
    `update_time`   datetime       not null                 default current_timestamp on update current_timestamp comment '更新时间',
    `deleted`       bit(1)         not null                 default b'0' comment '是否删除'
) engine = InnoDB
  default charset = utf8mb4
  collate = utf8mb4_unicode_ci comment ='商户充值请求表';
