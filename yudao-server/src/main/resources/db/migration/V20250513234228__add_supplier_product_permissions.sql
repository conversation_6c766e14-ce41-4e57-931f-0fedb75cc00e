select id
into @parent_id
from `system_menu`
where name = '供应商商品列表';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商商品查询', 'supplier-product:data:query', 3, 1, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商商品新增', 'supplier-product:data:create', 3, 2, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商商品修改', 'supplier-product:data:update', 3, 3, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商商品删除', 'supplier-product:data:delete', 3, 4, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
