DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`
(
    `id`                    BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    `order_no`              VARCHAR(64)    NOT NULL COMMENT '订单号',
    `upstream_order_no`     VARCHAR(64) COMMENT '上游订单号(最后一个上游订单号)',
    `recharge_account`      VARCHAR(128)   NOT NULL COMMENT '充值账号',
    `region`                VARCHAR(64) COMMENT '归属地',
    `product_type`          VARCHAR(32)    NOT NULL COMMENT '商品类型（直充/卡密）',
    `platform_product_id`   VARCHAR(64)    NOT NULL COMMENT '平台商品ID',
    `platform_product_name` VARCHAR(128)   NOT NULL COMMENT '平台商品名称',
    `merchant_id`           VARCHAR(64)    NOT NULL COMMENT '商户ID',
    `merchant_name`         VARCHAR(128)   NOT NULL COMMENT '商户名称',
    `fapiao_type`           VARCHAR(32) COMMENT '发票类型',
    `tax_rate`              DECIMAL(5, 2) COMMENT '税点',
    `unit_sale_price`       DECIMAL(18, 2) NOT NULL COMMENT '售价（密价）',
    `quantity`              INT            NOT NULL COMMENT '销售数量',
    `total_cost`            DECIMAL(18, 2) NOT NULL COMMENT '成本(上游成本 * 数量)',
    `order_amount`          DECIMAL(18, 2) NOT NULL COMMENT '订单金额（密价 * 销售数量）',
    `supplier_id`           VARCHAR(128) COMMENT '供应商ID',
    `supplier_name`         VARCHAR(128) COMMENT '供应商名称',
    `order_status`          TINYINT        NOT NULL COMMENT '订单状态（充值中/充值成功/充值失败）',
    `after_sale_status`     TINYINT COMMENT '售后状态（已退款）',
    `batch_no`              VARCHAR(64) COMMENT '批充批次号',
    `complete_time`         DATETIME COMMENT '订单完成时间',
    `remark`                VARCHAR(512) COMMENT '备注',
    `creator`               VARCHAR(64) collate utf8mb4_unicode_ci DEFAULT '' comment '创建者',
    `create_time`           DATETIME       NOT NULL                DEFAULT current_timestamp comment '订单创建时间',
    `updater`               VARCHAR(64) collate utf8mb4_unicode_ci DEFAULT '' comment '更新者',
    `update_time`           DATETIME       NOT NULL                DEFAULT current_timestamp on update current_timestamp comment '更新时间',
    `deleted`               BIT(1)         NOT NULL                DEFAULT b'0' comment '是否删除',
    INDEX                   `idx_order_no` (`order_no`),
    INDEX                   `idx_upstream_order_no` (`upstream_order_no`),
    INDEX                   `idx_platform_product_id` (`platform_product_id`),
    INDEX                   `idx_merchant_id` (`merchant_id`),
    INDEX                   `idx_create_time` (`create_time`)
) COMMENT '订单表';


DROP TABLE IF EXISTS `upstream_order`;
CREATE TABLE `upstream_order`
(
    `id`                    BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '上游订单ID',
    `main_order_no`         VARCHAR(64)    NOT NULL COMMENT '订单号',
    `upstream_order_no`     VARCHAR(64)    NOT NULL COMMENT '上游订单号(自己生成的唯一单号)',
    `external_order_id`     VARCHAR(64) COMMENT '外部订单ID（上游返回订单ID）',
    `external_order_no`     VARCHAR(64)    NOT NULL COMMENT '外部订单号（上游返回订单号）',
    `supplier_id`           VARCHAR(128) COMMENT '供应商ID',
    `supplier_name`         VARCHAR(128) COMMENT '供应商名称',
    `upstream_product_name` VARCHAR(128)   NOT NULL COMMENT '上游商品名称',
    `recharge_account`      VARCHAR(128)   NOT NULL COMMENT '充值账号',
    `unit_sale_price`       DECIMAL(18, 2) NOT NULL COMMENT '上游售价',
    `quantity`              INT            NOT NULL COMMENT '充值数量',
    `order_amount`          DECIMAL(18, 2) NOT NULL COMMENT '订单金额（密价 * 销售数量）',
    `total_cost`            DECIMAL(18, 2) NOT NULL COMMENT '上游成本金额',
    `upstream_order_status` TINYINT        NOT NULL COMMENT '订单状态（充值中/充值成功/充值失败）',
    `complete_time`         DATETIME       NOT NULL COMMENT '订单完成时间',
    `remark`                TEXT COMMENT '备注(记录失败原因)',
    `creator`               VARCHAR(64) collate utf8mb4_unicode_ci DEFAULT '' comment '创建者',
    `create_time`           DATETIME       NOT NULL                DEFAULT current_timestamp comment '订单创建时间',
    `updater`               VARCHAR(64) collate utf8mb4_unicode_ci DEFAULT '' comment '更新者',
    `update_time`           DATETIME       NOT NULL                DEFAULT current_timestamp on update current_timestamp comment '更新时间',
    `deleted`               BIT(1)         NOT NULL                DEFAULT b'0' comment '是否删除',
    INDEX                   `idx_main_order_no` (`main_order_no`),
    INDEX                   `idx_upstream_order_no` (`upstream_order_no`),
    INDEX                   `idx_create_time` (`create_time`)
) COMMENT '上游订单表';

DROP TABLE IF EXISTS `order_log`;
CREATE TABLE `order_log`
(
    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    `main_order_no` VARCHAR(64) NOT NULL COMMENT '主订单号',
    `log_type`      TINYINT     NOT NULL COMMENT '日志类型',
    `log_content`   TEXT        NOT NULL COMMENT '日志内容',
    `creator`       VARCHAR(64) collate utf8mb4_unicode_ci DEFAULT '' comment '创建者',
    `create_time`   DATETIME    NOT NULL                   DEFAULT current_timestamp comment '创建时间',
    INDEX           `idx_main_order_no` (`main_order_no`)
) COMMENT '订单日志表';
