DROP TABLE IF EXISTS platform_product;
CREATE TABLE platform_product
(
    `id`          bigint AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
    `name`        varchar(255) NOT NULL COMMENT '商品名称',
    `type`        tinyint      NOT NULL COMMENT '类型: 1-直充, 2-卡密',
    `is_multiple` bit(1)       NOT NULL                  DEFAULT b'0' COMMENT '是否支持倍冲',
    `fapiao_type` tinyint      NOT NULL                  DEFAULT 0 COMMENT '发票类型: 1-普票, 2-专票',
    `tax_rate`    decimal(10, 2)                         DEFAULT 0.00 COMMENT '税率',
    `loop_model`  tinyint      NOT NULL                  DEFAULT 0 COMMENT '询价模式: 1-定序, 2-低价, 3-随机',
    `price`       decimal(10, 2)                         DEFAULT 0.00 COMMENT '商品价格',
    `guide_price` decimal(10, 2)                         DEFAULT 0.00 COMMENT '面值',
    `status`      tinyint      NOT NULL COMMENT '商品状态: 1-上架, 2-下架, 3-删除, 4-维护',
    `creator`     varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime     NOT NULL                  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`     varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime     NOT NULL                  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     bit(1)       NOT NULL                  DEFAULT b'0' COMMENT '是否删除'
) ENGINE=InnoDB AUTO_INCREMENT=36222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台商品表';


DROP TABLE IF EXISTS platform_product_allowed_channels;
CREATE TABLE `platform_product_allowed_channels`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id`    bigint       NOT NULL COMMENT '商品ID',
    `channel_value` varchar(100) NOT NULL COMMENT '渠道值（对应字典的value字段）',
    `create_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`       varchar(64)           DEFAULT '' COMMENT '创建者',
    `updater`       varchar(64)           DEFAULT '' COMMENT '更新者',
    `deleted`       bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY             `idx_product_id` (`product_id`),
    KEY             `idx_channel_value` (`channel_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台商品与允许渠道关系表';

CREATE TABLE platform_product_supplier
(
    `id`                    bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `platform_product_id`   bigint       NOT NULL COMMENT '平台商品ID',
    `supplier_product_id`   bigint       NOT NULL COMMENT '供应商商品ID',
    `supplier_product_name` varchar(100) NOT NULL COMMENT '供应商商品名称',
    `price`                 DECIMAL(10, 2)        DEFAULT 0.00 COMMENT '商品价格',
    `create_time`           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`               varchar(64)           DEFAULT '' COMMENT '创建者',
    `updater`               varchar(64)           DEFAULT '' COMMENT '更新者',
    `deleted`               bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                     `idx_platform_product_id` (`platform_product_id`),
    KEY                     `idx_vendor_product_id` (`supplier_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台商品与供应商商品关系表';


