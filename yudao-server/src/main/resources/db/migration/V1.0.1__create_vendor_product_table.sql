DROP TABLE IF EXISTS bzi_vendor_product;
CREATE TABLE bzi_vendor_product (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '商品编号',
    product_id BIGINT NOT NULL COMMENT '主产品 ID',
    sku_id BIGINT NOT NULL COMMENT '规格产品 ID',
    sku_name VARCHAR(255) NOT NULL COMMENT '规格产品名称',
    is_pre_sale TINYINT NOT NULL COMMENT '是否预售: 1-非活动商品, 2-活动商品',
    pre_sale_start_time DATETIME DEFAULT NULL COMMENT '活动开始时间',
    pre_sale_end_time DATETIME DEFAULT NULL COMMENT '活动结束时间',
    end_time DATETIME DEFAULT NULL COMMENT '商品下架时间',
    status TINYINT NOT NULL COMMENT '商品状态: 1-上架, 2-下架, 3-删除, 4-维护',
    stock_amount INT DEFAULT 0 COMMENT '库存数量',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    template JSON DEFAULT NULL COMMENT '充值模板',
    image VARCHAR(500) COMMENT '主产品图片',
    price DECIMAL(10, 2) DEFAULT 0.00 COMMENT '结算价格',
    guide_price DECIMAL(10, 2) DEFAULT 0.00 COMMENT '面值',
    limit_count_max INT DEFAULT NULL COMMENT '单个订单最大购买数量',
    limit_count_min INT DEFAULT NULL COMMENT '单个订单最小购买数量',
    type TINYINT NOT NULL COMMENT '类型: 1-直充, 2-卡密',
    available_merchant_ids JSON DEFAULT NULL COMMENT '可用商户 ID 列表',
    category_ids JSON DEFAULT NULL COMMENT '类目id',
    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE=InnoDB AUTO_INCREMENT=36222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商商品表';
