select id
into @parent_id
from `system_menu`
where name = '平台商品列表';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('平台商品查询', 'platform-product:data:query', 3, 1, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('平台商品新增', 'platform-product:data:create', 3, 2, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('平台商品修改', 'platform-product:data:update', 3, 3, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('平台商品删除', 'platform-product:data:delete', 3, 4, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
