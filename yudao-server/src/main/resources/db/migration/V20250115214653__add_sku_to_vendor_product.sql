ALTER TABLE `quanyi_system`.`vendor_product`
ADD COLUMN `sku` VARCHAR(255) NULL COMMENT '商品编号' AFTER `deleted`,
ADD COLUMN `supplier_id` BIGINT NOT NULL COMMENT '供应商id' AFTER `sku`,
ADD COLUMN `multi_charge` BIT(1) NOT NULL DEFAULT b'0' COMMENT '倍充' AFTER `supplier_id`,
CHANGE COLUMN `product_id` `product_id` VARCHAR(255) NULL COMMENT '主产品 ID' ,
CHANGE COLUMN `sku_id` `sku_id` VARCHAR(255) NULL COMMENT '规格产品 ID' ,
CHANGE COLUMN `sku_name` `sku_name` VARCHAR(255) NULL COMMENT '规格产品名称' ;
