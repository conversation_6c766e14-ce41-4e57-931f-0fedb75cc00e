-- 为商户商品表添加商品类型字段并迁移数据
-- 如果voucher_template_id存在，则product_type为4（提货券）
-- 否则从platform_product表查询对应的type

-- 1. 添加product_type字段
ALTER TABLE `merchant_product` 
ADD COLUMN `product_type` TINYINT NULL COMMENT '商品类型：1-直充，2-卡密，3-实物，4-提货券' AFTER `platform_product_id`;

-- 2. 迁移现有数据
-- 2.1 对于有voucher_template_id的记录，设置product_type为4（提货券）
UPDATE `merchant_product` 
SET `product_type` = 4 
WHERE `voucher_template_id` IS NOT NULL;

-- 2.2 对于没有voucher_template_id的记录，从platform_product表获取type
UPDATE `merchant_product` mp
INNER JOIN `platform_product` pp ON mp.`platform_product_id` = pp.`id`
SET mp.`product_type` = pp.`type`
WHERE mp.`voucher_template_id` IS NULL 
  AND mp.`platform_product_id` IS NOT NULL;

-- 3. 将product_type字段设为NOT NULL（所有数据迁移完成后）
ALTER TABLE `merchant_product` 
MODIFY COLUMN `product_type` TINYINT NOT NULL COMMENT '商品类型：1-直充，2-卡密，3-实物，4-提货券';

-- 4. 添加索引以提高查询性能
CREATE INDEX `idx_product_type` ON `merchant_product` (`product_type`);
