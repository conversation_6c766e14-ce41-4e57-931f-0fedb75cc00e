ALTER TABLE `merchant`
    ADD COLUMN `product_id_list` json DEFAULT NULL COMMENT '商户商品ID List' AFTER `ip_whitelist`;



select id
into @parent_id
from `system_menu`
where name = '商户列表';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户商品查询', 'merchant:product:query', 3, 7, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户商品绑定', 'merchant:product:bind', 3, 8, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户商品修改', 'merchant:product:update', 3, 9, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商户商品删除', 'merchant:product:delete', 3, 10, @parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);