DROP TABLE IF EXISTS `supplier_balance`;
CREATE TABLE `supplier_balance`
(
    `id`              bigint(20)                              NOT NULL AUTO_INCREMENT COMMENT 'Id',
    `supplier_id`     bigint(20)                              NOT NULL COMMENT '供应商Id',
    `company`         varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商名称/公司名称',
    `channel_account` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '渠道帐号',
    `balance`         decimal(10, 2)                                   DEFAULT 0.00 COMMENT '余额',
    `enabled`         tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '是否启用',
    `create_time`     datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `creator`         varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `updater`         varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '更新者',
    `deleted`         bit(1)                                  NOT NULL DEFAULT b'0' COMMENT '是否删除',

    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='供应商余额数据表';