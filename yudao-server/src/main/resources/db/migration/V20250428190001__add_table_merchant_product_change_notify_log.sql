CREATE TABLE `merchant_product_change_notify_log`
(
    `id`                  bigint auto_increment NOT NULL COMMENT '主键id',
    `merchant_id`         bigint                NOT NULL COMMENT '商户ID',
    `merchant_product_id` bigint                NOT NULL COMMENT '商户商品ID',
    `platform_product_id` bigint                NOT NULL COMMENT '平台商品Id',
    `change_type`         int                   NOT NULL COMMENT '变更类型',
    `change_price`        decimal(10, 2)        NULL DEFAULT 0.00 COMMENT '变更价格',
    `status`              int                   NOT NULL COMMENT '通知的状态 1-上架 2-下架',
    `notify_result`       int                   NOT NULL COMMENT '通知结果 1-成功 2-失败',
    `create_time`         datetime              NOT NULL COMMENT '创建时间',
    `creator`             varchar(255) COMMENT '创建者',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='商户商品变更通知日志表';