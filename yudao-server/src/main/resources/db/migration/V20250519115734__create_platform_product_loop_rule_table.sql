SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_SCHEMA = DATABASE()
                 AND TABLE_NAME = 'order'
                 AND COLUMN_NAME = 'loop_rule_id');

SET @sqlstmt := IF(@exist > 0,
                   'ALTER TABLE `order` DROP COLUMN `loop_rule_id`;',
                   'SELECT ''Column loop_rule_id does not exist, skipping.''');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;


SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_SCHEMA = DATABASE()
                 AND TABLE_NAME = 'platform_product_supplier'
                 AND COLUMN_NAME = 'platform_product_loop_rule_id');

SET @sqlstmt := IF(@exist > 0,
                   'ALTER TABLE `platform_product_supplier` DROP COLUMN `platform_product_loop_rule_id`;',
                   'SELECT ''Column platform_product_loop_rule_id does not exist, skipping.''');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

alter table `platform_product_supplier`
    add column `platform_product_loop_rule_id` bigint comment '轮询规则id';

drop table if exists `platform_product_loop_rule`;

create table `platform_product_loop_rule`
(
    `id`                  bigint auto_increment primary key comment '平台商品轮询规则ID',
    `platform_product_id` bigint comment '平台商品Id',
    `provinces`           json comment '省份',
    `telecom_operators`   json comment '运营商',
    `time_range`          json comment '时间区间',
    `loop_model`          tinyint comment '轮询模式',
    `creator`             varchar(64) collate utf8mb4_unicode_ci default '' comment '创建者',
    `create_time`         datetime not null                      default current_timestamp comment '创建时间',
    `updater`             varchar(64) collate utf8mb4_unicode_ci default '' comment '更新者',
    `update_time`         datetime not null                      default current_timestamp on update current_timestamp comment '更新时间',
    `deleted`             bit(1)   not null                      default b'0' comment '是否删除'
) comment '平台商品轮询规则表';
