-- 修改提货码表：删除核销相关字段，添加出库相关字段
-- 删除核销状态、核销时间、核销用户字段
ALTER TABLE `pickup_code` DROP COLUMN `redemption_status`;
ALTER TABLE `pickup_code` DROP COLUMN `redemption_time`;
ALTER TABLE `pickup_code` DROP COLUMN `redemption_user`;

-- 添加出库相关字段
ALTER TABLE `pickup_code` ADD COLUMN `outbound_status` INT NOT NULL DEFAULT 0 COMMENT '出库状态：0=未出库，1=已出库' AFTER `pickup_rule`;
ALTER TABLE `pickup_code` ADD COLUMN `outbound_order_number` VARCHAR(64) DEFAULT NULL COMMENT '出库单号（订单号）' AFTER `outbound_status`;
ALTER TABLE `pickup_code` ADD COLUMN `outbound_time` DATETIME DEFAULT NULL COMMENT '出库时间（生成订单的时间）' AFTER `outbound_order_number`;

-- 添加新的索引
CREATE INDEX `idx_outbound_status` ON `pickup_code` (`outbound_status`);
CREATE INDEX `idx_outbound_order_number` ON `pickup_code` (`outbound_order_number`);

-- 修改提货码批次表：将已核销数量改为已出库数量
ALTER TABLE `pickup_code_batch` CHANGE COLUMN `redeemed_quantity` `outbound_quantity` INT NOT NULL DEFAULT 0 COMMENT '已出库数量';

-- 更新权限：将核销权限改为出库权限
UPDATE `system_menu` SET `name` = '提货码出库', `permission` = 'product:voucher-pickup-code:outbound'
WHERE `permission` = 'product:voucher-pickup-code:redeem';
