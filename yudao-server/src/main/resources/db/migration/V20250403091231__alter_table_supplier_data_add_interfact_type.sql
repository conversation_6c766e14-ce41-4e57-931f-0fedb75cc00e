ALTER TABLE `supplier_data`
    ADD COLUMN `interface_type` tinyint NOT NULL DEFAULT 1 COMMENT '接口类型:1-异步, 2-同步';

INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`)
VALUES ('供应商接口类型', 'supplier_interface_type', 0, '供应商接口类型', 1);

INSERT INTO `system_dict_data` (`dict_type`, `label`, `value`, `sort`, `status`, `remark`, `creator`)
VALUES ('supplier_interface_type', '异步', '1', 1, 0, '异步', '1'),
       ('supplier_interface_type', '同步', '2', 2, 0, '同步', '1');