DROP TABLE IF EXISTS `price_change_record`;
CREATE TABLE `price_change_record`
(
    `id`           bigint(20)                               NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `product_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '上游产品名称',
    `product_id`   varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '上游产品Id',
    `status`       tinyint                                 NOT NULL DEFAULT 1 COMMENT '当前状态: 1-正常, 2-告警',
    `supplier_id`  bigint                                  NOT NULL COMMENT '供应商id',
    `before_price` decimal(10, 2)                          NOT NULL DEFAULT '0.00' COMMENT '变更前价格',
    `after_price`  decimal(10, 2)                          NOT NULL DEFAULT '0.00' COMMENT '变更后价格',
    `change_price` decimal(10, 2)                          NOT NULL DEFAULT '0.00' COMMENT '变更金额',
    `change_time`  datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_time`  datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `creator`      varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '创建者',
    `updater`      varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '更新者',
    `deleted`      bit(1)                                  NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 46222
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='供应商商品价格变更记录';
