ALTER TABLE merchant_product
    ALTER COLUMN auth_type SET DEFAULT 1;

DELETE
FROM system_dict_type
WHERE type = 'auth_type'
  AND id > 0;

DELETE
FROM system_dict_data
WHERE dict_type = 'auth_type'
  AND id > 0;

INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`)
VALUES ('授权方式', -- 字典名称
        'auth_type', -- 字典类型
        0, -- 状态（0正常）
        '授权方式', -- 备注
        1);

INSERT INTO `system_dict_data` (`dict_type`, `label`, `value`, `sort`, `status`, `remark`, `creator`)
VALUES ('auth_type', '直充', '1', 1, 0, '', '1'),
       ('auth_type', '批充', '2', 2, 0, '', '1');