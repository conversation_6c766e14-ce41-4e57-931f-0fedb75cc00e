select id
into @parent_id
from `system_menu`
where name = '供应商管理';

delete
from `system_menu`
where name = '供应商余额';

INSERT INTO `system_menu` (name, permission, type, sort, `parent_id`, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商余额', 'supplier:balance:list', 2, 2, @parent_id, 'supplier/supplier-balance', 'ep:credit-card',
        'supplier/supplier-balance/index',
        'SystemSupplierBalance', 0, true, true, true, '1', current_timestamp, '1', current_timestamp, false);

select id
into @supplier_parent_id
from `system_menu`
where name = '供应商余额';

delete
from `system_menu`
where name = '供应商余额查询';

INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('供应商余额查询', 'supplier:balance:query', 3, 1, @supplier_parent_id, '', '', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);