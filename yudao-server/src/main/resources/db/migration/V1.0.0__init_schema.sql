-- MySQL dump 10.13  Distrib 5.7.42, for Linux (x86_64)
--
-- Host: localhost    Database: quanyi-system
-- ------------------------------------------------------
-- Server version	5.7.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `infra_api_access_log`
--

DROP TABLE IF EXISTS `infra_api_access_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_api_access_log` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
                                        `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
                                        `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户编号',
                                        `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型',
                                        `application_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
                                        `request_method` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求方法名',
                                        `request_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求地址',
                                        `request_params` text COLLATE utf8mb4_unicode_ci COMMENT '请求参数',
                                        `response_body` text COLLATE utf8mb4_unicode_ci COMMENT '响应结果',
                                        `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
                                        `user_agent` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
                                        `operate_module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作模块',
                                        `operate_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作名',
                                        `operate_type` tinyint(4) DEFAULT '0' COMMENT '操作分类',
                                        `begin_time` datetime NOT NULL COMMENT '开始请求时间',
                                        `end_time` datetime NOT NULL COMMENT '结束请求时间',
                                        `duration` int(11) NOT NULL COMMENT '执行时长',
                                        `result_code` int(11) NOT NULL DEFAULT '0' COMMENT '结果码',
                                        `result_msg` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '结果提示',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=36222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API 访问日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_api_access_log`
--

LOCK TABLES `infra_api_access_log` WRITE;
/*!40000 ALTER TABLE `infra_api_access_log` DISABLE KEYS */;
INSERT INTO `infra_api_access_log` VALUES (35942,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 14:57:25','2024-12-26 14:57:25',99,0,'',NULL,'2024-12-26 14:57:25',NULL,'2024-12-26 14:57:25',_binary '\0',1),(35943,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 14:57:25','2024-12-26 14:57:25',99,0,'',NULL,'2024-12-26 14:57:25',NULL,'2024-12-26 14:57:25',_binary '\0',1),(35944,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 14:58:24','2024-12-26 14:58:24',21,0,'',NULL,'2024-12-26 14:58:24',NULL,'2024-12-26 14:58:24',_binary '\0',1),(35945,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"芋道源码\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 14:58:49','2024-12-26 14:58:49',228,0,'',NULL,'2024-12-26 14:58:49',NULL,'2024-12-26 14:58:49',_binary '\0',1),(35946,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"芋道源码\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 14:58:49','2024-12-26 14:58:49',460,0,'',NULL,'2024-12-26 14:58:49',NULL,'2024-12-26 14:58:49',_binary '\0',1),(35947,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 14:58:49','2024-12-26 14:58:50',134,0,'',NULL,'2024-12-26 14:58:50',NULL,'2024-12-26 14:58:50',_binary '\0',1),(35948,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 14:58:50','2024-12-26 14:58:50',329,0,'',NULL,'2024-12-26 14:58:50',NULL,'2024-12-26 14:58:50',_binary '\0',1),(35949,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 14:58:50','2024-12-26 14:58:50',97,403,'没有该操作权限',NULL,'2024-12-26 14:58:50',NULL,'2024-12-26 14:58:50',_binary '\0',1),(35950,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:01:00','2024-12-26 15:01:00',103,0,'',NULL,'2024-12-26 15:01:00',NULL,'2024-12-26 15:01:00',_binary '\0',1),(35951,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:05:00','2024-12-26 15:05:00',54,0,'',NULL,'2024-12-26 15:05:00',NULL,'2024-12-26 15:05:00',_binary '\0',1),(35952,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:05:01','2024-12-26 15:05:01',24,403,'没有该操作权限',NULL,'2024-12-26 15:05:01',NULL,'2024-12-26 15:05:01',_binary '\0',1),(35953,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:05:06','2024-12-26 15:05:06',25,403,'没有该操作权限',NULL,'2024-12-26 15:05:06',NULL,'2024-12-26 15:05:06',_binary '\0',1),(35954,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获得租户套餐分页',1,'2024-12-26 15:05:12','2024-12-26 15:05:12',43,403,'没有该操作权限',NULL,'2024-12-26 15:05:12',NULL,'2024-12-26 15:05:12',_binary '\0',1),(35955,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:05:19','2024-12-26 15:05:19',50,0,'',NULL,'2024-12-26 15:05:19',NULL,'2024-12-26 15:05:19',_binary '\0',1),(35956,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:09:06','2024-12-26 15:09:07',97,0,'',NULL,'2024-12-26 15:09:07',NULL,'2024-12-26 15:09:07',_binary '\0',1),(35957,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:09:28','2024-12-26 15:09:28',139,0,'',NULL,'2024-12-26 15:09:28',NULL,'2024-12-26 15:09:28',_binary '\0',1),(35958,'',1,2,'yudao-server','GET','/admin-api/system/menu/list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 菜单','获取菜单列表',1,'2024-12-26 15:09:35','2024-12-26 15:09:35',97,403,'没有该操作权限',NULL,'2024-12-26 15:09:35',NULL,'2024-12-26 15:09:35',_binary '\0',1),(35959,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:13:15','2024-12-26 15:13:15',83,0,'',NULL,'2024-12-26 15:13:15',NULL,'2024-12-26 15:13:15',_binary '\0',1),(35960,'',1,2,'yudao-server','GET','/admin-api/system/menu/list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 菜单','获取菜单列表',1,'2024-12-26 15:13:16','2024-12-26 15:13:16',34,403,'没有该操作权限',NULL,'2024-12-26 15:13:16',NULL,'2024-12-26 15:13:16',_binary '\0',1),(35961,'',1,2,'yudao-server','POST','/admin-api/system/auth/logout','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','登出系统',2,'2024-12-26 15:13:22','2024-12-26 15:13:23',255,0,'',NULL,'2024-12-26 15:13:23',NULL,'2024-12-26 15:13:23',_binary '\0',1),(35962,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:13:24','2024-12-26 15:13:24',39,0,'',NULL,'2024-12-26 15:13:24',NULL,'2024-12-26 15:13:24',_binary '\0',1),(35963,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:13:24','2024-12-26 15:13:24',39,0,'',NULL,'2024-12-26 15:13:24',NULL,'2024-12-26 15:13:24',_binary '\0',1),(35964,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:13:24','2024-12-26 15:13:24',191,0,'',NULL,'2024-12-26 15:13:24',NULL,'2024-12-26 15:13:24',_binary '\0',1),(35965,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:13:24','2024-12-26 15:13:24',191,0,'',NULL,'2024-12-26 15:13:24',NULL,'2024-12-26 15:13:24',_binary '\0',1),(35966,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 15:13:24','2024-12-26 15:13:24',13,0,'',NULL,'2024-12-26 15:13:24',NULL,'2024-12-26 15:13:24',_binary '\0',1),(35967,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"芋道源码\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 15:13:29','2024-12-26 15:13:29',22,0,'',NULL,'2024-12-26 15:13:29',NULL,'2024-12-26 15:13:29',_binary '\0',1),(35968,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"芋道源码\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 15:13:29','2024-12-26 15:13:29',233,0,'',NULL,'2024-12-26 15:13:29',NULL,'2024-12-26 15:13:29',_binary '\0',1),(35969,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 15:13:29','2024-12-26 15:13:30',155,0,'',NULL,'2024-12-26 15:13:30',NULL,'2024-12-26 15:13:30',_binary '\0',1),(35970,'',1,2,'yudao-server','GET','/admin-api/infra/codegen/table/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 代码生成器','获得表定义分页',1,'2024-12-26 15:13:33','2024-12-26 15:13:33',34,403,'没有该操作权限',NULL,'2024-12-26 15:13:33',NULL,'2024-12-26 15:13:33',_binary '\0',1),(35971,'',1,2,'yudao-server','GET','/admin-api/infra/demo01-contact/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"sex\\\":\\\"\\\",\\\"name\\\":\\\"\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 示例联系人','获得示例联系人分页',1,'2024-12-26 15:13:36','2024-12-26 15:13:36',17,403,'没有该操作权限',NULL,'2024-12-26 15:13:36',NULL,'2024-12-26 15:13:36',_binary '\0',1),(35972,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:13:38','2024-12-26 15:13:38',15,403,'没有该操作权限',NULL,'2024-12-26 15:13:38',NULL,'2024-12-26 15:13:38',_binary '\0',1),(35973,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:13:38','2024-12-26 15:13:38',39,0,'',NULL,'2024-12-26 15:13:38',NULL,'2024-12-26 15:13:38',_binary '\0',1),(35974,'',0,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 15:14:50','2024-12-26 15:14:50',40,0,'',NULL,'2024-12-26 15:14:50',NULL,'2024-12-26 15:14:50',_binary '\0',0),(35975,'',1,2,'yudao-server','GET','/admin-api/system/menu/list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 菜单','获取菜单列表',1,'2024-12-26 15:22:34','2024-12-26 15:22:34',69,403,'没有该操作权限',NULL,'2024-12-26 15:22:34',NULL,'2024-12-26 15:22:34',_binary '\0',1),(35976,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:22:58','2024-12-26 15:22:58',100,0,'',NULL,'2024-12-26 15:22:58',NULL,'2024-12-26 15:22:58',_binary '\0',1),(35977,'',1,2,'yudao-server','GET','/admin-api/system/menu/list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 菜单','获取菜单列表',1,'2024-12-26 15:22:58','2024-12-26 15:22:58',19,403,'没有该操作权限',NULL,'2024-12-26 15:22:58',NULL,'2024-12-26 15:22:58',_binary '\0',1),(35978,'',1,2,'yudao-server','POST','/admin-api/system/auth/logout','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','登出系统',2,'2024-12-26 15:23:11','2024-12-26 15:23:11',139,0,'',NULL,'2024-12-26 15:23:11',NULL,'2024-12-26 15:23:11',_binary '\0',1),(35979,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:23:11','2024-12-26 15:23:11',33,0,'',NULL,'2024-12-26 15:23:11',NULL,'2024-12-26 15:23:11',_binary '\0',1),(35980,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:23:11','2024-12-26 15:23:11',34,0,'',NULL,'2024-12-26 15:23:11',NULL,'2024-12-26 15:23:11',_binary '\0',1),(35981,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:23:11','2024-12-26 15:23:11',133,0,'',NULL,'2024-12-26 15:23:11',NULL,'2024-12-26 15:23:11',_binary '\0',1),(35982,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:23:11','2024-12-26 15:23:11',133,0,'',NULL,'2024-12-26 15:23:11',NULL,'2024-12-26 15:23:11',_binary '\0',1),(35983,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 15:23:24','2024-12-26 15:23:24',3,0,'',NULL,'2024-12-26 15:23:24',NULL,'2024-12-26 15:23:24',_binary '\0',1),(35984,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 15:24:25','2024-12-26 15:24:25',18,0,'',NULL,'2024-12-26 15:24:25',NULL,'2024-12-26 15:24:25',_binary '\0',1),(35985,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:25:03','2024-12-26 15:25:03',94,0,'',NULL,'2024-12-26 15:25:03',NULL,'2024-12-26 15:25:03',_binary '\0',1),(35986,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:25:03','2024-12-26 15:25:03',94,0,'',NULL,'2024-12-26 15:25:03',NULL,'2024-12-26 15:25:03',_binary '\0',1),(35987,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:25:03','2024-12-26 15:25:03',103,0,'',NULL,'2024-12-26 15:25:03',NULL,'2024-12-26 15:25:03',_binary '\0',1),(35988,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:25:03','2024-12-26 15:25:03',102,0,'',NULL,'2024-12-26 15:25:03',NULL,'2024-12-26 15:25:03',_binary '\0',1),(35989,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"芋道源码\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 15:25:04','2024-12-26 15:25:04',34,0,'',NULL,'2024-12-26 15:25:04',NULL,'2024-12-26 15:25:04',_binary '\0',1),(35990,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"芋道源码\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 15:25:05','2024-12-26 15:25:05',229,0,'',NULL,'2024-12-26 15:25:05',NULL,'2024-12-26 15:25:05',_binary '\0',1),(35991,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:25:05','2024-12-26 15:25:05',42,0,'',NULL,'2024-12-26 15:25:05',NULL,'2024-12-26 15:25:05',_binary '\0',1),(35992,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 15:25:05','2024-12-26 15:25:05',108,0,'',NULL,'2024-12-26 15:25:05',NULL,'2024-12-26 15:25:05',_binary '\0',1),(35993,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:25:09','2024-12-26 15:25:09',53,403,'没有该操作权限',NULL,'2024-12-26 15:25:09',NULL,'2024-12-26 15:25:09',_binary '\0',1),(35994,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:26:53','2024-12-26 15:27:27',34351,0,'',NULL,'2024-12-26 15:27:27',NULL,'2024-12-26 15:27:27',_binary '\0',1),(35995,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:31:21','2024-12-26 15:31:26',5198,0,'',NULL,'2024-12-26 15:31:26',NULL,'2024-12-26 15:31:26',_binary '\0',1),(35996,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:31:27','2024-12-26 15:31:40',13251,403,'没有该操作权限',NULL,'2024-12-26 15:31:40',NULL,'2024-12-26 15:31:40',_binary '\0',1),(35997,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:31:40','2024-12-26 15:31:40',56,0,'',NULL,'2024-12-26 15:31:40',NULL,'2024-12-26 15:31:40',_binary '\0',1),(35998,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:31:40','2024-12-26 15:31:40',56,0,'',NULL,'2024-12-26 15:31:40',NULL,'2024-12-26 15:31:40',_binary '\0',1),(35999,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:31:40','2024-12-26 15:31:40',83,0,'',NULL,'2024-12-26 15:31:40',NULL,'2024-12-26 15:31:40',_binary '\0',1),(36000,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:31:40','2024-12-26 15:31:40',88,0,'',NULL,'2024-12-26 15:31:40',NULL,'2024-12-26 15:31:40',_binary '\0',1),(36001,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"芋道源码\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 15:31:44','2024-12-26 15:31:44',41,0,'',NULL,'2024-12-26 15:31:44',NULL,'2024-12-26 15:31:44',_binary '\0',1),(36002,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"芋道源码\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 15:31:44','2024-12-26 15:31:45',245,0,'',NULL,'2024-12-26 15:31:45',NULL,'2024-12-26 15:31:45',_binary '\0',1),(36003,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:31:45','2024-12-26 15:31:45',47,0,'',NULL,'2024-12-26 15:31:45',NULL,'2024-12-26 15:31:45',_binary '\0',1),(36004,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 15:31:45','2024-12-26 15:31:45',115,0,'',NULL,'2024-12-26 15:31:45',NULL,'2024-12-26 15:31:45',_binary '\0',1),(36005,'',1,2,'yudao-server','GET','/admin-api/infra/codegen/table/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 代码生成器','获得表定义分页',1,'2024-12-26 15:31:47','2024-12-26 15:31:47',33,403,'没有该操作权限',NULL,'2024-12-26 15:31:47',NULL,'2024-12-26 15:31:47',_binary '\0',1),(36006,'',1,2,'yudao-server','GET','/admin-api/infra/demo01-contact/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"sex\\\":\\\"\\\",\\\"name\\\":\\\"\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 示例联系人','获得示例联系人分页',1,'2024-12-26 15:31:50','2024-12-26 15:31:50',17,403,'没有该操作权限',NULL,'2024-12-26 15:31:50',NULL,'2024-12-26 15:31:50',_binary '\0',1),(36007,'',1,2,'yudao-server','GET','/admin-api/infra/demo03-student/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"sex\\\":\\\"\\\",\\\"name\\\":\\\"\\\",\\\"pageSize\\\":\\\"10\\\",\\\"description\\\":\\\"\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 学生','获得学生分页',1,'2024-12-26 15:31:52','2024-12-26 15:31:52',33,403,'没有该操作权限',NULL,'2024-12-26 15:31:52',NULL,'2024-12-26 15:31:52',_binary '\0',1),(36008,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:31:58','2024-12-26 15:31:58',26,403,'没有该操作权限',NULL,'2024-12-26 15:31:58',NULL,'2024-12-26 15:31:58',_binary '\0',1),(36009,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获得租户套餐分页',1,'2024-12-26 15:32:01','2024-12-26 15:32:01',24,403,'没有该操作权限',NULL,'2024-12-26 15:32:01',NULL,'2024-12-26 15:32:01',_binary '\0',1),(36010,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:33:13','2024-12-26 15:33:13',84,0,'',NULL,'2024-12-26 15:33:13',NULL,'2024-12-26 15:33:13',_binary '\0',1),(36011,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:33:14','2024-12-26 15:33:14',48,403,'没有该操作权限',NULL,'2024-12-26 15:33:14',NULL,'2024-12-26 15:33:14',_binary '\0',1),(36012,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获得租户套餐分页',1,'2024-12-26 15:33:17','2024-12-26 15:33:17',17,403,'没有该操作权限',NULL,'2024-12-26 15:33:17',NULL,'2024-12-26 15:33:17',_binary '\0',1),(36013,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:33:24','2024-12-26 15:33:24',12,403,'没有该操作权限',NULL,'2024-12-26 15:33:24',NULL,'2024-12-26 15:33:24',_binary '\0',1),(36014,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:33:24','2024-12-26 15:33:24',28,0,'',NULL,'2024-12-26 15:33:24',NULL,'2024-12-26 15:33:24',_binary '\0',1),(36015,'',1,2,'yudao-server','GET','/admin-api/system/role/page','{\"query\":\"{\\\"code\\\":\\\"\\\",\\\"pageNo\\\":\\\"1\\\",\\\"name\\\":\\\"\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 角色','获得角色分页',1,'2024-12-26 15:33:25','2024-12-26 15:33:25',33,403,'没有该操作权限',NULL,'2024-12-26 15:33:25',NULL,'2024-12-26 15:33:25',_binary '\0',1),(36016,'',1,2,'yudao-server','POST','/admin-api/system/auth/logout','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','登出系统',2,'2024-12-26 15:44:31','2024-12-26 15:44:31',199,0,'',NULL,'2024-12-26 15:44:31',NULL,'2024-12-26 15:44:31',_binary '\0',1),(36017,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:44:31','2024-12-26 15:44:31',22,0,'',NULL,'2024-12-26 15:44:31',NULL,'2024-12-26 15:44:31',_binary '\0',1),(36018,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 15:44:31','2024-12-26 15:44:31',22,0,'',NULL,'2024-12-26 15:44:31',NULL,'2024-12-26 15:44:31',_binary '\0',1),(36019,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:44:31','2024-12-26 15:44:31',47,0,'',NULL,'2024-12-26 15:44:31',NULL,'2024-12-26 15:44:31',_binary '\0',1),(36020,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 15:44:31','2024-12-26 15:44:31',47,0,'',NULL,'2024-12-26 15:44:31',NULL,'2024-12-26 15:44:31',_binary '\0',1),(36021,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"芋道源码\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 15:44:33','2024-12-26 15:44:33',37,0,'',NULL,'2024-12-26 15:44:33',NULL,'2024-12-26 15:44:33',_binary '\0',1),(36022,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"芋道源码\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 15:44:33','2024-12-26 15:44:33',172,0,'',NULL,'2024-12-26 15:44:33',NULL,'2024-12-26 15:44:33',_binary '\0',1),(36023,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 15:44:33','2024-12-26 15:44:33',97,0,'',NULL,'2024-12-26 15:44:33',NULL,'2024-12-26 15:44:33',_binary '\0',1),(36024,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:44:37','2024-12-26 15:44:37',105,0,'',NULL,'2024-12-26 15:44:37',NULL,'2024-12-26 15:44:37',_binary '\0',1),(36025,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获取租户套餐精简信息列表',1,'2024-12-26 15:44:37','2024-12-26 15:44:37',18,0,'',NULL,'2024-12-26 15:44:37',NULL,'2024-12-26 15:44:37',_binary '\0',1),(36026,'',1,2,'yudao-server','DELETE','/admin-api/system/tenant/delete','{\"query\":\"{\\\"id\\\":\\\"121\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','删除租户',4,'2024-12-26 15:48:04','2024-12-26 15:48:04',122,0,'',NULL,'2024-12-26 15:48:04',NULL,'2024-12-26 15:48:04',_binary '\0',1),(36027,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:48:04','2024-12-26 15:48:04',31,0,'',NULL,'2024-12-26 15:48:04',NULL,'2024-12-26 15:48:04',_binary '\0',1),(36028,'',1,2,'yudao-server','GET','/admin-api/system/tenant/get','{\"query\":\"{\\\"id\\\":\\\"1\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户',1,'2024-12-26 15:48:06','2024-12-26 15:48:06',74,0,'',NULL,'2024-12-26 15:48:06',NULL,'2024-12-26 15:48:06',_binary '\0',1),(36029,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获取租户套餐精简信息列表',1,'2024-12-26 15:48:06','2024-12-26 15:48:06',15,0,'',NULL,'2024-12-26 15:48:06',NULL,'2024-12-26 15:48:06',_binary '\0',1),(36030,'',1,2,'yudao-server','PUT','/admin-api/system/tenant/update','{\"query\":null,\"body\":\"{\\\"id\\\":1,\\\"name\\\":\\\"三胖科技\\\",\\\"contactName\\\":\\\"三胖\\\",\\\"contactMobile\\\":\\\"***********\\\",\\\"status\\\":0,\\\"website\\\":\\\"www.iocoder.cn\\\",\\\"packageId\\\":0,\\\"expireTime\\\":*************,\\\"accountCount\\\":9999,\\\"createTime\\\":*************}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','更新租户',3,'2024-12-26 15:48:19','2024-12-26 15:48:19',158,**********,'系统租户不能进行修改、删除等操作！',NULL,'2024-12-26 15:48:19',NULL,'2024-12-26 15:48:19',_binary '\0',1),(36031,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 15:48:43','2024-12-26 15:48:43',69,0,'',NULL,'2024-12-26 15:48:43',NULL,'2024-12-26 15:48:43',_binary '\0',1),(36032,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:48:44','2024-12-26 15:48:44',69,0,'',NULL,'2024-12-26 15:48:44',NULL,'2024-12-26 15:48:44',_binary '\0',1),(36033,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获取租户套餐精简信息列表',1,'2024-12-26 15:48:44','2024-12-26 15:48:44',18,0,'',NULL,'2024-12-26 15:48:44',NULL,'2024-12-26 15:48:44',_binary '\0',1),(36034,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 15:48:57','2024-12-26 15:48:57',119,0,'',NULL,'2024-12-26 15:48:57',NULL,'2024-12-26 15:48:57',_binary '\0',1),(36035,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获取租户套餐精简信息列表',1,'2024-12-26 15:48:57','2024-12-26 15:48:57',12,0,'',NULL,'2024-12-26 15:48:57',NULL,'2024-12-26 15:48:57',_binary '\0',1),(36036,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获得租户套餐分页',1,'2024-12-26 15:48:59','2024-12-26 15:49:00',100,0,'',NULL,'2024-12-26 15:49:00',NULL,'2024-12-26 15:49:00',_binary '\0',1),(36037,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:49:02','2024-12-26 15:49:02',57,0,'',NULL,'2024-12-26 15:49:02',NULL,'2024-12-26 15:49:02',_binary '\0',1),(36038,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:49:02','2024-12-26 15:49:02',111,0,'',NULL,'2024-12-26 15:49:02',NULL,'2024-12-26 15:49:02',_binary '\0',1),(36039,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"100\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:49:04','2024-12-26 15:49:04',97,0,'',NULL,'2024-12-26 15:49:04',NULL,'2024-12-26 15:49:04',_binary '\0',1),(36040,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"100\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:49:11','2024-12-26 15:49:11',117,0,'',NULL,'2024-12-26 15:49:11',NULL,'2024-12-26 15:49:11',_binary '\0',1),(36041,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:49:12','2024-12-26 15:49:12',20,0,'',NULL,'2024-12-26 15:49:12',NULL,'2024-12-26 15:49:12',_binary '\0',1),(36042,'',1,2,'yudao-server','GET','/admin-api/system/post/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 岗位','获取岗位全列表',1,'2024-12-26 15:49:12','2024-12-26 15:49:12',34,0,'',NULL,'2024-12-26 15:49:12',NULL,'2024-12-26 15:49:12',_binary '\0',1),(36043,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 15:49:20','2024-12-26 15:49:20',144,0,'',NULL,'2024-12-26 15:49:20',NULL,'2024-12-26 15:49:20',_binary '\0',1),(36044,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 15:49:20','2024-12-26 15:49:20',61,0,'',NULL,'2024-12-26 15:49:20',NULL,'2024-12-26 15:49:20',_binary '\0',1),(36045,'',1,2,'yudao-server','GET','/admin-api/system/dept/get','{\"query\":\"{\\\"id\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获得部门信息',1,'2024-12-26 15:49:24','2024-12-26 15:49:24',77,0,'',NULL,'2024-12-26 15:49:24',NULL,'2024-12-26 15:49:24',_binary '\0',1),(36046,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 15:49:24','2024-12-26 15:49:24',34,0,'',NULL,'2024-12-26 15:49:24',NULL,'2024-12-26 15:49:24',_binary '\0',1),(36047,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:49:24','2024-12-26 15:49:24',23,0,'',NULL,'2024-12-26 15:49:24',NULL,'2024-12-26 15:49:24',_binary '\0',1),(36048,'',1,2,'yudao-server','GET','/admin-api/system/user/get','{\"query\":\"{\\\"id\\\":\\\"1\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户详情',1,'2024-12-26 15:49:50','2024-12-26 15:49:50',127,0,'',NULL,'2024-12-26 15:49:50',NULL,'2024-12-26 15:49:50',_binary '\0',1),(36049,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:49:50','2024-12-26 15:49:50',21,0,'',NULL,'2024-12-26 15:49:50',NULL,'2024-12-26 15:49:50',_binary '\0',1),(36050,'',1,2,'yudao-server','GET','/admin-api/system/post/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 岗位','获取岗位全列表',1,'2024-12-26 15:49:50','2024-12-26 15:49:50',12,0,'',NULL,'2024-12-26 15:49:50',NULL,'2024-12-26 15:49:50',_binary '\0',1),(36051,'',1,2,'yudao-server','PUT','/admin-api/system/user/update','{\"query\":null,\"body\":\"{\\\"id\\\":1,\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"贾三胖\\\",\\\"remark\\\":\\\"管理员\\\",\\\"deptId\\\":103,\\\"deptName\\\":\\\"研发部门\\\",\\\"postIds\\\":[1,2],\\\"email\\\":\\\"<EMAIL>\\\",\\\"mobile\\\":\\\"***********\\\",\\\"sex\\\":2,\\\"avatar\\\":\\\"http://test.yudao.iocoder.cn/bf2002b38950c904243be7c825d3f82e29f25a44526583c3fde2ebdff3a87f75.png\\\",\\\"status\\\":0,\\\"loginIp\\\":\\\"0:0:0:0:0:0:0:1\\\",\\\"loginDate\\\":1735199073000,\\\"createTime\\\":*************}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','修改用户',3,'2024-12-26 15:50:15','2024-12-26 15:50:15',412,0,'',NULL,'2024-12-26 15:50:15',NULL,'2024-12-26 15:50:15',_binary '\0',1),(36052,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"100\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:15','2024-12-26 15:50:16',109,0,'',NULL,'2024-12-26 15:50:16',NULL,'2024-12-26 15:50:16',_binary '\0',1),(36053,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"103\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:34','2024-12-26 15:50:34',135,0,'',NULL,'2024-12-26 15:50:34',NULL,'2024-12-26 15:50:34',_binary '\0',1),(36054,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"104\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:40','2024-12-26 15:50:40',98,0,'',NULL,'2024-12-26 15:50:40',NULL,'2024-12-26 15:50:40',_binary '\0',1),(36055,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"105\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:41','2024-12-26 15:50:41',61,0,'',NULL,'2024-12-26 15:50:41',NULL,'2024-12-26 15:50:41',_binary '\0',1),(36056,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"106\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:43','2024-12-26 15:50:44',93,0,'',NULL,'2024-12-26 15:50:44',NULL,'2024-12-26 15:50:44',_binary '\0',1),(36057,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"107\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:45','2024-12-26 15:50:45',96,0,'',NULL,'2024-12-26 15:50:45',NULL,'2024-12-26 15:50:45',_binary '\0',1),(36058,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"107\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:46','2024-12-26 15:50:46',96,0,'',NULL,'2024-12-26 15:50:46',NULL,'2024-12-26 15:50:46',_binary '\0',1),(36059,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"deptId\\\":\\\"100\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 15:50:47','2024-12-26 15:50:47',181,0,'',NULL,'2024-12-26 15:50:47',NULL,'2024-12-26 15:50:47',_binary '\0',1),(36060,'',1,2,'yudao-server','GET','/admin-api/system/dept/get','{\"query\":\"{\\\"id\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获得部门信息',1,'2024-12-26 15:50:56','2024-12-26 15:50:56',79,0,'',NULL,'2024-12-26 15:50:56',NULL,'2024-12-26 15:50:56',_binary '\0',1),(36061,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 15:50:56','2024-12-26 15:50:56',45,0,'',NULL,'2024-12-26 15:50:56',NULL,'2024-12-26 15:50:56',_binary '\0',1),(36062,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 15:50:56','2024-12-26 15:50:56',23,0,'',NULL,'2024-12-26 15:50:56',NULL,'2024-12-26 15:50:56',_binary '\0',1),(36063,'',1,2,'yudao-server','PUT','/admin-api/system/dept/update','{\"query\":null,\"body\":\"{\\\"id\\\":100,\\\"name\\\":\\\"三胖科技\\\",\\\"parentId\\\":0,\\\"sort\\\":0,\\\"leaderUserId\\\":1,\\\"phone\\\":\\\"15888888888\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"status\\\":0,\\\"createTime\\\":*************}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','更新部门',3,'2024-12-26 15:51:02','2024-12-26 15:51:02',206,0,'',NULL,'2024-12-26 15:51:02',NULL,'2024-12-26 15:51:02',_binary '\0',1),(36064,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 15:51:02','2024-12-26 15:51:02',34,0,'',NULL,'2024-12-26 15:51:02',NULL,'2024-12-26 15:51:02',_binary '\0',1),(36065,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:15:24','2024-12-26 16:15:25',78,0,'',NULL,'2024-12-26 16:15:25',NULL,'2024-12-26 16:15:25',_binary '\0',1),(36066,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:15:24','2024-12-26 16:15:25',78,0,'',NULL,'2024-12-26 16:15:25',NULL,'2024-12-26 16:15:25',_binary '\0',1),(36067,'',0,2,'yudao-server','POST','/admin-api/system/auth/refresh-token','{\"query\":\"{}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','刷新令牌',2,'2024-12-26 16:15:25','2024-12-26 16:15:25',159,0,'',NULL,'2024-12-26 16:15:25',NULL,'2024-12-26 16:15:25',_binary '\0',1),(36068,'',1,2,'yudao-server','GET','/admin-api/system/dept/get','{\"query\":\"{\\\"id\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获得部门信息',1,'2024-12-26 16:35:26','2024-12-26 16:35:26',74,0,'',NULL,'2024-12-26 16:35:26',NULL,'2024-12-26 16:35:26',_binary '\0',1),(36069,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:35:26','2024-12-26 16:35:26',38,0,'',NULL,'2024-12-26 16:35:26',NULL,'2024-12-26 16:35:26',_binary '\0',1),(36070,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:35:26','2024-12-26 16:35:26',25,0,'',NULL,'2024-12-26 16:35:26',NULL,'2024-12-26 16:35:26',_binary '\0',1),(36071,'',1,2,'yudao-server','PUT','/admin-api/system/dept/update','{\"query\":null,\"body\":\"{\\\"id\\\":100,\\\"name\\\":\\\"三胖科技\\\",\\\"parentId\\\":0,\\\"sort\\\":0,\\\"leaderUserId\\\":1,\\\"phone\\\":\\\"15888888888\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"status\\\":0,\\\"createTime\\\":*************}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','更新部门',3,'2024-12-26 16:35:29','2024-12-26 16:35:29',73,0,'',NULL,'2024-12-26 16:35:29',NULL,'2024-12-26 16:35:29',_binary '\0',1),(36072,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 16:35:29','2024-12-26 16:35:29',36,0,'',NULL,'2024-12-26 16:35:29',NULL,'2024-12-26 16:35:29',_binary '\0',1),(36073,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 16:35:31','2024-12-26 16:35:31',60,0,'',NULL,'2024-12-26 16:35:31',NULL,'2024-12-26 16:35:31',_binary '\0',1),(36074,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 16:35:31','2024-12-26 16:35:31',62,0,'',NULL,'2024-12-26 16:35:31',NULL,'2024-12-26 16:35:31',_binary '\0',1),(36075,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:35:31','2024-12-26 16:35:31',32,0,'',NULL,'2024-12-26 16:35:31',NULL,'2024-12-26 16:35:31',_binary '\0',1),(36076,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:35:35','2024-12-26 16:35:35',46,0,'',NULL,'2024-12-26 16:35:35',NULL,'2024-12-26 16:35:35',_binary '\0',1),(36077,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:35:35','2024-12-26 16:35:35',65,0,'',NULL,'2024-12-26 16:35:35',NULL,'2024-12-26 16:35:35',_binary '\0',1),(36078,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"2\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:35:46','2024-12-26 16:35:46',53,0,'',NULL,'2024-12-26 16:35:46',NULL,'2024-12-26 16:35:46',_binary '\0',1),(36079,'',1,2,'yudao-server','POST','/admin-api/system/auth/logout','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','登出系统',2,'2024-12-26 16:35:53','2024-12-26 16:35:54',161,0,'',NULL,'2024-12-26 16:35:54',NULL,'2024-12-26 16:35:54',_binary '\0',1),(36080,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:35:54','2024-12-26 16:35:54',28,0,'',NULL,'2024-12-26 16:35:54',NULL,'2024-12-26 16:35:54',_binary '\0',1),(36081,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:35:54','2024-12-26 16:35:54',28,0,'',NULL,'2024-12-26 16:35:54',NULL,'2024-12-26 16:35:54',_binary '\0',1),(36082,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:35:54','2024-12-26 16:35:54',75,0,'',NULL,'2024-12-26 16:35:54',NULL,'2024-12-26 16:35:54',_binary '\0',1),(36083,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:35:54','2024-12-26 16:35:54',76,0,'',NULL,'2024-12-26 16:35:54',NULL,'2024-12-26 16:35:54',_binary '\0',1),(36084,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"芋道源码\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:35:56','2024-12-26 16:35:56',18,0,'',NULL,'2024-12-26 16:35:56',NULL,'2024-12-26 16:35:56',_binary '\0',1),(36085,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"芋道源码\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:35:56','2024-12-26 16:35:56',2,0,'',NULL,'2024-12-26 16:35:56',NULL,'2024-12-26 16:35:56',_binary '\0',0),(36086,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"三胖科技\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:36:01','2024-12-26 16:36:01',32,0,'',NULL,'2024-12-26 16:36:01',NULL,'2024-12-26 16:36:01',_binary '\0',0),(36087,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"三胖科技\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 16:36:01','2024-12-26 16:36:01',203,0,'',NULL,'2024-12-26 16:36:01',NULL,'2024-12-26 16:36:01',_binary '\0',1),(36088,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 16:36:01','2024-12-26 16:36:01',100,0,'',NULL,'2024-12-26 16:36:01',NULL,'2024-12-26 16:36:01',_binary '\0',1),(36089,'',1,2,'yudao-server','GET','/admin-api/system/tenant/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','获得租户分页',1,'2024-12-26 16:36:07','2024-12-26 16:36:07',32,0,'',NULL,'2024-12-26 16:36:07',NULL,'2024-12-26 16:36:07',_binary '\0',1),(36090,'',1,2,'yudao-server','GET','/admin-api/system/tenant-package/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户套餐','获取租户套餐精简信息列表',1,'2024-12-26 16:36:07','2024-12-26 16:36:07',23,0,'',NULL,'2024-12-26 16:36:07',NULL,'2024-12-26 16:36:07',_binary '\0',1),(36091,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 16:36:54','2024-12-26 16:36:54',102,0,'',NULL,'2024-12-26 16:36:54',NULL,'2024-12-26 16:36:54',_binary '\0',1),(36092,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:36:55','2024-12-26 16:36:55',64,0,'',NULL,'2024-12-26 16:36:55',NULL,'2024-12-26 16:36:55',_binary '\0',1),(36093,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:37:13','2024-12-26 16:37:13',55,0,'',NULL,'2024-12-26 16:37:13',NULL,'2024-12-26 16:37:13',_binary '\0',1),(36094,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:37:13','2024-12-26 16:37:13',48,0,'',NULL,'2024-12-26 16:37:13',NULL,'2024-12-26 16:37:13',_binary '\0',1),(36095,'',1,2,'yudao-server','POST','/admin-api/system/dept/create','{\"query\":null,\"body\":\"{\\\"title\\\":\\\"\\\",\\\"parentId\\\":0,\\\"name\\\":\\\"测试租户\\\",\\\"sort\\\":1,\\\"status\\\":0}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','创建部门',2,'2024-12-26 16:37:28','2024-12-26 16:37:28',170,0,'',NULL,'2024-12-26 16:37:28',NULL,'2024-12-26 16:37:28',_binary '\0',1),(36096,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 16:37:28','2024-12-26 16:37:28',56,0,'',NULL,'2024-12-26 16:37:28',NULL,'2024-12-26 16:37:28',_binary '\0',1),(36097,'',1,2,'yudao-server','GET','/admin-api/system/dept/get','{\"query\":\"{\\\"id\\\":\\\"115\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获得部门信息',1,'2024-12-26 16:37:32','2024-12-26 16:37:32',61,0,'',NULL,'2024-12-26 16:37:32',NULL,'2024-12-26 16:37:32',_binary '\0',1),(36098,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:37:32','2024-12-26 16:37:32',40,0,'',NULL,'2024-12-26 16:37:32',NULL,'2024-12-26 16:37:32',_binary '\0',1),(36099,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:37:32','2024-12-26 16:37:32',28,0,'',NULL,'2024-12-26 16:37:33',NULL,'2024-12-26 16:37:33',_binary '\0',1),(36100,'',1,2,'yudao-server','PUT','/admin-api/system/dept/update','{\"query\":null,\"body\":\"{\\\"id\\\":115,\\\"name\\\":\\\"测试租户\\\",\\\"parentId\\\":0,\\\"sort\\\":0,\\\"leaderUserId\\\":null,\\\"phone\\\":null,\\\"email\\\":null,\\\"status\\\":0,\\\"createTime\\\":1735202248000}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','更新部门',3,'2024-12-26 16:37:36','2024-12-26 16:37:36',101,0,'',NULL,'2024-12-26 16:37:36',NULL,'2024-12-26 16:37:36',_binary '\0',1),(36101,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 16:37:36','2024-12-26 16:37:36',37,0,'',NULL,'2024-12-26 16:37:36',NULL,'2024-12-26 16:37:36',_binary '\0',1),(36102,'',1,2,'yudao-server','GET','/admin-api/system/dept/get','{\"query\":\"{\\\"id\\\":\\\"115\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获得部门信息',1,'2024-12-26 16:37:51','2024-12-26 16:37:51',117,0,'',NULL,'2024-12-26 16:37:51',NULL,'2024-12-26 16:37:51',_binary '\0',1),(36103,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:37:51','2024-12-26 16:37:51',39,0,'',NULL,'2024-12-26 16:37:51',NULL,'2024-12-26 16:37:51',_binary '\0',1),(36104,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:37:51','2024-12-26 16:37:51',25,0,'',NULL,'2024-12-26 16:37:51',NULL,'2024-12-26 16:37:51',_binary '\0',1),(36105,'',1,2,'yudao-server','GET','/admin-api/system/user/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获取用户精简信息列表',1,'2024-12-26 16:37:54','2024-12-26 16:37:54',50,0,'',NULL,'2024-12-26 16:37:54',NULL,'2024-12-26 16:37:54',_binary '\0',1),(36106,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:37:54','2024-12-26 16:37:54',22,0,'',NULL,'2024-12-26 16:37:54',NULL,'2024-12-26 16:37:54',_binary '\0',1),(36107,'',1,2,'yudao-server','POST','/admin-api/system/dept/create','{\"query\":null,\"body\":\"{\\\"title\\\":\\\"\\\",\\\"parentId\\\":115,\\\"name\\\":\\\"西安分公司\\\",\\\"sort\\\":1,\\\"status\\\":0}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','创建部门',2,'2024-12-26 16:38:17','2024-12-26 16:38:17',129,0,'',NULL,'2024-12-26 16:38:17',NULL,'2024-12-26 16:38:17',_binary '\0',1),(36108,'',1,2,'yudao-server','GET','/admin-api/system/dept/list','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"100\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门列表',1,'2024-12-26 16:38:17','2024-12-26 16:38:17',131,0,'',NULL,'2024-12-26 16:38:17',NULL,'2024-12-26 16:38:17',_binary '\0',1),(36109,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:38:28','2024-12-26 16:38:28',61,0,'',NULL,'2024-12-26 16:38:28',NULL,'2024-12-26 16:38:28',_binary '\0',1),(36110,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:38:28','2024-12-26 16:38:28',78,0,'',NULL,'2024-12-26 16:38:28',NULL,'2024-12-26 16:38:28',_binary '\0',1),(36111,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:39:03','2024-12-26 16:39:03',41,0,'',NULL,'2024-12-26 16:39:03',NULL,'2024-12-26 16:39:03',_binary '\0',1),(36112,'',1,2,'yudao-server','GET','/admin-api/system/post/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 岗位','获取岗位全列表',1,'2024-12-26 16:39:03','2024-12-26 16:39:03',35,0,'',NULL,'2024-12-26 16:39:03',NULL,'2024-12-26 16:39:03',_binary '\0',1),(36113,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"2\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:39:07','2024-12-26 16:39:07',120,0,'',NULL,'2024-12-26 16:39:07',NULL,'2024-12-26 16:39:07',_binary '\0',1),(36114,'',1,2,'yudao-server','GET','/admin-api/system/dict-data/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 字典数据','获得全部字典数据列表',1,'2024-12-26 16:39:21','2024-12-26 16:39:21',76,0,'',NULL,'2024-12-26 16:39:21',NULL,'2024-12-26 16:39:21',_binary '\0',1),(36115,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:39:21','2024-12-26 16:39:21',61,0,'',NULL,'2024-12-26 16:39:21',NULL,'2024-12-26 16:39:21',_binary '\0',1),(36116,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:39:21','2024-12-26 16:39:21',88,0,'',NULL,'2024-12-26 16:39:21',NULL,'2024-12-26 16:39:21',_binary '\0',1),(36117,'',1,2,'yudao-server','PUT','/admin-api/system/user/update-password','{\"query\":null,\"body\":\"{\\\"id\\\":131}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','重置用户密码',3,'2024-12-26 16:39:34','2024-12-26 16:39:34',150,0,'',NULL,'2024-12-26 16:39:34',NULL,'2024-12-26 16:39:34',_binary '\0',1),(36118,'',1,2,'yudao-server','POST','/admin-api/system/auth/logout','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','登出系统',2,'2024-12-26 16:39:40','2024-12-26 16:39:40',117,0,'',NULL,'2024-12-26 16:39:40',NULL,'2024-12-26 16:39:40',_binary '\0',1),(36119,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:39:40','2024-12-26 16:39:40',39,0,'',NULL,'2024-12-26 16:39:40',NULL,'2024-12-26 16:39:40',_binary '\0',1),(36120,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:39:40','2024-12-26 16:39:40',42,0,'',NULL,'2024-12-26 16:39:40',NULL,'2024-12-26 16:39:40',_binary '\0',1),(36121,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:39:40','2024-12-26 16:39:40',84,0,'',NULL,'2024-12-26 16:39:40',NULL,'2024-12-26 16:39:40',_binary '\0',1),(36122,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:39:40','2024-12-26 16:39:40',84,0,'',NULL,'2024-12-26 16:39:40',NULL,'2024-12-26 16:39:40',_binary '\0',1),(36123,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"三胖科技\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:39:49','2024-12-26 16:39:49',33,0,'',NULL,'2024-12-26 16:39:49',NULL,'2024-12-26 16:39:49',_binary '\0',1),(36124,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"三胖科技\\\",\\\"username\\\":\\\"hh\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 16:39:49','2024-12-26 16:39:49',73,400,'请求参数不正确:账号长度为 4-16 位',NULL,'2024-12-26 16:39:49',NULL,'2024-12-26 16:39:49',_binary '\0',1),(36125,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"三胖科技\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:39:57','2024-12-26 16:39:57',40,0,'',NULL,'2024-12-26 16:39:57',NULL,'2024-12-26 16:39:57',_binary '\0',1),(36126,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"三胖科技\\\",\\\"username\\\":\\\"hh\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 16:39:57','2024-12-26 16:39:57',6,400,'请求参数不正确:账号长度为 4-16 位',NULL,'2024-12-26 16:39:57',NULL,'2024-12-26 16:39:57',_binary '\0',1),(36127,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"三胖科技\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:40:21','2024-12-26 16:40:21',65,0,'',NULL,'2024-12-26 16:40:21',NULL,'2024-12-26 16:40:21',_binary '\0',1),(36128,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"三胖科技\\\",\\\"username\\\":\\\"admin\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','使用账号密码登录',2,'2024-12-26 16:40:21','2024-12-26 16:40:21',209,0,'',NULL,'2024-12-26 16:40:21',NULL,'2024-12-26 16:40:21',_binary '\0',1),(36129,'',1,2,'yudao-server','GET','/admin-api/system/auth/get-permission-info','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','获取登录用户的权限信息',1,'2024-12-26 16:40:21','2024-12-26 16:40:21',121,0,'',NULL,'2024-12-26 16:40:21',NULL,'2024-12-26 16:40:21',_binary '\0',1),(36130,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:40:29','2024-12-26 16:40:29',54,0,'',NULL,'2024-12-26 16:40:29',NULL,'2024-12-26 16:40:29',_binary '\0',1),(36131,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:40:29','2024-12-26 16:40:29',76,0,'',NULL,'2024-12-26 16:40:29',NULL,'2024-12-26 16:40:29',_binary '\0',1),(36132,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"2\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:40:32','2024-12-26 16:40:32',140,0,'',NULL,'2024-12-26 16:40:32',NULL,'2024-12-26 16:40:32',_binary '\0',1),(36133,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:40:35','2024-12-26 16:40:35',102,0,'',NULL,'2024-12-26 16:40:35',NULL,'2024-12-26 16:40:35',_binary '\0',1),(36134,'',1,2,'yudao-server','GET','/admin-api/system/dept/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 部门','获取部门精简信息列表',1,'2024-12-26 16:40:47','2024-12-26 16:40:47',66,0,'',NULL,'2024-12-26 16:40:47',NULL,'2024-12-26 16:40:47',_binary '\0',1),(36135,'',1,2,'yudao-server','GET','/admin-api/system/post/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 岗位','获取岗位全列表',1,'2024-12-26 16:40:47','2024-12-26 16:40:47',19,0,'',NULL,'2024-12-26 16:40:47',NULL,'2024-12-26 16:40:47',_binary '\0',1),(36136,'',1,2,'yudao-server','POST','/admin-api/system/user/create','{\"query\":null,\"body\":\"{\\\"nickname\\\":\\\"贾三胖\\\",\\\"deptId\\\":115,\\\"mobile\\\":\\\"\\\",\\\"email\\\":\\\"\\\",\\\"username\\\":\\\"admin\\\",\\\"sex\\\":2,\\\"postIds\\\":[1],\\\"remark\\\":\\\"\\\",\\\"status\\\":0,\\\"roleIds\\\":[]}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','新增用户',2,'2024-12-26 16:41:19','2024-12-26 16:41:19',226,1002003000,'用户账号已经存在',NULL,'2024-12-26 16:41:19',NULL,'2024-12-26 16:41:19',_binary '\0',1),(36137,'',1,2,'yudao-server','POST','/admin-api/system/user/create','{\"query\":null,\"body\":\"{\\\"nickname\\\":\\\"贾三胖\\\",\\\"deptId\\\":115,\\\"mobile\\\":\\\"\\\",\\\"email\\\":\\\"\\\",\\\"username\\\":\\\"admin\\\",\\\"sex\\\":2,\\\"postIds\\\":[1],\\\"remark\\\":\\\"\\\",\\\"status\\\":0,\\\"roleIds\\\":[]}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','新增用户',2,'2024-12-26 16:41:24','2024-12-26 16:41:24',107,1002003000,'用户账号已经存在',NULL,'2024-12-26 16:41:24',NULL,'2024-12-26 16:41:24',_binary '\0',1),(36138,'',1,2,'yudao-server','POST','/admin-api/system/user/create','{\"query\":null,\"body\":\"{\\\"nickname\\\":\\\"贾三胖\\\",\\\"deptId\\\":115,\\\"mobile\\\":\\\"\\\",\\\"email\\\":\\\"\\\",\\\"username\\\":\\\"admin-test\\\",\\\"sex\\\":2,\\\"postIds\\\":[1],\\\"remark\\\":\\\"\\\",\\\"status\\\":0,\\\"roleIds\\\":[]}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','新增用户',2,'2024-12-26 16:41:36','2024-12-26 16:41:36',63,400,'请求参数不正确:用户账号由 数字、字母 组成',NULL,'2024-12-26 16:41:36',NULL,'2024-12-26 16:41:36',_binary '\0',1),(36139,'',1,2,'yudao-server','POST','/admin-api/system/user/create','{\"query\":null,\"body\":\"{\\\"nickname\\\":\\\"贾三胖\\\",\\\"deptId\\\":115,\\\"mobile\\\":\\\"\\\",\\\"email\\\":\\\"\\\",\\\"username\\\":\\\"admin001\\\",\\\"sex\\\":2,\\\"postIds\\\":[1],\\\"remark\\\":\\\"\\\",\\\"status\\\":0,\\\"roleIds\\\":[]}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','新增用户',2,'2024-12-26 16:41:43','2024-12-26 16:41:43',392,0,'',NULL,'2024-12-26 16:41:43',NULL,'2024-12-26 16:41:43',_binary '\0',1),(36140,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:41:43','2024-12-26 16:41:43',69,0,'',NULL,'2024-12-26 16:41:43',NULL,'2024-12-26 16:41:43',_binary '\0',1),(36141,'',1,2,'yudao-server','PUT','/admin-api/system/user/update-password','{\"query\":null,\"body\":\"{\\\"id\\\":140}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','重置用户密码',3,'2024-12-26 16:41:51','2024-12-26 16:41:51',18,400,'请求参数不正确:密码不能为空',NULL,'2024-12-26 16:41:51',NULL,'2024-12-26 16:41:51',_binary '\0',1),(36142,'',1,2,'yudao-server','PUT','/admin-api/system/user/update-password','{\"query\":null,\"body\":\"{\\\"id\\\":140}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','重置用户密码',3,'2024-12-26 16:41:58','2024-12-26 16:41:58',11,400,'请求参数不正确:密码不能为空',NULL,'2024-12-26 16:41:58',NULL,'2024-12-26 16:41:58',_binary '\0',1),(36143,'',1,2,'yudao-server','PUT','/admin-api/system/user/update-password','{\"query\":null,\"body\":\"{\\\"id\\\":140}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','重置用户密码',3,'2024-12-26 16:42:04','2024-12-26 16:42:04',117,0,'',NULL,'2024-12-26 16:42:04',NULL,'2024-12-26 16:42:04',_binary '\0',1),(36144,'',1,2,'yudao-server','GET','/admin-api/system/permission/list-user-roles','{\"query\":\"{\\\"userId\\\":\\\"140\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 权限','获得管理员拥有的角色编号列表',1,'2024-12-26 16:42:07','2024-12-26 16:42:07',105,0,'',NULL,'2024-12-26 16:42:07',NULL,'2024-12-26 16:42:07',_binary '\0',1),(36145,'',1,2,'yudao-server','GET','/admin-api/system/role/simple-list','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 角色','获取角色精简信息列表',1,'2024-12-26 16:42:07','2024-12-26 16:42:07',38,0,'',NULL,'2024-12-26 16:42:07',NULL,'2024-12-26 16:42:07',_binary '\0',1),(36146,'',1,2,'yudao-server','POST','/admin-api/system/permission/assign-user-role','{\"query\":null,\"body\":\"{\\\"userId\\\":140,\\\"roleIds\\\":[1]}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 权限','赋予用户角色',2,'2024-12-26 16:42:17','2024-12-26 16:42:17',94,0,'',NULL,'2024-12-26 16:42:17',NULL,'2024-12-26 16:42:17',_binary '\0',1),(36147,'',1,2,'yudao-server','GET','/admin-api/system/user/page','{\"query\":\"{\\\"pageNo\\\":\\\"1\\\",\\\"pageSize\\\":\\\"10\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 用户','获得用户分页列表',1,'2024-12-26 16:42:17','2024-12-26 16:42:17',101,0,'',NULL,'2024-12-26 16:42:17',NULL,'2024-12-26 16:42:17',_binary '\0',1),(36148,'',1,2,'yudao-server','POST','/admin-api/system/auth/logout','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 认证','登出系统',2,'2024-12-26 16:42:23','2024-12-26 16:42:23',104,0,'',NULL,'2024-12-26 16:42:23',NULL,'2024-12-26 16:42:23',_binary '\0',1),(36149,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:42:23','2024-12-26 16:42:23',18,0,'',NULL,'2024-12-26 16:42:23',NULL,'2024-12-26 16:42:23',_binary '\0',1),(36150,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:42:23','2024-12-26 16:42:23',19,0,'',NULL,'2024-12-26 16:42:23',NULL,'2024-12-26 16:42:23',_binary '\0',1),(36151,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:42:23','2024-12-26 16:42:23',65,0,'',NULL,'2024-12-26 16:42:23',NULL,'2024-12-26 16:42:23',_binary '\0',1),(36152,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:42:23','2024-12-26 16:42:23',85,0,'',NULL,'2024-12-26 16:42:23',NULL,'2024-12-26 16:42:23',_binary '\0',1),(36153,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:42:24','2024-12-26 16:42:24',4,0,'',NULL,'2024-12-26 16:42:24',NULL,'2024-12-26 16:42:24',_binary '\0',1),(36154,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:42:35','2024-12-26 16:42:35',26,0,'',NULL,'2024-12-26 16:42:35',NULL,'2024-12-26 16:42:35',_binary '\0',1),(36155,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:42:35','2024-12-26 16:42:35',17,0,'',NULL,'2024-12-26 16:42:35',NULL,'2024-12-26 16:42:35',_binary '\0',122),(36156,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:43:08','2024-12-26 16:43:08',61,0,'',NULL,'2024-12-26 16:43:08',NULL,'2024-12-26 16:43:08',_binary '\0',122),(36157,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:08','2024-12-26 16:43:08',1,0,'',NULL,'2024-12-26 16:43:08',NULL,'2024-12-26 16:43:08',_binary '\0',122),(36158,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:09','2024-12-26 16:43:09',0,0,'',NULL,'2024-12-26 16:43:09',NULL,'2024-12-26 16:43:09',_binary '\0',122),(36159,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:43:09','2024-12-26 16:43:09',27,0,'',NULL,'2024-12-26 16:43:09',NULL,'2024-12-26 16:43:09',_binary '\0',122),(36160,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:43:10','2024-12-26 16:43:10',19,0,'',NULL,'2024-12-26 16:43:10',NULL,'2024-12-26 16:43:10',_binary '\0',122),(36161,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:10','2024-12-26 16:43:10',1,0,'',NULL,'2024-12-26 16:43:10',NULL,'2024-12-26 16:43:10',_binary '\0',122),(36162,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:43:14','2024-12-26 16:43:14',27,0,'',NULL,'2024-12-26 16:43:14',NULL,'2024-12-26 16:43:14',_binary '\0',122),(36163,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:14','2024-12-26 16:43:14',1,0,'',NULL,'2024-12-26 16:43:14',NULL,'2024-12-26 16:43:14',_binary '\0',122),(36164,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:43:17','2024-12-26 16:43:17',44,0,'',NULL,'2024-12-26 16:43:17',NULL,'2024-12-26 16:43:17',_binary '\0',122),(36165,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-by-website','{\"query\":\"{\\\"website\\\":\\\"localhost\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用域名，获得租户信息',1,'2024-12-26 16:43:17','2024-12-26 16:43:17',49,0,'',NULL,'2024-12-26 16:43:17',NULL,'2024-12-26 16:43:17',_binary '\0',122),(36166,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:43:17','2024-12-26 16:43:17',69,0,'',NULL,'2024-12-26 16:43:17',NULL,'2024-12-26 16:43:17',_binary '\0',122),(36167,'',0,2,'yudao-server','POST','/admin-api/system/captcha/get','{\"query\":null,\"body\":\"{\\\"captchaType\\\":\\\"blockPuzzle\\\"}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 验证码','获得验证码',2,'2024-12-26 16:43:17','2024-12-26 16:43:17',69,0,'',NULL,'2024-12-26 16:43:17',NULL,'2024-12-26 16:43:17',_binary '\0',122),(36168,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:43:24','2024-12-26 16:43:24',23,0,'',NULL,'2024-12-26 16:43:24',NULL,'2024-12-26 16:43:24',_binary '\0',122),(36169,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:24','2024-12-26 16:43:24',1,0,'',NULL,'2024-12-26 16:43:24',NULL,'2024-12-26 16:43:24',_binary '\0',122),(36170,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:24','2024-12-26 16:43:24',3,0,'',NULL,'2024-12-26 16:43:24',NULL,'2024-12-26 16:43:24',_binary '\0',122),(36171,'',0,2,'yudao-server','GET','/admin-api/system/tenant/get-id-by-name','{\"query\":\"{\\\"name\\\":\\\"测试租户\\\"}\",\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','管理后台 - 租户','使用租户名，获得租户编号',1,'2024-12-26 16:43:25','2024-12-26 16:43:25',26,0,'',NULL,'2024-12-26 16:43:25',NULL,'2024-12-26 16:43:25',_binary '\0',122),(36172,'',0,2,'yudao-server','POST','/admin-api/system/auth/login','{\"query\":null,\"body\":\"{\\\"tenantName\\\":\\\"测试租户\\\",\\\"username\\\":\\\"admin001\\\",\\\"rememberMe\\\":true}\"}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:43:25','2024-12-26 16:43:25',1,0,'',NULL,'2024-12-26 16:43:25',NULL,'2024-12-26 16:43:25',_binary '\0',122),(36173,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:44:25','2024-12-26 16:44:25',6,0,'',NULL,'2024-12-26 16:44:25',NULL,'2024-12-26 16:44:25',_binary '\0',122),(36174,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:45:24','2024-12-26 16:45:25',6,0,'',NULL,'2024-12-26 16:45:25',NULL,'2024-12-26 16:45:25',_binary '\0',122),(36175,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:46:25','2024-12-26 16:46:25',6,0,'',NULL,'2024-12-26 16:46:25',NULL,'2024-12-26 16:46:25',_binary '\0',122),(36176,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:47:25','2024-12-26 16:47:25',6,0,'',NULL,'2024-12-26 16:47:25',NULL,'2024-12-26 16:47:25',_binary '\0',122),(36177,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:48:25','2024-12-26 16:48:25',6,0,'',NULL,'2024-12-26 16:48:25',NULL,'2024-12-26 16:48:25',_binary '\0',122),(36178,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:49:25','2024-12-26 16:49:25',5,0,'',NULL,'2024-12-26 16:49:25',NULL,'2024-12-26 16:49:25',_binary '\0',122),(36179,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:50:25','2024-12-26 16:50:25',5,0,'',NULL,'2024-12-26 16:50:25',NULL,'2024-12-26 16:50:25',_binary '\0',122),(36180,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:51:25','2024-12-26 16:51:25',7,0,'',NULL,'2024-12-26 16:51:25',NULL,'2024-12-26 16:51:25',_binary '\0',122),(36181,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:52:25','2024-12-26 16:52:25',6,0,'',NULL,'2024-12-26 16:52:25',NULL,'2024-12-26 16:52:25',_binary '\0',122),(36182,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:53:25','2024-12-26 16:53:25',4,0,'',NULL,'2024-12-26 16:53:25',NULL,'2024-12-26 16:53:25',_binary '\0',122),(36183,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:54:25','2024-12-26 16:54:25',5,0,'',NULL,'2024-12-26 16:54:25',NULL,'2024-12-26 16:54:25',_binary '\0',122),(36184,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:55:25','2024-12-26 16:55:25',7,0,'',NULL,'2024-12-26 16:55:25',NULL,'2024-12-26 16:55:25',_binary '\0',122),(36185,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:56:25','2024-12-26 16:56:25',36,0,'',NULL,'2024-12-26 16:56:25',NULL,'2024-12-26 16:56:25',_binary '\0',122),(36186,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:57:25','2024-12-26 16:57:25',6,0,'',NULL,'2024-12-26 16:57:25',NULL,'2024-12-26 16:57:25',_binary '\0',122),(36187,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:58:25','2024-12-26 16:58:25',15,0,'',NULL,'2024-12-26 16:58:25',NULL,'2024-12-26 16:58:25',_binary '\0',122),(36188,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 16:59:25','2024-12-26 16:59:25',14,0,'',NULL,'2024-12-26 16:59:25',NULL,'2024-12-26 16:59:25',_binary '\0',122),(36189,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:00:25','2024-12-26 17:00:25',6,0,'',NULL,'2024-12-26 17:00:25',NULL,'2024-12-26 17:00:25',_binary '\0',122),(36190,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:01:25','2024-12-26 17:01:25',8,0,'',NULL,'2024-12-26 17:01:25',NULL,'2024-12-26 17:01:25',_binary '\0',122),(36191,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:02:25','2024-12-26 17:02:25',12,0,'',NULL,'2024-12-26 17:02:25',NULL,'2024-12-26 17:02:25',_binary '\0',122),(36192,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:03:25','2024-12-26 17:03:25',16,0,'',NULL,'2024-12-26 17:03:25',NULL,'2024-12-26 17:03:25',_binary '\0',122),(36193,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:04:25','2024-12-26 17:04:25',8,0,'',NULL,'2024-12-26 17:04:25',NULL,'2024-12-26 17:04:25',_binary '\0',122),(36194,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:05:25','2024-12-26 17:05:25',5,0,'',NULL,'2024-12-26 17:05:25',NULL,'2024-12-26 17:05:25',_binary '\0',122),(36195,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:06:25','2024-12-26 17:06:25',10,0,'',NULL,'2024-12-26 17:06:25',NULL,'2024-12-26 17:06:25',_binary '\0',122),(36196,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:07:25','2024-12-26 17:07:25',7,0,'',NULL,'2024-12-26 17:07:25',NULL,'2024-12-26 17:07:25',_binary '\0',122),(36197,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:08:25','2024-12-26 17:08:25',10,0,'',NULL,'2024-12-26 17:08:25',NULL,'2024-12-26 17:08:25',_binary '\0',122),(36198,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:09:25','2024-12-26 17:09:25',62,0,'',NULL,'2024-12-26 17:09:25',NULL,'2024-12-26 17:09:25',_binary '\0',122),(36199,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:10:25','2024-12-26 17:10:25',12,0,'',NULL,'2024-12-26 17:10:25',NULL,'2024-12-26 17:10:25',_binary '\0',122),(36200,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:11:24','2024-12-26 17:11:25',21,0,'',NULL,'2024-12-26 17:11:25',NULL,'2024-12-26 17:11:25',_binary '\0',122),(36201,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:12:25','2024-12-26 17:12:25',6,0,'',NULL,'2024-12-26 17:12:25',NULL,'2024-12-26 17:12:25',_binary '\0',122),(36202,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:13:25','2024-12-26 17:13:25',11,0,'',NULL,'2024-12-26 17:13:25',NULL,'2024-12-26 17:13:25',_binary '\0',122),(36203,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:14:25','2024-12-26 17:14:25',9,0,'',NULL,'2024-12-26 17:14:25',NULL,'2024-12-26 17:14:25',_binary '\0',122),(36204,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:15:25','2024-12-26 17:15:25',6,0,'',NULL,'2024-12-26 17:15:25',NULL,'2024-12-26 17:15:25',_binary '\0',122),(36205,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:16:25','2024-12-26 17:16:25',32,0,'',NULL,'2024-12-26 17:16:25',NULL,'2024-12-26 17:16:25',_binary '\0',122),(36206,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:17:25','2024-12-26 17:17:25',11,0,'',NULL,'2024-12-26 17:17:25',NULL,'2024-12-26 17:17:25',_binary '\0',122),(36207,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:18:25','2024-12-26 17:18:25',37,0,'',NULL,'2024-12-26 17:18:25',NULL,'2024-12-26 17:18:25',_binary '\0',122),(36208,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:19:25','2024-12-26 17:19:25',11,0,'',NULL,'2024-12-26 17:19:25',NULL,'2024-12-26 17:19:25',_binary '\0',122),(36209,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:20:25','2024-12-26 17:20:25',20,0,'',NULL,'2024-12-26 17:20:25',NULL,'2024-12-26 17:20:25',_binary '\0',122),(36210,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:21:25','2024-12-26 17:21:25',30,0,'',NULL,'2024-12-26 17:21:25',NULL,'2024-12-26 17:21:25',_binary '\0',122),(36211,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:22:25','2024-12-26 17:22:25',7,0,'',NULL,'2024-12-26 17:22:25',NULL,'2024-12-26 17:22:25',_binary '\0',122),(36212,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:23:25','2024-12-26 17:23:25',16,0,'',NULL,'2024-12-26 17:23:25',NULL,'2024-12-26 17:23:25',_binary '\0',122),(36213,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:24:25','2024-12-26 17:24:25',6,0,'',NULL,'2024-12-26 17:24:25',NULL,'2024-12-26 17:24:25',_binary '\0',122),(36214,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:25:25','2024-12-26 17:25:25',17,0,'',NULL,'2024-12-26 17:25:25',NULL,'2024-12-26 17:25:25',_binary '\0',122),(36215,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:26:25','2024-12-26 17:26:25',17,0,'',NULL,'2024-12-26 17:26:25',NULL,'2024-12-26 17:26:25',_binary '\0',122),(36216,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:27:25','2024-12-26 17:27:25',6,0,'',NULL,'2024-12-26 17:27:25',NULL,'2024-12-26 17:27:25',_binary '\0',122),(36217,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:28:25','2024-12-26 17:28:25',9,0,'',NULL,'2024-12-26 17:28:25',NULL,'2024-12-26 17:28:25',_binary '\0',122),(36218,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:29:25','2024-12-26 17:29:25',7,0,'',NULL,'2024-12-26 17:29:25',NULL,'2024-12-26 17:29:25',_binary '\0',122),(36219,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:30:25','2024-12-26 17:30:25',25,0,'',NULL,'2024-12-26 17:30:25',NULL,'2024-12-26 17:30:25',_binary '\0',122),(36220,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:31:25','2024-12-26 17:31:25',9,0,'',NULL,'2024-12-26 17:31:25',NULL,'2024-12-26 17:31:25',_binary '\0',122),(36221,'',0,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":null,\"body\":null}',NULL,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,NULL,0,'2024-12-26 17:32:25','2024-12-26 17:32:25',27,0,'',NULL,'2024-12-26 17:32:25',NULL,'2024-12-26 17:32:25',_binary '\0',122);
/*!40000 ALTER TABLE `infra_api_access_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_api_error_log`
--

DROP TABLE IF EXISTS `infra_api_error_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_api_error_log` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                       `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链路追踪编号',
                                       `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户编号',
                                       `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型',
                                       `application_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
                                       `request_method` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方法名',
                                       `request_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求地址',
                                       `request_params` varchar(8000) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求参数',
                                       `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
                                       `user_agent` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
                                       `exception_time` datetime NOT NULL COMMENT '异常发生时间',
                                       `exception_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '异常名',
                                       `exception_message` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常导致的消息',
                                       `exception_root_cause_message` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常导致的根消息',
                                       `exception_stack_trace` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常的栈轨迹',
                                       `exception_class_name` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的类全名',
                                       `exception_file_name` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的类文件',
                                       `exception_method_name` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的方法名',
                                       `exception_line_number` int(11) NOT NULL COMMENT '异常发生的方法所在行',
                                       `process_status` tinyint(4) NOT NULL COMMENT '处理状态',
                                       `process_time` datetime DEFAULT NULL COMMENT '处理时间',
                                       `process_user_id` int(11) DEFAULT '0' COMMENT '处理用户编号',
                                       `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21017 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统异常日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_api_error_log`
--

LOCK TABLES `infra_api_error_log` WRITE;
/*!40000 ALTER TABLE `infra_api_error_log` DISABLE KEYS */;
INSERT INTO `infra_api_error_log` VALUES (21016,'',1,2,'yudao-server','GET','/admin-api/system/notify-message/get-unread-count','{\"query\":{},\"body\":\"\"}','0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2024-12-26 15:29:51','org.springframework.web.context.request.async.AsyncRequestNotUsableException','AsyncRequestNotUsableException: ServletOutputStream failed to flush: java.io.IOException: Broken pipe','IOException: Broken pipe','org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to flush: java.io.IOException: Broken pipe\n	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:343)\n	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:415)\n	at java.base/java.io.FilterOutputStream.flush(FilterOutputStream.java:153)\n	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1207)\n	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1063)\n	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:483)\n	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:114)\n	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:297)\n	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:192)\n	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)\n	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)\n	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)\n	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)\n	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\n	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\n	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\n	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\n	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)\n	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)\n	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\n	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\n	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\n	at org.apa','org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse','StandardServletAsyncWebRequest.java','handleIOException',343,0,NULL,0,NULL,'2024-12-26 15:30:09',NULL,'2024-12-26 15:30:09',_binary '\0',1);
/*!40000 ALTER TABLE `infra_api_error_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_codegen_column`
--

DROP TABLE IF EXISTS `infra_codegen_column`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_codegen_column` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `table_id` bigint(20) NOT NULL COMMENT '表编号',
                                        `column_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段名',
                                        `data_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段类型',
                                        `column_comment` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段描述',
                                        `nullable` bit(1) NOT NULL COMMENT '是否允许为空',
                                        `primary_key` bit(1) NOT NULL COMMENT '是否主键',
                                        `ordinal_position` int(11) NOT NULL COMMENT '排序',
                                        `java_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Java 属性类型',
                                        `java_field` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Java 属性名',
                                        `dict_type` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '字典类型',
                                        `example` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据示例',
                                        `create_operation` bit(1) NOT NULL COMMENT '是否为 Create 创建操作的字段',
                                        `update_operation` bit(1) NOT NULL COMMENT '是否为 Update 更新操作的字段',
                                        `list_operation` bit(1) NOT NULL COMMENT '是否为 List 查询操作的字段',
                                        `list_operation_condition` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '=' COMMENT 'List 查询操作的条件类型',
                                        `list_operation_result` bit(1) NOT NULL COMMENT '是否为 List 查询操作的返回字段',
                                        `html_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '显示类型',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2483 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代码生成表字段定义';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_codegen_column`
--

LOCK TABLES `infra_codegen_column` WRITE;
/*!40000 ALTER TABLE `infra_codegen_column` DISABLE KEYS */;
/*!40000 ALTER TABLE `infra_codegen_column` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_codegen_table`
--

DROP TABLE IF EXISTS `infra_codegen_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_codegen_table` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                       `data_source_config_id` bigint(20) NOT NULL COMMENT '数据源配置的编号',
                                       `scene` tinyint(4) NOT NULL DEFAULT '1' COMMENT '生成场景',
                                       `table_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '表名称',
                                       `table_comment` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '表描述',
                                       `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                       `module_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块名',
                                       `business_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务名',
                                       `class_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类名称',
                                       `class_comment` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类描述',
                                       `author` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '作者',
                                       `template_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '模板类型',
                                       `front_type` tinyint(4) NOT NULL COMMENT '前端类型',
                                       `parent_menu_id` bigint(20) DEFAULT NULL COMMENT '父菜单编号',
                                       `master_table_id` bigint(20) DEFAULT NULL COMMENT '主表的编号',
                                       `sub_join_column_id` bigint(20) DEFAULT NULL COMMENT '子表关联主表的字段编号',
                                       `sub_join_many` bit(1) DEFAULT NULL COMMENT '主表与子表是否一对多',
                                       `tree_parent_column_id` bigint(20) DEFAULT NULL COMMENT '树表的父字段编号',
                                       `tree_name_column_id` bigint(20) DEFAULT NULL COMMENT '树表的名字字段编号',
                                       `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代码生成表定义';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_codegen_table`
--

LOCK TABLES `infra_codegen_table` WRITE;
/*!40000 ALTER TABLE `infra_codegen_table` DISABLE KEYS */;
/*!40000 ALTER TABLE `infra_codegen_table` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_config`
--

DROP TABLE IF EXISTS `infra_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_config` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
                                `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数分组',
                                `type` tinyint(4) NOT NULL COMMENT '参数类型',
                                `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数名称',
                                `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数键名',
                                `value` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数键值',
                                `visible` bit(1) NOT NULL COMMENT '是否可见',
                                `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_config`
--

LOCK TABLES `infra_config` WRITE;
/*!40000 ALTER TABLE `infra_config` DISABLE KEYS */;
INSERT INTO `infra_config` VALUES (2,'biz',1,'用户管理-账号初始密码','system.user.init-password','123456',_binary '\0','初始化密码 123456','admin','2021-01-05 17:03:48','1','2024-07-20 17:22:47',_binary '\0'),(7,'url',2,'MySQL 监控的地址','url.druid','',_binary '','','1','2023-04-07 13:41:16','1','2023-04-07 14:33:38',_binary '\0'),(8,'url',2,'SkyWalking 监控的地址','url.skywalking','',_binary '','','1','2023-04-07 13:41:16','1','2023-04-07 14:57:03',_binary '\0'),(9,'url',2,'Spring Boot Admin 监控的地址','url.spring-boot-admin','',_binary '','','1','2023-04-07 13:41:16','1','2023-04-07 14:52:07',_binary '\0'),(10,'url',2,'Swagger 接口文档的地址','url.swagger','',_binary '','','1','2023-04-07 13:41:16','1','2023-04-07 14:59:00',_binary '\0'),(11,'ui',2,'腾讯地图 key','tencent.lbs.key','TVDBZ-TDILD-4ON4B-PFDZA-RNLKH-VVF6E',_binary '','腾讯地图 key','1','2023-06-03 19:16:27','1','2023-06-03 19:16:27',_binary '\0'),(12,'test2',2,'test3','test4','test5',_binary '','test6','1','2023-12-03 09:55:16','1','2023-12-03 09:55:27',_binary '\0');
/*!40000 ALTER TABLE `infra_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_data_source_config`
--

DROP TABLE IF EXISTS `infra_data_source_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_data_source_config` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
                                            `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数名称',
                                            `url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据源连接',
                                            `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
                                            `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
                                            `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据源配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_data_source_config`
--

LOCK TABLES `infra_data_source_config` WRITE;
/*!40000 ALTER TABLE `infra_data_source_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `infra_data_source_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_file`
--

DROP TABLE IF EXISTS `infra_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_file` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件编号',
                              `config_id` bigint(20) DEFAULT NULL COMMENT '配置编号',
                              `name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名',
                              `path` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
                              `url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件 URL',
                              `type` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件类型',
                              `size` int(11) NOT NULL COMMENT '文件大小',
                              `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1509 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_file`
--

LOCK TABLES `infra_file` WRITE;
/*!40000 ALTER TABLE `infra_file` DISABLE KEYS */;
/*!40000 ALTER TABLE `infra_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_file_config`
--

DROP TABLE IF EXISTS `infra_file_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_file_config` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                     `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名',
                                     `storage` tinyint(4) NOT NULL COMMENT '存储器',
                                     `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                     `master` bit(1) NOT NULL COMMENT '是否为主配置',
                                     `config` varchar(4096) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储配置',
                                     `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_file_config`
--

LOCK TABLES `infra_file_config` WRITE;
/*!40000 ALTER TABLE `infra_file_config` DISABLE KEYS */;
INSERT INTO `infra_file_config` VALUES (4,'数据库',1,'我是数据库',_binary '\0','{\"@class\":\"cn.iocoder.yudao.module.infra.framework.file.core.client.db.DBFileClientConfig\",\"domain\":\"http://127.0.0.1:48080\"}','1','2022-03-15 23:56:24','1','2024-02-28 22:54:07',_binary '\0'),(22,'七牛存储器',20,'',_binary '','{\"@class\":\"cn.iocoder.yudao.module.infra.framework.file.core.client.s3.S3FileClientConfig\",\"endpoint\":\"s3.cn-south-1.qiniucs.com\",\"domain\":\"http://test.yudao.iocoder.cn\",\"bucket\":\"ruoyi-vue-pro\",\"accessKey\":\"3TvrJ70gl2Gt6IBe7_IZT1F6i_k0iMuRtyEv4EyS\",\"accessSecret\":\"wd0tbVBYlp0S-ihA8Qg2hPLncoP83wyrIq24OZuY\"}','1','2024-01-13 22:11:12','1','2024-04-03 19:38:34',_binary '\0');
/*!40000 ALTER TABLE `infra_file_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_file_content`
--

DROP TABLE IF EXISTS `infra_file_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_file_content` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                      `config_id` bigint(20) NOT NULL COMMENT '配置编号',
                                      `path` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
                                      `content` mediumblob NOT NULL COMMENT '文件内容',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=283 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_file_content`
--

LOCK TABLES `infra_file_content` WRITE;
/*!40000 ALTER TABLE `infra_file_content` DISABLE KEYS */;
/*!40000 ALTER TABLE `infra_file_content` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_job`
--

DROP TABLE IF EXISTS `infra_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_job` (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务编号',
                             `name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
                             `status` tinyint(4) NOT NULL COMMENT '任务状态',
                             `handler_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理器的名字',
                             `handler_param` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理器的参数',
                             `cron_expression` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'CRON 表达式',
                             `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
                             `retry_interval` int(11) NOT NULL DEFAULT '0' COMMENT '重试间隔',
                             `monitor_timeout` int(11) NOT NULL DEFAULT '0' COMMENT '监控超时时间',
                             `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_job`
--

LOCK TABLES `infra_job` WRITE;
/*!40000 ALTER TABLE `infra_job` DISABLE KEYS */;
INSERT INTO `infra_job` VALUES (5,'支付通知 Job',2,'payNotifyJob',NULL,'* * * * * ?',0,0,0,'1','2021-10-27 08:34:42','1','2024-09-12 13:32:48',_binary '\0'),(17,'支付订单同步 Job',2,'payOrderSyncJob',NULL,'0 0/1 * * * ?',0,0,0,'1','2023-07-22 14:36:26','1','2023-07-22 15:39:08',_binary '\0'),(18,'支付订单过期 Job',2,'payOrderExpireJob',NULL,'0 0/1 * * * ?',0,0,0,'1','2023-07-22 15:36:23','1','2023-07-22 15:39:54',_binary '\0'),(19,'退款订单的同步 Job',2,'payRefundSyncJob',NULL,'0 0/1 * * * ?',0,0,0,'1','2023-07-23 21:03:44','1','2023-07-23 21:09:00',_binary '\0'),(21,'交易订单的自动过期 Job',2,'tradeOrderAutoCancelJob','','0 * * * * ?',3,0,0,'1','2023-09-25 23:43:26','1','2023-09-26 19:23:30',_binary '\0'),(22,'交易订单的自动收货 Job',2,'tradeOrderAutoReceiveJob','','0 * * * * ?',3,0,0,'1','2023-09-26 19:23:53','1','2023-09-26 23:38:08',_binary '\0'),(23,'交易订单的自动评论 Job',2,'tradeOrderAutoCommentJob','','0 * * * * ?',3,0,0,'1','2023-09-26 23:38:29','1','2023-09-27 11:03:10',_binary '\0'),(24,'佣金解冻 Job',2,'brokerageRecordUnfreezeJob','','0 * * * * ?',3,0,0,'1','2023-09-28 22:01:46','1','2023-09-28 22:01:56',_binary '\0'),(25,'访问日志清理 Job',2,'accessLogCleanJob','','0 0 0 * * ?',3,0,0,'1','2023-10-03 10:59:41','1','2023-10-03 11:01:10',_binary '\0'),(26,'错误日志清理 Job',2,'errorLogCleanJob','','0 0 0 * * ?',3,0,0,'1','2023-10-03 11:00:43','1','2023-10-03 11:01:12',_binary '\0'),(27,'任务日志清理 Job',2,'jobLogCleanJob','','0 0 0 * * ?',3,0,0,'1','2023-10-03 11:01:33','1','2024-09-12 13:40:34',_binary '\0');
/*!40000 ALTER TABLE `infra_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `infra_job_log`
--

DROP TABLE IF EXISTS `infra_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `infra_job_log` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志编号',
                                 `job_id` bigint(20) NOT NULL COMMENT '任务编号',
                                 `handler_name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理器的名字',
                                 `handler_param` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理器的参数',
                                 `execute_index` tinyint(4) NOT NULL DEFAULT '1' COMMENT '第几次执行',
                                 `begin_time` datetime NOT NULL COMMENT '开始执行时间',
                                 `end_time` datetime DEFAULT NULL COMMENT '结束执行时间',
                                 `duration` int(11) DEFAULT NULL COMMENT '执行时长',
                                 `status` tinyint(4) NOT NULL COMMENT '任务状态',
                                 `result` varchar(4000) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '结果数据',
                                 `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=634 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='定时任务日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `infra_job_log`
--

LOCK TABLES `infra_job_log` WRITE;
/*!40000 ALTER TABLE `infra_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `infra_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_dept`
--

DROP TABLE IF EXISTS `system_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_dept` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
                               `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '部门名称',
                               `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父部门id',
                               `sort` int(11) NOT NULL DEFAULT '0' COMMENT '显示顺序',
                               `leader_user_id` bigint(20) DEFAULT NULL COMMENT '负责人',
                               `phone` varchar(11) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
                               `email` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
                               `status` tinyint(4) NOT NULL COMMENT '部门状态（0正常 1停用）',
                               `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                               `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_dept`
--

LOCK TABLES `system_dept` WRITE;
/*!40000 ALTER TABLE `system_dept` DISABLE KEYS */;
INSERT INTO `system_dept` VALUES (100,'三胖科技',0,0,1,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','1','2024-12-26 16:35:29',_binary '\0',1),(101,'深圳总公司',100,1,104,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','1','2023-12-02 09:53:35',_binary '\0',1),(102,'长沙分公司',100,2,NULL,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','','2021-12-15 05:01:40',_binary '\0',1),(103,'研发部门',101,1,1,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','1','2024-10-02 10:22:03',_binary '\0',1),(104,'市场部门',101,2,NULL,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','','2021-12-15 05:01:38',_binary '\0',1),(105,'测试部门',101,3,NULL,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','1','2022-05-16 20:25:15',_binary '\0',1),(106,'财务部门',101,4,103,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','103','2022-01-15 21:32:22',_binary '\0',1),(107,'运维部门',101,5,1,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','1','2023-12-02 09:28:22',_binary '\0',1),(108,'市场部门',102,1,NULL,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','1','2022-02-16 08:35:45',_binary '\0',1),(109,'财务部门',102,2,NULL,'15888888888','<EMAIL>',0,'admin','2021-01-05 17:03:47','','2021-12-15 05:01:29',_binary '\0',1),(110,'新部门',0,1,NULL,NULL,NULL,0,'110','2022-02-23 20:46:30','110','2022-02-23 20:46:30',_binary '\0',121),(111,'顶级部门',0,1,NULL,NULL,NULL,0,'113','2022-03-07 21:44:50','113','2022-03-07 21:44:50',_binary '\0',122),(112,'产品部门',101,100,1,NULL,NULL,1,'1','2023-12-02 09:45:13','1','2023-12-02 09:45:31',_binary '\0',1),(113,'支持部门',102,3,104,NULL,NULL,1,'1','2023-12-02 09:47:38','1','2023-12-02 09:47:38',_binary '\0',1),(115,'测试租户',0,0,NULL,NULL,NULL,0,'1','2024-12-26 16:37:28','1','2024-12-26 16:37:36',_binary '\0',1),(116,'西安分公司',115,1,NULL,NULL,NULL,0,'1','2024-12-26 16:38:17','1','2024-12-26 16:38:17',_binary '\0',1);
/*!40000 ALTER TABLE `system_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_dict_data`
--

DROP TABLE IF EXISTS `system_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_dict_data` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
                                    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '字典排序',
                                    `label` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典标签',
                                    `value` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典键值',
                                    `dict_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典类型',
                                    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                    `color_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '颜色类型',
                                    `css_class` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'css 样式',
                                    `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1593 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_dict_data`
--

LOCK TABLES `system_dict_data` WRITE;
/*!40000 ALTER TABLE `system_dict_data` DISABLE KEYS */;
INSERT INTO `system_dict_data` VALUES (1,1,'男','1','system_user_sex',0,'default','A','性别男','admin','2021-01-05 17:03:48','1','2022-03-29 00:14:39',_binary '\0'),(2,2,'女','2','system_user_sex',0,'success','','性别女','admin','2021-01-05 17:03:48','1','2023-11-15 23:30:37',_binary '\0'),(8,1,'正常','1','infra_job_status',0,'success','','正常状态','admin','2021-01-05 17:03:48','1','2022-02-16 19:33:38',_binary '\0'),(9,2,'暂停','2','infra_job_status',0,'danger','','停用状态','admin','2021-01-05 17:03:48','1','2022-02-16 19:33:45',_binary '\0'),(12,1,'系统内置','1','infra_config_type',0,'danger','','参数类型 - 系统内置','admin','2021-01-05 17:03:48','1','2022-02-16 19:06:02',_binary '\0'),(13,2,'自定义','2','infra_config_type',0,'primary','','参数类型 - 自定义','admin','2021-01-05 17:03:48','1','2022-02-16 19:06:07',_binary '\0'),(14,1,'通知','1','system_notice_type',0,'success','','通知','admin','2021-01-05 17:03:48','1','2022-02-16 13:05:57',_binary '\0'),(15,2,'公告','2','system_notice_type',0,'info','','公告','admin','2021-01-05 17:03:48','1','2022-02-16 13:06:01',_binary '\0'),(16,0,'其它','0','infra_operate_type',0,'default','','其它操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:19',_binary '\0'),(17,1,'查询','1','infra_operate_type',0,'info','','查询操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:20',_binary '\0'),(18,2,'新增','2','infra_operate_type',0,'primary','','新增操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:21',_binary '\0'),(19,3,'修改','3','infra_operate_type',0,'warning','','修改操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:22',_binary '\0'),(20,4,'删除','4','infra_operate_type',0,'danger','','删除操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:23',_binary '\0'),(22,5,'导出','5','infra_operate_type',0,'default','','导出操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:24',_binary '\0'),(23,6,'导入','6','infra_operate_type',0,'default','','导入操作','admin','2021-01-05 17:03:48','1','2024-03-14 12:44:25',_binary '\0'),(27,1,'开启','0','common_status',0,'primary','','开启状态','admin','2021-01-05 17:03:48','1','2022-02-16 08:00:39',_binary '\0'),(28,2,'关闭','1','common_status',0,'info','','关闭状态','admin','2021-01-05 17:03:48','1','2022-02-16 08:00:44',_binary '\0'),(29,1,'目录','1','system_menu_type',0,'','','目录','admin','2021-01-05 17:03:48','','2022-02-01 16:43:45',_binary '\0'),(30,2,'菜单','2','system_menu_type',0,'','','菜单','admin','2021-01-05 17:03:48','','2022-02-01 16:43:41',_binary '\0'),(31,3,'按钮','3','system_menu_type',0,'','','按钮','admin','2021-01-05 17:03:48','','2022-02-01 16:43:39',_binary '\0'),(32,1,'内置','1','system_role_type',0,'danger','','内置角色','admin','2021-01-05 17:03:48','1','2022-02-16 13:02:08',_binary '\0'),(33,2,'自定义','2','system_role_type',0,'primary','','自定义角色','admin','2021-01-05 17:03:48','1','2022-02-16 13:02:12',_binary '\0'),(34,1,'全部数据权限','1','system_data_scope',0,'','','全部数据权限','admin','2021-01-05 17:03:48','','2022-02-01 16:47:17',_binary '\0'),(35,2,'指定部门数据权限','2','system_data_scope',0,'','','指定部门数据权限','admin','2021-01-05 17:03:48','','2022-02-01 16:47:18',_binary '\0'),(36,3,'本部门数据权限','3','system_data_scope',0,'','','本部门数据权限','admin','2021-01-05 17:03:48','','2022-02-01 16:47:16',_binary '\0'),(37,4,'本部门及以下数据权限','4','system_data_scope',0,'','','本部门及以下数据权限','admin','2021-01-05 17:03:48','','2022-02-01 16:47:21',_binary '\0'),(38,5,'仅本人数据权限','5','system_data_scope',0,'','','仅本人数据权限','admin','2021-01-05 17:03:48','','2022-02-01 16:47:23',_binary '\0'),(39,0,'成功','0','system_login_result',0,'success','','登陆结果 - 成功','','2021-01-18 06:17:36','1','2022-02-16 13:23:49',_binary '\0'),(40,10,'账号或密码不正确','10','system_login_result',0,'primary','','登陆结果 - 账号或密码不正确','','2021-01-18 06:17:54','1','2022-02-16 13:24:27',_binary '\0'),(41,20,'用户被禁用','20','system_login_result',0,'warning','','登陆结果 - 用户被禁用','','2021-01-18 06:17:54','1','2022-02-16 13:23:57',_binary '\0'),(42,30,'验证码不存在','30','system_login_result',0,'info','','登陆结果 - 验证码不存在','','2021-01-18 06:17:54','1','2022-02-16 13:24:07',_binary '\0'),(43,31,'验证码不正确','31','system_login_result',0,'info','','登陆结果 - 验证码不正确','','2021-01-18 06:17:54','1','2022-02-16 13:24:11',_binary '\0'),(44,100,'未知异常','100','system_login_result',0,'danger','','登陆结果 - 未知异常','','2021-01-18 06:17:54','1','2022-02-16 13:24:23',_binary '\0'),(45,1,'是','true','infra_boolean_string',0,'danger','','Boolean 是否类型 - 是','','2021-01-19 03:20:55','1','2022-03-15 23:01:45',_binary '\0'),(46,1,'否','false','infra_boolean_string',0,'info','','Boolean 是否类型 - 否','','2021-01-19 03:20:55','1','2022-03-15 23:09:45',_binary '\0'),(50,1,'单表（增删改查）','1','infra_codegen_template_type',0,'','',NULL,'','2021-02-05 07:09:06','','2022-03-10 16:33:15',_binary '\0'),(51,2,'树表（增删改查）','2','infra_codegen_template_type',0,'','',NULL,'','2021-02-05 07:14:46','','2022-03-10 16:33:19',_binary '\0'),(53,0,'初始化中','0','infra_job_status',0,'primary','',NULL,'','2021-02-07 07:46:49','1','2022-02-16 19:33:29',_binary '\0'),(57,0,'运行中','0','infra_job_log_status',0,'primary','','RUNNING','','2021-02-08 10:04:24','1','2022-02-16 19:07:48',_binary '\0'),(58,1,'成功','1','infra_job_log_status',0,'success','',NULL,'','2021-02-08 10:06:57','1','2022-02-16 19:07:52',_binary '\0'),(59,2,'失败','2','infra_job_log_status',0,'warning','','失败','','2021-02-08 10:07:38','1','2022-02-16 19:07:56',_binary '\0'),(60,1,'会员','1','user_type',0,'primary','',NULL,'','2021-02-26 00:16:27','1','2022-02-16 10:22:19',_binary '\0'),(61,2,'管理员','2','user_type',0,'success','',NULL,'','2021-02-26 00:16:34','1','2022-02-16 10:22:22',_binary '\0'),(62,0,'未处理','0','infra_api_error_log_process_status',0,'primary','',NULL,'','2021-02-26 07:07:19','1','2022-02-16 20:14:17',_binary '\0'),(63,1,'已处理','1','infra_api_error_log_process_status',0,'success','',NULL,'','2021-02-26 07:07:26','1','2022-02-16 20:14:08',_binary '\0'),(64,2,'已忽略','2','infra_api_error_log_process_status',0,'danger','',NULL,'','2021-02-26 07:07:34','1','2022-02-16 20:14:14',_binary '\0'),(66,1,'阿里云','ALIYUN','system_sms_channel_code',0,'primary','',NULL,'1','2021-04-05 01:05:26','1','2024-07-22 22:23:25',_binary '\0'),(67,1,'验证码','1','system_sms_template_type',0,'warning','',NULL,'1','2021-04-05 21:50:57','1','2022-02-16 12:48:30',_binary '\0'),(68,2,'通知','2','system_sms_template_type',0,'primary','',NULL,'1','2021-04-05 21:51:08','1','2022-02-16 12:48:27',_binary '\0'),(69,0,'营销','3','system_sms_template_type',0,'danger','',NULL,'1','2021-04-05 21:51:15','1','2022-02-16 12:48:22',_binary '\0'),(70,0,'初始化','0','system_sms_send_status',0,'primary','',NULL,'1','2021-04-11 20:18:33','1','2022-02-16 10:26:07',_binary '\0'),(71,1,'发送成功','10','system_sms_send_status',0,'success','',NULL,'1','2021-04-11 20:18:43','1','2022-02-16 10:25:56',_binary '\0'),(72,2,'发送失败','20','system_sms_send_status',0,'danger','',NULL,'1','2021-04-11 20:18:49','1','2022-02-16 10:26:03',_binary '\0'),(73,3,'不发送','30','system_sms_send_status',0,'info','',NULL,'1','2021-04-11 20:19:44','1','2022-02-16 10:26:10',_binary '\0'),(74,0,'等待结果','0','system_sms_receive_status',0,'primary','',NULL,'1','2021-04-11 20:27:43','1','2022-02-16 10:28:24',_binary '\0'),(75,1,'接收成功','10','system_sms_receive_status',0,'success','',NULL,'1','2021-04-11 20:29:25','1','2022-02-16 10:28:28',_binary '\0'),(76,2,'接收失败','20','system_sms_receive_status',0,'danger','',NULL,'1','2021-04-11 20:29:31','1','2022-02-16 10:28:32',_binary '\0'),(77,0,'调试(钉钉)','DEBUG_DING_TALK','system_sms_channel_code',0,'info','',NULL,'1','2021-04-13 00:20:37','1','2022-02-16 10:10:00',_binary '\0'),(80,100,'账号登录','100','system_login_type',0,'primary','','账号登录','1','2021-10-06 00:52:02','1','2022-02-16 13:11:34',_binary '\0'),(81,101,'社交登录','101','system_login_type',0,'info','','社交登录','1','2021-10-06 00:52:17','1','2022-02-16 13:11:40',_binary '\0'),(83,200,'主动登出','200','system_login_type',0,'primary','','主动登出','1','2021-10-06 00:52:58','1','2022-02-16 13:11:49',_binary '\0'),(85,202,'强制登出','202','system_login_type',0,'danger','','强制退出','1','2021-10-06 00:53:41','1','2022-02-16 13:11:57',_binary '\0'),(86,0,'病假','1','bpm_oa_leave_type',0,'primary','',NULL,'1','2021-09-21 22:35:28','1','2022-02-16 10:00:41',_binary '\0'),(87,1,'事假','2','bpm_oa_leave_type',0,'info','',NULL,'1','2021-09-21 22:36:11','1','2022-02-16 10:00:49',_binary '\0'),(88,2,'婚假','3','bpm_oa_leave_type',0,'warning','',NULL,'1','2021-09-21 22:36:38','1','2022-02-16 10:00:53',_binary '\0'),(1127,1,'审批中','1','bpm_process_instance_status',0,'default','','流程实例的状态 - 进行中','1','2022-01-07 23:47:22','1','2024-03-16 16:11:45',_binary '\0'),(1128,2,'审批通过','2','bpm_process_instance_status',0,'success','','流程实例的状态 - 已完成','1','2022-01-07 23:47:49','1','2024-03-16 16:11:54',_binary '\0'),(1129,1,'审批中','1','bpm_task_status',0,'primary','','流程实例的结果 - 处理中','1','2022-01-07 23:48:32','1','2024-03-08 22:41:37',_binary '\0'),(1130,2,'审批通过','2','bpm_task_status',0,'success','','流程实例的结果 - 通过','1','2022-01-07 23:48:45','1','2024-03-08 22:41:38',_binary '\0'),(1131,3,'审批不通过','3','bpm_task_status',0,'danger','','流程实例的结果 - 不通过','1','2022-01-07 23:48:55','1','2024-03-08 22:41:38',_binary '\0'),(1132,4,'已取消','4','bpm_task_status',0,'info','','流程实例的结果 - 撤销','1','2022-01-07 23:49:06','1','2024-03-08 22:41:39',_binary '\0'),(1133,10,'流程表单','10','bpm_model_form_type',0,'','','流程的表单类型 - 流程表单','103','2022-01-11 23:51:30','103','2022-01-11 23:51:30',_binary '\0'),(1134,20,'业务表单','20','bpm_model_form_type',0,'','','流程的表单类型 - 业务表单','103','2022-01-11 23:51:47','103','2022-01-11 23:51:47',_binary '\0'),(1135,10,'角色','10','bpm_task_candidate_strategy',0,'info','','任务分配规则的类型 - 角色','103','2022-01-12 23:21:22','1','2024-03-06 02:53:16',_binary '\0'),(1136,20,'部门的成员','20','bpm_task_candidate_strategy',0,'primary','','任务分配规则的类型 - 部门的成员','103','2022-01-12 23:21:47','1','2024-03-06 02:53:17',_binary '\0'),(1137,21,'部门的负责人','21','bpm_task_candidate_strategy',0,'primary','','任务分配规则的类型 - 部门的负责人','103','2022-01-12 23:33:36','1','2024-03-06 02:53:18',_binary '\0'),(1138,30,'用户','30','bpm_task_candidate_strategy',0,'info','','任务分配规则的类型 - 用户','103','2022-01-12 23:34:02','1','2024-03-06 02:53:19',_binary '\0'),(1139,40,'用户组','40','bpm_task_candidate_strategy',0,'warning','','任务分配规则的类型 - 用户组','103','2022-01-12 23:34:21','1','2024-03-06 02:53:20',_binary '\0'),(1140,60,'流程表达式','60','bpm_task_candidate_strategy',0,'danger','','任务分配规则的类型 - 流程表达式','103','2022-01-12 23:34:43','1','2024-03-06 02:53:20',_binary '\0'),(1141,22,'岗位','22','bpm_task_candidate_strategy',0,'success','','任务分配规则的类型 - 岗位','103','2022-01-14 18:41:55','1','2024-03-06 02:53:21',_binary '\0'),(1145,1,'管理后台','1','infra_codegen_scene',0,'','','代码生成的场景枚举 - 管理后台','1','2022-02-02 13:15:06','1','2022-03-10 16:32:59',_binary '\0'),(1146,2,'用户 APP','2','infra_codegen_scene',0,'','','代码生成的场景枚举 - 用户 APP','1','2022-02-02 13:15:19','1','2022-03-10 16:33:03',_binary '\0'),(1150,1,'数据库','1','infra_file_storage',0,'default','',NULL,'1','2022-03-15 00:25:28','1','2022-03-15 00:25:28',_binary '\0'),(1151,10,'本地磁盘','10','infra_file_storage',0,'default','',NULL,'1','2022-03-15 00:25:41','1','2022-03-15 00:25:56',_binary '\0'),(1152,11,'FTP 服务器','11','infra_file_storage',0,'default','',NULL,'1','2022-03-15 00:26:06','1','2022-03-15 00:26:10',_binary '\0'),(1153,12,'SFTP 服务器','12','infra_file_storage',0,'default','',NULL,'1','2022-03-15 00:26:22','1','2022-03-15 00:26:22',_binary '\0'),(1154,20,'S3 对象存储','20','infra_file_storage',0,'default','',NULL,'1','2022-03-15 00:26:31','1','2022-03-15 00:26:45',_binary '\0'),(1155,103,'短信登录','103','system_login_type',0,'default','',NULL,'1','2022-05-09 23:57:58','1','2022-05-09 23:58:09',_binary '\0'),(1156,1,'password','password','system_oauth2_grant_type',0,'default','','密码模式','1','2022-05-12 00:22:05','1','2022-05-11 16:26:01',_binary '\0'),(1157,2,'authorization_code','authorization_code','system_oauth2_grant_type',0,'primary','','授权码模式','1','2022-05-12 00:22:59','1','2022-05-11 16:26:02',_binary '\0'),(1158,3,'implicit','implicit','system_oauth2_grant_type',0,'success','','简化模式','1','2022-05-12 00:23:40','1','2022-05-11 16:26:05',_binary '\0'),(1159,4,'client_credentials','client_credentials','system_oauth2_grant_type',0,'default','','客户端模式','1','2022-05-12 00:23:51','1','2022-05-11 16:26:08',_binary '\0'),(1160,5,'refresh_token','refresh_token','system_oauth2_grant_type',0,'info','','刷新模式','1','2022-05-12 00:24:02','1','2022-05-11 16:26:11',_binary '\0'),(1194,10,'微信小程序','10','terminal',0,'default','','终端 - 微信小程序','1','2022-12-10 10:51:11','1','2022-12-10 10:51:57',_binary '\0'),(1195,20,'H5 网页','20','terminal',0,'default','','终端 - H5 网页','1','2022-12-10 10:51:30','1','2022-12-10 10:51:59',_binary '\0'),(1196,11,'微信公众号','11','terminal',0,'default','','终端 - 微信公众号','1','2022-12-10 10:54:16','1','2022-12-10 10:52:01',_binary '\0'),(1197,31,'苹果 App','31','terminal',0,'default','','终端 - 苹果 App','1','2022-12-10 10:54:42','1','2022-12-10 10:52:18',_binary '\0'),(1198,32,'安卓 App','32','terminal',0,'default','','终端 - 安卓 App','1','2022-12-10 10:55:02','1','2022-12-10 10:59:17',_binary '\0'),(1223,0,'初始化','0','system_mail_send_status',0,'primary','','邮件发送状态 - 初始化\n','1','2023-01-26 09:53:49','1','2023-01-26 16:36:14',_binary '\0'),(1224,10,'发送成功','10','system_mail_send_status',0,'success','','邮件发送状态 - 发送成功','1','2023-01-26 09:54:28','1','2023-01-26 16:36:22',_binary '\0'),(1225,20,'发送失败','20','system_mail_send_status',0,'danger','','邮件发送状态 - 发送失败','1','2023-01-26 09:54:50','1','2023-01-26 16:36:26',_binary '\0'),(1226,30,'不发送','30','system_mail_send_status',0,'info','','邮件发送状态 -  不发送','1','2023-01-26 09:55:06','1','2023-01-26 16:36:36',_binary '\0'),(1227,1,'通知公告','1','system_notify_template_type',0,'primary','','站内信模版的类型 - 通知公告','1','2023-01-28 10:35:59','1','2023-01-28 10:35:59',_binary '\0'),(1228,2,'系统消息','2','system_notify_template_type',0,'success','','站内信模版的类型 - 系统消息','1','2023-01-28 10:36:20','1','2023-01-28 10:36:25',_binary '\0'),(1231,10,'Vue2 Element UI 标准模版','10','infra_codegen_front_type',0,'','','','1','2023-04-13 00:03:55','1','2023-04-13 00:03:55',_binary '\0'),(1232,20,'Vue3 Element Plus 标准模版','20','infra_codegen_front_type',0,'','','','1','2023-04-13 00:04:08','1','2023-04-13 00:04:08',_binary '\0'),(1234,30,'Vue3 vben 模版','30','infra_codegen_front_type',0,'','','','1','2023-04-13 00:04:26','1','2023-04-13 00:04:26',_binary '\0'),(1359,1,'人人分销','1','brokerage_enabled_condition',0,'','','所有用户都可以分销','','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1360,2,'指定分销','2','brokerage_enabled_condition',0,'','','仅可后台手动设置推广员','','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1361,1,'首次绑定','1','brokerage_bind_mode',0,'','','只要用户没有推广人，随时都可以绑定推广关系','','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1362,2,'注册绑定','2','brokerage_bind_mode',0,'','','仅新用户注册时才能绑定推广关系','','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1363,3,'覆盖绑定','3','brokerage_bind_mode',0,'','','如果用户已经有推广人，推广人会被变更','','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1364,1,'钱包','1','brokerage_withdraw_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1365,2,'银行卡','2','brokerage_withdraw_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1366,3,'微信','3','brokerage_withdraw_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1367,4,'支付宝','4','brokerage_withdraw_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1368,1,'订单返佣','1','brokerage_record_biz_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1369,2,'申请提现','2','brokerage_record_biz_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1370,3,'申请提现驳回','3','brokerage_record_biz_type',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1371,0,'待结算','0','brokerage_record_status',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1372,1,'已结算','1','brokerage_record_status',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1373,2,'已取消','2','brokerage_record_status',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1374,0,'审核中','0','brokerage_withdraw_status',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1375,10,'审核通过','10','brokerage_withdraw_status',0,'success','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1376,11,'提现成功','11','brokerage_withdraw_status',0,'success','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1377,20,'审核不通过','20','brokerage_withdraw_status',0,'danger','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1378,21,'提现失败','21','brokerage_withdraw_status',0,'danger','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1379,0,'工商银行','0','brokerage_bank_name',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1380,1,'建设银行','1','brokerage_bank_name',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1381,2,'农业银行','2','brokerage_bank_name',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1382,3,'中国银行','3','brokerage_bank_name',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1383,4,'交通银行','4','brokerage_bank_name',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1384,5,'招商银行','5','brokerage_bank_name',0,'','',NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0'),(1435,10,'Gitee','10','system_social_type',0,'','','','1','2023-11-04 13:04:42','1','2023-11-04 13:04:42',_binary '\0'),(1436,20,'钉钉','20','system_social_type',0,'','','','1','2023-11-04 13:04:54','1','2023-11-04 13:04:54',_binary '\0'),(1437,30,'企业微信','30','system_social_type',0,'','','','1','2023-11-04 13:05:09','1','2023-11-04 13:05:09',_binary '\0'),(1438,31,'微信公众平台','31','system_social_type',0,'','','','1','2023-11-04 13:05:18','1','2023-11-04 13:05:18',_binary '\0'),(1439,32,'微信开放平台','32','system_social_type',0,'','','','1','2023-11-04 13:05:30','1','2023-11-04 13:05:30',_binary '\0'),(1440,34,'微信小程序','34','system_social_type',0,'','','','1','2023-11-04 13:05:38','1','2023-11-04 13:07:16',_binary '\0'),(1443,15,'子表','15','infra_codegen_template_type',0,'default','','','1','2023-11-13 23:06:16','1','2023-11-13 23:06:16',_binary '\0'),(1444,10,'主表（标准模式）','10','infra_codegen_template_type',0,'default','','','1','2023-11-14 12:32:49','1','2023-11-14 12:32:49',_binary '\0'),(1445,11,'主表（ERP 模式）','11','infra_codegen_template_type',0,'default','','','1','2023-11-14 12:33:05','1','2023-11-14 12:33:05',_binary '\0'),(1446,12,'主表（内嵌模式）','12','infra_codegen_template_type',0,'','','','1','2023-11-14 12:33:31','1','2023-11-14 12:33:31',_binary '\0'),(1509,3,'审批不通过','3','bpm_process_instance_status',0,'danger','','','1','2024-03-16 16:12:06','1','2024-03-16 16:12:06',_binary '\0'),(1510,4,'已取消','4','bpm_process_instance_status',0,'warning','','','1','2024-03-16 16:12:22','1','2024-03-16 16:12:22',_binary '\0'),(1511,5,'已退回','5','bpm_task_status',0,'warning','','','1','2024-03-16 19:10:46','1','2024-03-08 22:41:40',_binary '\0'),(1512,6,'委派中','6','bpm_task_status',0,'primary','','','1','2024-03-17 10:06:22','1','2024-03-08 22:41:40',_binary '\0'),(1513,7,'审批通过中','7','bpm_task_status',0,'success','','','1','2024-03-17 10:06:47','1','2024-03-08 22:41:41',_binary '\0'),(1514,0,'待审批','0','bpm_task_status',0,'info','','','1','2024-03-17 10:07:11','1','2024-03-08 22:41:42',_binary '\0'),(1515,35,'发起人自选','35','bpm_task_candidate_strategy',0,'','','','1','2024-03-22 19:45:16','1','2024-03-22 19:45:16',_binary '\0'),(1516,1,'执行监听器','execution','bpm_process_listener_type',0,'primary','','','1','2024-03-23 12:54:03','1','2024-03-23 19:14:19',_binary '\0'),(1517,1,'任务监听器','task','bpm_process_listener_type',0,'success','','','1','2024-03-23 12:54:13','1','2024-03-23 19:14:24',_binary '\0'),(1526,1,'Java 类','class','bpm_process_listener_value_type',0,'primary','','','1','2024-03-23 15:08:45','1','2024-03-23 19:14:32',_binary '\0'),(1527,2,'表达式','expression','bpm_process_listener_value_type',0,'success','','','1','2024-03-23 15:09:06','1','2024-03-23 19:14:38',_binary '\0'),(1528,3,'代理表达式','delegateExpression','bpm_process_listener_value_type',0,'info','','','1','2024-03-23 15:11:23','1','2024-03-23 19:14:41',_binary '\0'),(1529,1,'天','1','date_interval',0,'','','','1','2024-03-29 22:50:26','1','2024-03-29 22:50:26',_binary '\0'),(1530,2,'周','2','date_interval',0,'','','','1','2024-03-29 22:50:36','1','2024-03-29 22:50:36',_binary '\0'),(1531,3,'月','3','date_interval',0,'','','','1','2024-03-29 22:50:46','1','2024-03-29 22:50:54',_binary '\0'),(1532,4,'季度','4','date_interval',0,'','','','1','2024-03-29 22:51:01','1','2024-03-29 22:51:01',_binary '\0'),(1533,5,'年','5','date_interval',0,'','','','1','2024-03-29 22:51:07','1','2024-03-29 22:51:07',_binary '\0'),(1537,1,'OpenAI','OpenAI','ai_platform',0,'','','','1','2024-05-09 22:33:47','1','2024-05-09 22:58:46',_binary '\0'),(1538,2,'Ollama','Ollama','ai_platform',0,'','','','1','2024-05-17 23:02:55','1','2024-05-17 23:02:55',_binary '\0'),(1539,3,'文心一言','YiYan','ai_platform',0,'','','','1','2024-05-18 09:24:20','1','2024-05-18 09:29:01',_binary '\0'),(1540,4,'讯飞星火','XingHuo','ai_platform',0,'','','','1','2024-05-18 10:08:56','1','2024-05-18 10:08:56',_binary '\0'),(1541,5,'通义千问','TongYi','ai_platform',0,'','','','1','2024-05-18 10:32:29','1','2024-07-06 15:42:29',_binary '\0'),(1542,6,'StableDiffusion','StableDiffusion','ai_platform',0,'','','','1','2024-06-01 15:09:31','1','2024-06-01 15:10:25',_binary '\0'),(1543,10,'进行中','10','ai_image_status',0,'primary','','','1','2024-06-26 20:51:41','1','2024-06-26 20:52:48',_binary '\0'),(1544,20,'已完成','20','ai_image_status',0,'success','','','1','2024-06-26 20:52:07','1','2024-06-26 20:52:41',_binary '\0'),(1545,30,'已失败','30','ai_image_status',0,'warning','','','1','2024-06-26 20:52:25','1','2024-06-26 20:52:35',_binary '\0'),(1546,7,'Midjourney','Midjourney','ai_platform',0,'','','','1','2024-06-26 22:14:46','1','2024-06-26 22:14:46',_binary '\0'),(1547,10,'进行中','10','ai_music_status',0,'primary','','','1','2024-06-27 22:45:22','1','2024-06-28 00:56:17',_binary '\0'),(1548,20,'已完成','20','ai_music_status',0,'success','','','1','2024-06-27 22:45:33','1','2024-06-28 00:56:18',_binary '\0'),(1549,30,'已失败','30','ai_music_status',0,'danger','','','1','2024-06-27 22:45:44','1','2024-06-28 00:56:19',_binary '\0'),(1550,1,'歌词模式','1','ai_generate_mode',0,'','','','1','2024-06-27 22:46:31','1','2024-06-28 01:22:25',_binary '\0'),(1551,2,'描述模式','2','ai_generate_mode',0,'','','','1','2024-06-27 22:46:37','1','2024-06-28 01:22:24',_binary '\0'),(1552,8,'Suno','Suno','ai_platform',0,'','','','1','2024-06-29 09:13:36','1','2024-06-29 09:13:41',_binary '\0'),(1553,9,'DeepSeek','DeepSeek','ai_platform',0,'','','','1','2024-07-06 12:04:30','1','2024-07-06 12:05:20',_binary '\0'),(1554,10,'智谱','ZhiPu','ai_platform',0,'','','','1','2024-07-06 18:00:35','1','2024-07-06 18:00:35',_binary '\0'),(1555,4,'长','4','ai_write_length',0,'','','','1','2024-07-07 15:49:03','1','2024-07-07 15:49:03',_binary '\0'),(1556,5,'段落','5','ai_write_format',0,'','','','1','2024-07-07 15:49:54','1','2024-07-07 15:49:54',_binary '\0'),(1557,6,'文章','6','ai_write_format',0,'','','','1','2024-07-07 15:50:05','1','2024-07-07 15:50:05',_binary '\0'),(1558,7,'博客文章','7','ai_write_format',0,'','','','1','2024-07-07 15:50:23','1','2024-07-07 15:50:23',_binary '\0'),(1559,8,'想法','8','ai_write_format',0,'','','','1','2024-07-07 15:50:31','1','2024-07-07 15:50:31',_binary '\0'),(1560,9,'大纲','9','ai_write_format',0,'','','','1','2024-07-07 15:50:37','1','2024-07-07 15:50:37',_binary '\0'),(1561,1,'自动','1','ai_write_tone',0,'','','','1','2024-07-07 15:51:06','1','2024-07-07 15:51:06',_binary '\0'),(1562,2,'友善','2','ai_write_tone',0,'','','','1','2024-07-07 15:51:19','1','2024-07-07 15:51:19',_binary '\0'),(1563,3,'随意','3','ai_write_tone',0,'','','','1','2024-07-07 15:51:27','1','2024-07-07 15:51:27',_binary '\0'),(1564,4,'友好','4','ai_write_tone',0,'','','','1','2024-07-07 15:51:37','1','2024-07-07 15:51:37',_binary '\0'),(1565,5,'专业','5','ai_write_tone',0,'','','','1','2024-07-07 15:51:49','1','2024-07-07 15:52:02',_binary '\0'),(1566,6,'诙谐','6','ai_write_tone',0,'','','','1','2024-07-07 15:52:15','1','2024-07-07 15:52:15',_binary '\0'),(1567,7,'有趣','7','ai_write_tone',0,'','','','1','2024-07-07 15:52:24','1','2024-07-07 15:52:24',_binary '\0'),(1568,8,'正式','8','ai_write_tone',0,'','','','1','2024-07-07 15:54:33','1','2024-07-07 15:54:33',_binary '\0'),(1569,5,'段落','5','ai_write_format',0,'','','','1','2024-07-07 15:49:54','1','2024-07-07 15:49:54',_binary '\0'),(1570,1,'自动','1','ai_write_format',0,'','','','1','2024-07-07 15:19:34','1','2024-07-07 15:19:34',_binary '\0'),(1571,2,'电子邮件','2','ai_write_format',0,'','','','1','2024-07-07 15:19:50','1','2024-07-07 15:49:30',_binary '\0'),(1572,3,'消息','3','ai_write_format',0,'','','','1','2024-07-07 15:20:01','1','2024-07-07 15:49:38',_binary '\0'),(1573,4,'评论','4','ai_write_format',0,'','','','1','2024-07-07 15:20:13','1','2024-07-07 15:49:45',_binary '\0'),(1574,1,'自动','1','ai_write_language',0,'','','','1','2024-07-07 15:44:18','1','2024-07-07 15:44:18',_binary '\0'),(1575,2,'中文','2','ai_write_language',0,'','','','1','2024-07-07 15:44:28','1','2024-07-07 15:44:28',_binary '\0'),(1576,3,'英文','3','ai_write_language',0,'','','','1','2024-07-07 15:44:37','1','2024-07-07 15:44:37',_binary '\0'),(1577,4,'韩语','4','ai_write_language',0,'','','','1','2024-07-07 15:46:28','1','2024-07-07 15:46:28',_binary '\0'),(1578,5,'日语','5','ai_write_language',0,'','','','1','2024-07-07 15:46:44','1','2024-07-07 15:46:44',_binary '\0'),(1579,1,'自动','1','ai_write_length',0,'','','','1','2024-07-07 15:48:34','1','2024-07-07 15:48:34',_binary '\0'),(1580,2,'短','2','ai_write_length',0,'','','','1','2024-07-07 15:48:44','1','2024-07-07 15:48:44',_binary '\0'),(1581,3,'中等','3','ai_write_length',0,'','','','1','2024-07-07 15:48:52','1','2024-07-07 15:48:52',_binary '\0'),(1582,4,'长','4','ai_write_length',0,'','','','1','2024-07-07 15:49:03','1','2024-07-07 15:49:03',_binary '\0'),(1584,1,'撰写','1','ai_write_type',0,'','','','1','2024-07-10 21:26:00','1','2024-07-10 21:26:00',_binary '\0'),(1585,2,'回复','2','ai_write_type',0,'','','','1','2024-07-10 21:26:06','1','2024-07-10 21:26:06',_binary '\0'),(1586,2,'腾讯云','TENCENT','system_sms_channel_code',0,'','','','1','2024-07-22 22:23:16','1','2024-07-22 22:23:16',_binary '\0'),(1587,3,'华为云','HUAWEI','system_sms_channel_code',0,'','','','1','2024-07-22 22:23:46','1','2024-07-22 22:23:53',_binary '\0'),(1588,1,'OpenAI 微软','AzureOpenAI','ai_platform',0,'','','','1','2024-08-10 14:07:41','1','2024-08-10 14:07:41',_binary '\0'),(1589,10,'BPMN 设计器','10','bpm_model_type',0,'primary','','','1','2024-08-26 15:22:17','1','2024-08-26 16:46:02',_binary '\0'),(1590,20,'SIMPLE 设计器','20','bpm_model_type',0,'success','','','1','2024-08-26 15:22:27','1','2024-08-26 16:45:58',_binary '\0'),(1591,4,'七牛云','QINIU','system_sms_channel_code',0,'','','','1','2024-08-31 08:45:03','1','2024-08-31 08:45:24',_binary '\0');
/*!40000 ALTER TABLE `system_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_dict_type`
--

DROP TABLE IF EXISTS `system_dict_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_dict_type` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
                                    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典名称',
                                    `type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '字典类型',
                                    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
                                    `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    `deleted_time` datetime DEFAULT NULL COMMENT '删除时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=630 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_dict_type`
--

LOCK TABLES `system_dict_type` WRITE;
/*!40000 ALTER TABLE `system_dict_type` DISABLE KEYS */;
INSERT INTO `system_dict_type` VALUES (1,'用户性别','system_user_sex',0,NULL,'admin','2021-01-05 17:03:48','1','2022-05-16 20:29:32',_binary '\0',NULL),(6,'参数类型','infra_config_type',0,NULL,'admin','2021-01-05 17:03:48','','2022-02-01 16:36:54',_binary '\0',NULL),(7,'通知类型','system_notice_type',0,NULL,'admin','2021-01-05 17:03:48','','2022-02-01 16:35:26',_binary '\0',NULL),(9,'操作类型','infra_operate_type',0,NULL,'admin','2021-01-05 17:03:48','1','2024-03-14 12:44:01',_binary '\0',NULL),(10,'系统状态','common_status',0,NULL,'admin','2021-01-05 17:03:48','','2022-02-01 16:21:28',_binary '\0',NULL),(11,'Boolean 是否类型','infra_boolean_string',0,'boolean 转是否','','2021-01-19 03:20:08','','2022-02-01 16:37:10',_binary '\0',NULL),(104,'登陆结果','system_login_result',0,'登陆结果','','2021-01-18 06:17:11','','2022-02-01 16:36:00',_binary '\0',NULL),(106,'代码生成模板类型','infra_codegen_template_type',0,NULL,'','2021-02-05 07:08:06','1','2022-05-16 20:26:50',_binary '\0',NULL),(107,'定时任务状态','infra_job_status',0,NULL,'','2021-02-07 07:44:16','','2022-02-01 16:51:11',_binary '\0',NULL),(108,'定时任务日志状态','infra_job_log_status',0,NULL,'','2021-02-08 10:03:51','','2022-02-01 16:50:43',_binary '\0',NULL),(109,'用户类型','user_type',0,NULL,'','2021-02-26 00:15:51','','2021-02-26 00:15:51',_binary '\0',NULL),(110,'API 异常数据的处理状态','infra_api_error_log_process_status',0,NULL,'','2021-02-26 07:07:01','','2022-02-01 16:50:53',_binary '\0',NULL),(111,'短信渠道编码','system_sms_channel_code',0,NULL,'1','2021-04-05 01:04:50','1','2022-02-16 02:09:08',_binary '\0',NULL),(112,'短信模板的类型','system_sms_template_type',0,NULL,'1','2021-04-05 21:50:43','1','2022-02-01 16:35:06',_binary '\0',NULL),(113,'短信发送状态','system_sms_send_status',0,NULL,'1','2021-04-11 20:18:03','1','2022-02-01 16:35:09',_binary '\0',NULL),(114,'短信接收状态','system_sms_receive_status',0,NULL,'1','2021-04-11 20:27:14','1','2022-02-01 16:35:14',_binary '\0',NULL),(116,'登陆日志的类型','system_login_type',0,'登陆日志的类型','1','2021-10-06 00:50:46','1','2022-02-01 16:35:56',_binary '\0',NULL),(117,'OA 请假类型','bpm_oa_leave_type',0,NULL,'1','2021-09-21 22:34:33','1','2022-01-22 10:41:37',_binary '\0',NULL),(139,'流程实例的状态','bpm_process_instance_status',0,'流程实例的状态','1','2022-01-07 23:46:42','1','2022-01-07 23:46:42',_binary '\0',NULL),(140,'流程实例的结果','bpm_task_status',0,'流程实例的结果','1','2022-01-07 23:48:10','1','2024-03-08 22:42:03',_binary '\0',NULL),(141,'流程的表单类型','bpm_model_form_type',0,'流程的表单类型','103','2022-01-11 23:50:45','103','2022-01-11 23:50:45',_binary '\0',NULL),(142,'任务分配规则的类型','bpm_task_candidate_strategy',0,'BPM 任务的候选人的策略','103','2022-01-12 23:21:04','103','2024-03-06 02:53:59',_binary '\0',NULL),(144,'代码生成的场景枚举','infra_codegen_scene',0,'代码生成的场景枚举','1','2022-02-02 13:14:45','1','2022-03-10 16:33:46',_binary '\0',NULL),(145,'角色类型','system_role_type',0,'角色类型','1','2022-02-16 13:01:46','1','2022-02-16 13:01:46',_binary '\0',NULL),(146,'文件存储器','infra_file_storage',0,'文件存储器','1','2022-03-15 00:24:38','1','2022-03-15 00:24:38',_binary '\0',NULL),(147,'OAuth 2.0 授权类型','system_oauth2_grant_type',0,'OAuth 2.0 授权类型（模式）','1','2022-05-12 00:20:52','1','2022-05-11 16:25:49',_binary '\0',NULL),(160,'终端','terminal',0,'终端','1','2022-12-10 10:50:50','1','2022-12-10 10:53:11',_binary '\0',NULL),(166,'邮件发送状态','system_mail_send_status',0,'邮件发送状态','1','2023-01-26 09:53:13','1','2023-01-26 09:53:13',_binary '\0','1970-01-01 00:00:00'),(167,'站内信模版的类型','system_notify_template_type',0,'站内信模版的类型','1','2023-01-28 10:35:10','1','2023-01-28 10:35:10',_binary '\0','1970-01-01 00:00:00'),(168,'代码生成的前端类型','infra_codegen_front_type',0,'','1','2023-04-12 23:57:52','1','2023-04-12 23:57:52',_binary '\0','1970-01-01 00:00:00'),(176,'分佣模式','brokerage_enabled_condition',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(177,'分销关系绑定模式','brokerage_bind_mode',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(178,'佣金提现类型','brokerage_withdraw_type',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(179,'佣金记录业务类型','brokerage_record_biz_type',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(180,'佣金记录状态','brokerage_record_status',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(181,'佣金提现状态','brokerage_withdraw_status',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(182,'佣金提现银行','brokerage_bank_name',0,NULL,'','2023-09-28 02:46:05','','2023-09-28 02:46:05',_binary '\0',NULL),(601,'社交类型','system_social_type',0,'','1','2023-11-04 13:03:54','1','2023-11-04 13:03:54',_binary '\0','1970-01-01 00:00:00'),(613,'BPM 监听器类型','bpm_process_listener_type',0,'','1','2024-03-23 12:52:24','1','2024-03-09 15:54:28',_binary '\0','1970-01-01 00:00:00'),(615,'BPM 监听器值类型','bpm_process_listener_value_type',0,'','1','2024-03-23 13:00:31','1','2024-03-23 13:00:31',_binary '\0','1970-01-01 00:00:00'),(616,'时间间隔','date_interval',0,'','1','2024-03-29 22:50:09','1','2024-03-29 22:50:09',_binary '\0','1970-01-01 00:00:00'),(620,'AI 模型平台','ai_platform',0,'','1','2024-05-09 22:27:38','1','2024-05-09 22:27:38',_binary '\0','1970-01-01 00:00:00'),(621,'AI 绘画状态','ai_image_status',0,'','1','2024-06-26 20:51:23','1','2024-06-26 20:51:23',_binary '\0','1970-01-01 00:00:00'),(622,'AI 音乐状态','ai_music_status',0,'','1','2024-06-27 22:45:07','1','2024-06-28 00:56:27',_binary '\0','1970-01-01 00:00:00'),(623,'AI 音乐生成模式','ai_generate_mode',0,'','1','2024-06-27 22:46:21','1','2024-06-28 01:22:29',_binary '\0','1970-01-01 00:00:00'),(624,'写作语气','ai_write_tone',0,'','1','2024-07-07 15:19:02','1','2024-07-07 15:19:02',_binary '\0','1970-01-01 00:00:00'),(625,'写作语言','ai_write_language',0,'','1','2024-07-07 15:18:52','1','2024-07-07 15:18:52',_binary '\0','1970-01-01 00:00:00'),(626,'写作长度','ai_write_length',0,'','1','2024-07-07 15:18:41','1','2024-07-07 15:18:41',_binary '\0','1970-01-01 00:00:00'),(627,'写作格式','ai_write_format',0,'','1','2024-07-07 15:14:34','1','2024-07-07 15:14:34',_binary '\0','1970-01-01 00:00:00'),(628,'AI 写作类型','ai_write_type',0,'','1','2024-07-10 21:25:29','1','2024-07-10 21:25:29',_binary '\0','1970-01-01 00:00:00'),(629,'BPM 流程模型类型','bpm_model_type',0,'','1','2024-08-26 15:21:43','1','2024-08-26 15:21:43',_binary '\0','1970-01-01 00:00:00');
/*!40000 ALTER TABLE `system_dict_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_login_log`
--

DROP TABLE IF EXISTS `system_login_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_login_log` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
                                    `log_type` bigint(20) NOT NULL COMMENT '日志类型',
                                    `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
                                    `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户编号',
                                    `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型',
                                    `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户账号',
                                    `result` tinyint(4) NOT NULL COMMENT '登陆结果',
                                    `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
                                    `user_agent` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
                                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3348 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统访问记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_login_log`
--

LOCK TABLES `system_login_log` WRITE;
/*!40000 ALTER TABLE `system_login_log` DISABLE KEYS */;
INSERT INTO `system_login_log` VALUES (3335,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 14:58:49',NULL,'2024-12-26 14:58:49',_binary '\0',1),(3336,200,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','1','2024-12-26 15:13:23','1','2024-12-26 15:13:23',_binary '\0',1),(3337,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 15:13:29',NULL,'2024-12-26 15:13:29',_binary '\0',1),(3338,200,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','1','2024-12-26 15:23:11','1','2024-12-26 15:23:11',_binary '\0',1),(3339,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 15:25:05',NULL,'2024-12-26 15:25:05',_binary '\0',1),(3340,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 15:31:45',NULL,'2024-12-26 15:31:45',_binary '\0',1),(3341,200,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','1','2024-12-26 15:44:31','1','2024-12-26 15:44:31',_binary '\0',1),(3342,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 15:44:33',NULL,'2024-12-26 15:44:33',_binary '\0',1),(3343,200,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','1','2024-12-26 16:35:54','1','2024-12-26 16:35:54',_binary '\0',1),(3344,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 16:36:01',NULL,'2024-12-26 16:36:01',_binary '\0',1),(3345,200,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','1','2024-12-26 16:39:40','1','2024-12-26 16:39:40',_binary '\0',1),(3346,100,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 16:40:21',NULL,'2024-12-26 16:40:21',_binary '\0',1),(3347,200,'',1,2,'admin',0,'0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','1','2024-12-26 16:42:23','1','2024-12-26 16:42:23',_binary '\0',1);
/*!40000 ALTER TABLE `system_login_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_mail_account`
--

DROP TABLE IF EXISTS `system_mail_account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_mail_account` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `mail` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
                                       `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
                                       `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
                                       `host` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SMTP 服务器域名',
                                       `port` int(11) NOT NULL COMMENT 'SMTP 服务器端口',
                                       `ssl_enable` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启 SSL',
                                       `starttls_enable` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启 STARTTLS',
                                       `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱账号表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_mail_account`
--

LOCK TABLES `system_mail_account` WRITE;
/*!40000 ALTER TABLE `system_mail_account` DISABLE KEYS */;
INSERT INTO `system_mail_account` VALUES (1,'<EMAIL>','<EMAIL>','1234576','127.0.0.1',8080,_binary '\0',_binary '\0','1','2023-01-25 17:39:52','1','2024-07-27 22:39:12',_binary '\0'),(2,'<EMAIL>','<EMAIL>','WBZTEINMIFVRYSOE','smtp.163.com',465,_binary '',_binary '\0','1','2023-01-26 01:26:03','1','2023-04-12 22:39:38',_binary '\0'),(3,'<EMAIL>','3335','11234','yunai1.cn',466,_binary '\0',_binary '\0','1','2023-01-27 15:06:38','1','2023-01-27 07:08:36',_binary ''),(4,'<EMAIL>','2','3','4',5,_binary '',_binary '\0','1','2023-04-12 23:05:06','1','2023-04-12 15:05:11',_binary '');
/*!40000 ALTER TABLE `system_mail_account` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_mail_log`
--

DROP TABLE IF EXISTS `system_mail_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_mail_log` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                   `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
                                   `user_type` tinyint(4) DEFAULT NULL COMMENT '用户类型',
                                   `to_mail` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接收邮箱地址',
                                   `account_id` bigint(20) NOT NULL COMMENT '邮箱账号编号',
                                   `from_mail` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送邮箱地址',
                                   `template_id` bigint(20) NOT NULL COMMENT '模板编号',
                                   `template_code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
                                   `template_nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模版发送人名称',
                                   `template_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件标题',
                                   `template_content` varchar(10240) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件内容',
                                   `template_params` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件参数',
                                   `send_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发送状态',
                                   `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                   `send_message_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送返回的消息 ID',
                                   `send_exception` varchar(4096) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送异常',
                                   `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=359 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_mail_log`
--

LOCK TABLES `system_mail_log` WRITE;
/*!40000 ALTER TABLE `system_mail_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_mail_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_mail_template`
--

DROP TABLE IF EXISTS `system_mail_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_mail_template` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
                                        `code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
                                        `account_id` bigint(20) NOT NULL COMMENT '发送的邮箱账号编号',
                                        `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送人名称',
                                        `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板标题',
                                        `content` varchar(10240) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板内容',
                                        `params` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数数组',
                                        `status` tinyint(4) NOT NULL COMMENT '开启状态',
                                        `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件模版表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_mail_template`
--

LOCK TABLES `system_mail_template` WRITE;
/*!40000 ALTER TABLE `system_mail_template` DISABLE KEYS */;
INSERT INTO `system_mail_template` VALUES (13,'后台用户短信登录','admin-sms-login',1,'奥特曼','你猜我猜','<p>您的验证码是{code}，名字是{name}</p>','[\"code\",\"name\"]',0,'3','1','2021-10-11 08:10:00','1','2023-12-02 19:51:14',_binary '\0'),(14,'测试模版','test_01',2,'芋艿','一个标题','<p>你是 {key01} 吗？</p><p><br></p><p>是的话，赶紧 {key02} 一下！</p>','[\"key01\",\"key02\"]',0,NULL,'1','2023-01-26 01:27:40','1','2023-01-27 10:32:16',_binary '\0'),(15,'3','2',2,'7','4','<p>45</p>','[]',1,'80','1','2023-01-27 15:50:35','1','2023-01-27 16:34:49',_binary '\0');
/*!40000 ALTER TABLE `system_mail_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_menu`
--

DROP TABLE IF EXISTS `system_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_menu` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
                               `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
                               `permission` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '权限标识',
                               `type` tinyint(4) NOT NULL COMMENT '菜单类型',
                               `sort` int(11) NOT NULL DEFAULT '0' COMMENT '显示顺序',
                               `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父菜单ID',
                               `path` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
                               `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
                               `component` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
                               `component_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件名',
                               `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '菜单状态',
                               `visible` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否可见',
                               `keep_alive` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否缓存',
                               `always_show` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否总是显示',
                               `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2814 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_menu`
--

LOCK TABLES `system_menu` WRITE;
/*!40000 ALTER TABLE `system_menu` DISABLE KEYS */;
INSERT INTO `system_menu` VALUES (1,'系统管理','',1,10,0,'/system','ep:tools',NULL,NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-06-18 01:19:41',_binary '\0'),(2,'基础设施','',1,20,0,'/infra','ep:monitor',NULL,NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-03-01 08:28:40',_binary '\0'),(100,'用户管理','system:user:list',2,1,1,'user','ep:avatar','system/user/index','SystemUser',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:02:04',_binary '\0'),(101,'角色管理','',2,2,1,'role','ep:user','system/role/index','SystemRole',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-05-01 18:35:29',_binary '\0'),(102,'菜单管理','',2,3,1,'menu','ep:menu','system/menu/index','SystemMenu',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:03:50',_binary '\0'),(103,'部门管理','',2,4,1,'dept','fa:address-card','system/dept/index','SystemDept',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:06:28',_binary '\0'),(104,'岗位管理','',2,5,1,'post','fa:address-book-o','system/post/index','SystemPost',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:06:39',_binary '\0'),(105,'字典管理','',2,6,1,'dict','ep:collection','system/dict/index','SystemDictType',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:07:12',_binary '\0'),(106,'配置管理','',2,8,2,'config','fa:connectdevelop','infra/config/index','InfraConfig',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-04-23 00:02:45',_binary '\0'),(107,'通知公告','',2,4,2739,'notice','ep:takeaway-box','system/notice/index','SystemNotice',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-04-22 23:56:17',_binary '\0'),(108,'审计日志','',1,9,1,'log','ep:document-copy','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:08:30',_binary '\0'),(109,'令牌管理','',2,2,1261,'token','fa:key','system/oauth2/token/index','SystemTokenClient',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:13:48',_binary '\0'),(110,'定时任务','',2,7,2,'job','fa-solid:tasks','infra/job/index','InfraJob',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 08:57:36',_binary '\0'),(111,'MySQL 监控','',2,1,2740,'druid','fa-solid:box','infra/druid/index','InfraDruid',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-04-23 00:05:58',_binary '\0'),(112,'Java 监控','',2,3,2740,'admin-server','ep:coffee-cup','infra/server/index','InfraAdminServer',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-04-23 00:06:57',_binary '\0'),(113,'Redis 监控','',2,2,2740,'redis','fa:reddit-square','infra/redis/index','InfraRedis',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-04-23 00:06:09',_binary '\0'),(114,'表单构建','infra:build:list',2,2,2,'build','fa:wpforms','infra/build/index','InfraBuild',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 08:51:35',_binary '\0'),(115,'代码生成','infra:codegen:query',2,1,2,'codegen','ep:document-copy','infra/codegen/index','InfraCodegen',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 08:51:06',_binary '\0'),(116,'API 接口','infra:swagger:list',2,3,2,'swagger','fa:fighter-jet','infra/swagger/index','InfraSwagger',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-04-23 00:01:24',_binary '\0'),(500,'操作日志','',2,1,108,'operate-log','ep:position','system/operatelog/index','SystemOperateLog',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:09:59',_binary '\0'),(501,'登录日志','',2,2,108,'login-log','ep:promotion','system/loginlog/index','SystemLoginLog',0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2024-02-29 01:10:29',_binary '\0'),(1001,'用户查询','system:user:query',3,1,100,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1002,'用户新增','system:user:create',3,2,100,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1003,'用户修改','system:user:update',3,3,100,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1004,'用户删除','system:user:delete',3,4,100,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1005,'用户导出','system:user:export',3,5,100,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1006,'用户导入','system:user:import',3,6,100,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1007,'重置密码','system:user:update-password',3,7,100,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1008,'角色查询','system:role:query',3,1,101,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1009,'角色新增','system:role:create',3,2,101,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1010,'角色修改','system:role:update',3,3,101,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1011,'角色删除','system:role:delete',3,4,101,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1012,'角色导出','system:role:export',3,5,101,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1013,'菜单查询','system:menu:query',3,1,102,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1014,'菜单新增','system:menu:create',3,2,102,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1015,'菜单修改','system:menu:update',3,3,102,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1016,'菜单删除','system:menu:delete',3,4,102,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1017,'部门查询','system:dept:query',3,1,103,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1018,'部门新增','system:dept:create',3,2,103,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1019,'部门修改','system:dept:update',3,3,103,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1020,'部门删除','system:dept:delete',3,4,103,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1021,'岗位查询','system:post:query',3,1,104,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1022,'岗位新增','system:post:create',3,2,104,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1023,'岗位修改','system:post:update',3,3,104,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1024,'岗位删除','system:post:delete',3,4,104,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1025,'岗位导出','system:post:export',3,5,104,'','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1026,'字典查询','system:dict:query',3,1,105,'#','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1027,'字典新增','system:dict:create',3,2,105,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1028,'字典修改','system:dict:update',3,3,105,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1029,'字典删除','system:dict:delete',3,4,105,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1030,'字典导出','system:dict:export',3,5,105,'#','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1031,'配置查询','infra:config:query',3,1,106,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1032,'配置新增','infra:config:create',3,2,106,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1033,'配置修改','infra:config:update',3,3,106,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1034,'配置删除','infra:config:delete',3,4,106,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1035,'配置导出','infra:config:export',3,5,106,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1036,'公告查询','system:notice:query',3,1,107,'#','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1037,'公告新增','system:notice:create',3,2,107,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1038,'公告修改','system:notice:update',3,3,107,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1039,'公告删除','system:notice:delete',3,4,107,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1040,'操作查询','system:operate-log:query',3,1,500,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1042,'日志导出','system:operate-log:export',3,2,500,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1043,'登录查询','system:login-log:query',3,1,501,'#','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1045,'日志导出','system:login-log:export',3,3,501,'#','#','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1046,'令牌列表','system:oauth2-token:page',3,1,109,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-05-09 23:54:42',_binary '\0'),(1048,'令牌删除','system:oauth2-token:delete',3,2,109,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-05-09 23:54:53',_binary '\0'),(1050,'任务新增','infra:job:create',3,2,110,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1051,'任务修改','infra:job:update',3,3,110,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1052,'任务删除','infra:job:delete',3,4,110,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1053,'状态修改','infra:job:update',3,5,110,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1054,'任务导出','infra:job:export',3,7,110,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','','2022-04-20 17:03:10',_binary '\0'),(1056,'生成修改','infra:codegen:update',3,2,115,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1057,'生成删除','infra:codegen:delete',3,3,115,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1058,'导入代码','infra:codegen:create',3,2,115,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1059,'预览代码','infra:codegen:preview',3,4,115,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1060,'生成代码','infra:codegen:download',3,5,115,'','','',NULL,0,_binary '',_binary '',_binary '','admin','2021-01-05 17:03:48','1','2022-04-20 17:03:10',_binary '\0'),(1063,'设置角色菜单权限','system:permission:assign-role-menu',3,6,101,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-01-06 17:53:44','','2022-04-20 17:03:10',_binary '\0'),(1064,'设置角色数据权限','system:permission:assign-role-data-scope',3,7,101,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-01-06 17:56:31','','2022-04-20 17:03:10',_binary '\0'),(1065,'设置用户角色','system:permission:assign-user-role',3,8,101,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-01-07 10:23:28','','2022-04-20 17:03:10',_binary '\0'),(1066,'获得 Redis 监控信息','infra:redis:get-monitor-info',3,1,113,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-01-26 01:02:31','','2022-04-20 17:03:10',_binary '\0'),(1067,'获得 Redis Key 列表','infra:redis:get-key-list',3,2,113,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-01-26 01:02:52','','2022-04-20 17:03:10',_binary '\0'),(1070,'代码生成案例','',1,1,2,'demo','ep:aim','infra/testDemo/index',NULL,0,_binary '',_binary '',_binary '','','2021-02-06 12:42:49','1','2023-11-15 23:45:53',_binary '\0'),(1075,'任务触发','infra:job:trigger',3,8,110,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-02-07 13:03:10','','2022-04-20 17:03:10',_binary '\0'),(1077,'链路追踪','',2,4,2740,'skywalking','fa:eye','infra/skywalking/index','InfraSkyWalking',0,_binary '',_binary '',_binary '','','2021-02-08 20:41:31','1','2024-04-23 00:07:15',_binary '\0'),(1078,'访问日志','',2,1,1083,'api-access-log','ep:place','infra/apiAccessLog/index','InfraApiAccessLog',0,_binary '',_binary '',_binary '','','2021-02-26 01:32:59','1','2024-02-29 08:54:57',_binary '\0'),(1082,'日志导出','infra:api-access-log:export',3,2,1078,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-02-26 01:32:59','1','2022-04-20 17:03:10',_binary '\0'),(1083,'API 日志','',2,4,2,'log','fa:tasks',NULL,NULL,0,_binary '',_binary '',_binary '','','2021-02-26 02:18:24','1','2024-04-22 23:58:36',_binary '\0'),(1084,'错误日志','infra:api-error-log:query',2,2,1083,'api-error-log','ep:warning-filled','infra/apiErrorLog/index','InfraApiErrorLog',0,_binary '',_binary '',_binary '','','2021-02-26 07:53:20','1','2024-02-29 08:55:17',_binary '\0'),(1085,'日志处理','infra:api-error-log:update-status',3,2,1084,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-02-26 07:53:20','1','2022-04-20 17:03:10',_binary '\0'),(1086,'日志导出','infra:api-error-log:export',3,3,1084,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-02-26 07:53:20','1','2022-04-20 17:03:10',_binary '\0'),(1087,'任务查询','infra:job:query',3,1,110,'','','',NULL,0,_binary '',_binary '',_binary '','1','2021-03-10 01:26:19','1','2022-04-20 17:03:10',_binary '\0'),(1088,'日志查询','infra:api-access-log:query',3,1,1078,'','','',NULL,0,_binary '',_binary '',_binary '','1','2021-03-10 01:28:04','1','2022-04-20 17:03:10',_binary '\0'),(1089,'日志查询','infra:api-error-log:query',3,1,1084,'','','',NULL,0,_binary '',_binary '',_binary '','1','2021-03-10 01:29:09','1','2022-04-20 17:03:10',_binary '\0'),(1090,'文件列表','',2,5,1243,'file','ep:upload-filled','infra/file/index','InfraFile',0,_binary '',_binary '',_binary '','','2021-03-12 20:16:20','1','2024-02-29 08:53:02',_binary '\0'),(1091,'文件查询','infra:file:query',3,1,1090,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-03-12 20:16:20','','2022-04-20 17:03:10',_binary '\0'),(1092,'文件删除','infra:file:delete',3,4,1090,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-03-12 20:16:20','','2022-04-20 17:03:10',_binary '\0'),(1093,'短信管理','',1,1,2739,'sms','ep:message',NULL,NULL,0,_binary '',_binary '',_binary '','1','2021-04-05 01:10:16','1','2024-04-22 23:56:03',_binary '\0'),(1094,'短信渠道','',2,0,1093,'sms-channel','fa:stack-exchange','system/sms/channel/index','SystemSmsChannel',0,_binary '',_binary '',_binary '','','2021-04-01 11:07:15','1','2024-02-29 01:15:54',_binary '\0'),(1095,'短信渠道查询','system:sms-channel:query',3,1,1094,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 11:07:15','','2022-04-20 17:03:10',_binary '\0'),(1096,'短信渠道创建','system:sms-channel:create',3,2,1094,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 11:07:15','','2022-04-20 17:03:10',_binary '\0'),(1097,'短信渠道更新','system:sms-channel:update',3,3,1094,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 11:07:15','','2022-04-20 17:03:10',_binary '\0'),(1098,'短信渠道删除','system:sms-channel:delete',3,4,1094,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 11:07:15','','2022-04-20 17:03:10',_binary '\0'),(1100,'短信模板','',2,1,1093,'sms-template','ep:connection','system/sms/template/index','SystemSmsTemplate',0,_binary '',_binary '',_binary '','','2021-04-01 17:35:17','1','2024-02-29 01:16:18',_binary '\0'),(1101,'短信模板查询','system:sms-template:query',3,1,1100,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 17:35:17','','2022-04-20 17:03:10',_binary '\0'),(1102,'短信模板创建','system:sms-template:create',3,2,1100,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 17:35:17','','2022-04-20 17:03:10',_binary '\0'),(1103,'短信模板更新','system:sms-template:update',3,3,1100,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 17:35:17','','2022-04-20 17:03:10',_binary '\0'),(1104,'短信模板删除','system:sms-template:delete',3,4,1100,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 17:35:17','','2022-04-20 17:03:10',_binary '\0'),(1105,'短信模板导出','system:sms-template:export',3,5,1100,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-01 17:35:17','','2022-04-20 17:03:10',_binary '\0'),(1106,'发送测试短信','system:sms-template:send-sms',3,6,1100,'','','',NULL,0,_binary '',_binary '',_binary '','1','2021-04-11 00:26:40','1','2022-04-20 17:03:10',_binary '\0'),(1107,'短信日志','',2,2,1093,'sms-log','fa:edit','system/sms/log/index','SystemSmsLog',0,_binary '',_binary '',_binary '','','2021-04-11 08:37:05','1','2024-02-29 08:49:02',_binary '\0'),(1108,'短信日志查询','system:sms-log:query',3,1,1107,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-11 08:37:05','','2022-04-20 17:03:10',_binary '\0'),(1109,'短信日志导出','system:sms-log:export',3,5,1107,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-04-11 08:37:05','','2022-04-20 17:03:10',_binary '\0'),(1138,'租户列表','',2,0,1224,'list','ep:house','system/tenant/index','SystemTenant',0,_binary '',_binary '',_binary '','','2021-12-14 12:31:43','1','2024-02-29 01:01:10',_binary '\0'),(1139,'租户查询','system:tenant:query',3,1,1138,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-12-14 12:31:44','','2022-04-20 17:03:10',_binary '\0'),(1140,'租户创建','system:tenant:create',3,2,1138,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-12-14 12:31:44','','2022-04-20 17:03:10',_binary '\0'),(1141,'租户更新','system:tenant:update',3,3,1138,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-12-14 12:31:44','','2022-04-20 17:03:10',_binary '\0'),(1142,'租户删除','system:tenant:delete',3,4,1138,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-12-14 12:31:44','','2022-04-20 17:03:10',_binary '\0'),(1143,'租户导出','system:tenant:export',3,5,1138,'','','',NULL,0,_binary '',_binary '',_binary '','','2021-12-14 12:31:44','','2022-04-20 17:03:10',_binary '\0'),(1224,'租户管理','',2,0,1,'tenant','fa-solid:house-user',NULL,NULL,0,_binary '',_binary '',_binary '','1','2022-02-20 01:41:13','1','2024-02-29 00:59:29',_binary '\0'),(1225,'租户套餐','',2,0,1224,'package','fa:bars','system/tenantPackage/index','SystemTenantPackage',0,_binary '',_binary '',_binary '','','2022-02-19 17:44:06','1','2024-02-29 01:01:43',_binary '\0'),(1226,'租户套餐查询','system:tenant-package:query',3,1,1225,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-02-19 17:44:06','','2022-04-20 17:03:10',_binary '\0'),(1227,'租户套餐创建','system:tenant-package:create',3,2,1225,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-02-19 17:44:06','','2022-04-20 17:03:10',_binary '\0'),(1228,'租户套餐更新','system:tenant-package:update',3,3,1225,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-02-19 17:44:06','','2022-04-20 17:03:10',_binary '\0'),(1229,'租户套餐删除','system:tenant-package:delete',3,4,1225,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-02-19 17:44:06','','2022-04-20 17:03:10',_binary '\0'),(1237,'文件配置','',2,0,1243,'file-config','fa-solid:file-signature','infra/fileConfig/index','InfraFileConfig',0,_binary '',_binary '',_binary '','','2022-03-15 14:35:28','1','2024-02-29 08:52:54',_binary '\0'),(1238,'文件配置查询','infra:file-config:query',3,1,1237,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-03-15 14:35:28','','2022-04-20 17:03:10',_binary '\0'),(1239,'文件配置创建','infra:file-config:create',3,2,1237,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-03-15 14:35:28','','2022-04-20 17:03:10',_binary '\0'),(1240,'文件配置更新','infra:file-config:update',3,3,1237,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-03-15 14:35:28','','2022-04-20 17:03:10',_binary '\0'),(1241,'文件配置删除','infra:file-config:delete',3,4,1237,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-03-15 14:35:28','','2022-04-20 17:03:10',_binary '\0'),(1242,'文件配置导出','infra:file-config:export',3,5,1237,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-03-15 14:35:28','','2022-04-20 17:03:10',_binary '\0'),(1243,'文件管理','',2,6,2,'file','ep:files',NULL,'',0,_binary '',_binary '',_binary '','1','2022-03-16 23:47:40','1','2024-04-23 00:02:11',_binary '\0'),(1255,'数据源配置','',2,1,2,'data-source-config','ep:data-analysis','infra/dataSourceConfig/index','InfraDataSourceConfig',0,_binary '',_binary '',_binary '','','2022-04-27 14:37:32','1','2024-02-29 08:51:25',_binary '\0'),(1256,'数据源配置查询','infra:data-source-config:query',3,1,1255,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-04-27 14:37:32','','2022-04-27 14:37:32',_binary '\0'),(1257,'数据源配置创建','infra:data-source-config:create',3,2,1255,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-04-27 14:37:32','','2022-04-27 14:37:32',_binary '\0'),(1258,'数据源配置更新','infra:data-source-config:update',3,3,1255,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-04-27 14:37:32','','2022-04-27 14:37:32',_binary '\0'),(1259,'数据源配置删除','infra:data-source-config:delete',3,4,1255,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-04-27 14:37:32','','2022-04-27 14:37:32',_binary '\0'),(1260,'数据源配置导出','infra:data-source-config:export',3,5,1255,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-04-27 14:37:32','','2022-04-27 14:37:32',_binary '\0'),(1261,'OAuth 2.0','',2,10,1,'oauth2','fa:dashcube',NULL,NULL,0,_binary '',_binary '',_binary '','1','2022-05-09 23:38:17','1','2024-02-29 01:12:08',_binary '\0'),(1263,'应用管理','',2,0,1261,'oauth2/application','fa:hdd-o','system/oauth2/client/index','SystemOAuth2Client',0,_binary '',_binary '',_binary '','','2022-05-10 16:26:33','1','2024-02-29 01:13:14',_binary '\0'),(1264,'客户端查询','system:oauth2-client:query',3,1,1263,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-05-10 16:26:33','1','2022-05-11 00:31:06',_binary '\0'),(1265,'客户端创建','system:oauth2-client:create',3,2,1263,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-05-10 16:26:33','1','2022-05-11 00:31:23',_binary '\0'),(1266,'客户端更新','system:oauth2-client:update',3,3,1263,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-05-10 16:26:33','1','2022-05-11 00:31:28',_binary '\0'),(1267,'客户端删除','system:oauth2-client:delete',3,4,1263,'','','',NULL,0,_binary '',_binary '',_binary '','','2022-05-10 16:26:33','1','2022-05-11 00:31:33',_binary '\0'),(2083,'地区管理','',2,14,1,'area','fa:map-marker','system/area/index','SystemArea',0,_binary '',_binary '',_binary '','1','2022-12-23 17:35:05','1','2024-02-29 08:50:28',_binary '\0'),(2130,'邮箱管理','',2,2,2739,'mail','fa-solid:mail-bulk',NULL,NULL,0,_binary '',_binary '',_binary '','1','2023-01-25 17:27:44','1','2024-04-22 23:56:08',_binary '\0'),(2131,'邮箱账号','',2,0,2130,'mail-account','fa:universal-access','system/mail/account/index','SystemMailAccount',0,_binary '',_binary '',_binary '','','2023-01-25 09:33:48','1','2024-02-29 08:48:16',_binary '\0'),(2132,'账号查询','system:mail-account:query',3,1,2131,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 09:33:48','','2023-01-25 09:33:48',_binary '\0'),(2133,'账号创建','system:mail-account:create',3,2,2131,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 09:33:48','','2023-01-25 09:33:48',_binary '\0'),(2134,'账号更新','system:mail-account:update',3,3,2131,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 09:33:48','','2023-01-25 09:33:48',_binary '\0'),(2135,'账号删除','system:mail-account:delete',3,4,2131,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 09:33:48','','2023-01-25 09:33:48',_binary '\0'),(2136,'邮件模版','',2,0,2130,'mail-template','fa:tag','system/mail/template/index','SystemMailTemplate',0,_binary '',_binary '',_binary '','','2023-01-25 12:05:31','1','2024-02-29 08:48:41',_binary '\0'),(2137,'模版查询','system:mail-template:query',3,1,2136,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 12:05:31','','2023-01-25 12:05:31',_binary '\0'),(2138,'模版创建','system:mail-template:create',3,2,2136,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 12:05:31','','2023-01-25 12:05:31',_binary '\0'),(2139,'模版更新','system:mail-template:update',3,3,2136,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 12:05:31','','2023-01-25 12:05:31',_binary '\0'),(2140,'模版删除','system:mail-template:delete',3,4,2136,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-25 12:05:31','','2023-01-25 12:05:31',_binary '\0'),(2141,'邮件记录','',2,0,2130,'mail-log','fa:edit','system/mail/log/index','SystemMailLog',0,_binary '',_binary '',_binary '','','2023-01-26 02:16:50','1','2024-02-29 08:48:51',_binary '\0'),(2142,'日志查询','system:mail-log:query',3,1,2141,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-26 02:16:50','','2023-01-26 02:16:50',_binary '\0'),(2143,'发送测试邮件','system:mail-template:send-mail',3,5,2136,'','','',NULL,0,_binary '',_binary '',_binary '','1','2023-01-26 23:29:15','1','2023-01-26 23:29:15',_binary '\0'),(2144,'站内信管理','',1,3,2739,'notify','ep:message-box',NULL,NULL,0,_binary '',_binary '',_binary '','1','2023-01-28 10:25:18','1','2024-04-22 23:56:12',_binary '\0'),(2145,'模板管理','',2,0,2144,'notify-template','fa:archive','system/notify/template/index','SystemNotifyTemplate',0,_binary '',_binary '',_binary '','','2023-01-28 02:26:42','1','2024-02-29 08:49:14',_binary '\0'),(2146,'站内信模板查询','system:notify-template:query',3,1,2145,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-28 02:26:42','','2023-01-28 02:26:42',_binary '\0'),(2147,'站内信模板创建','system:notify-template:create',3,2,2145,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-28 02:26:42','','2023-01-28 02:26:42',_binary '\0'),(2148,'站内信模板更新','system:notify-template:update',3,3,2145,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-28 02:26:42','','2023-01-28 02:26:42',_binary '\0'),(2149,'站内信模板删除','system:notify-template:delete',3,4,2145,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-28 02:26:42','','2023-01-28 02:26:42',_binary '\0'),(2150,'发送测试站内信','system:notify-template:send-notify',3,5,2145,'','','',NULL,0,_binary '',_binary '',_binary '','1','2023-01-28 10:54:43','1','2023-01-28 10:54:43',_binary '\0'),(2151,'消息记录','',2,0,2144,'notify-message','fa:edit','system/notify/message/index','SystemNotifyMessage',0,_binary '',_binary '',_binary '','','2023-01-28 04:28:22','1','2024-02-29 08:49:22',_binary '\0'),(2152,'站内信消息查询','system:notify-message:query',3,1,2151,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-01-28 04:28:22','','2023-01-28 04:28:22',_binary '\0'),(2447,'三方登录','',1,10,1,'social','fa:rocket','','',0,_binary '',_binary '',_binary '','1','2023-11-04 12:12:01','1','2024-02-29 01:14:05',_binary '\0'),(2448,'三方应用','',2,1,2447,'client','ep:set-up','system/social/client/index.vue','SocialClient',0,_binary '',_binary '',_binary '','1','2023-11-04 12:17:19','1','2024-05-04 19:09:54',_binary '\0'),(2449,'三方应用查询','system:social-client:query',3,1,2448,'','','','',0,_binary '',_binary '',_binary '','1','2023-11-04 12:43:12','1','2023-11-04 12:43:33',_binary '\0'),(2450,'三方应用创建','system:social-client:create',3,2,2448,'','','','',0,_binary '',_binary '',_binary '','1','2023-11-04 12:43:58','1','2023-11-04 12:43:58',_binary '\0'),(2451,'三方应用更新','system:social-client:update',3,3,2448,'','','','',0,_binary '',_binary '',_binary '','1','2023-11-04 12:44:27','1','2023-11-04 12:44:27',_binary '\0'),(2452,'三方应用删除','system:social-client:delete',3,4,2448,'','','','',0,_binary '',_binary '',_binary '','1','2023-11-04 12:44:43','1','2023-11-04 12:44:43',_binary '\0'),(2453,'三方用户','system:social-user:query',2,2,2447,'user','ep:avatar','system/social/user/index.vue','SocialUser',0,_binary '',_binary '',_binary '','1','2023-11-04 14:01:05','1','2023-11-04 14:01:05',_binary '\0'),(2472,'主子表（内嵌）','',2,12,1070,'demo03-inner','fa:power-off','infra/demo/demo03/inner/index','Demo03StudentInner',0,_binary '',_binary '',_binary '','','2023-11-13 04:39:51','1','2023-11-16 23:53:46',_binary '\0'),(2478,'单表（增删改查）','',2,1,1070,'demo01-contact','ep:bicycle','infra/demo/demo01/index','Demo01Contact',0,_binary '',_binary '',_binary '','','2023-11-15 14:42:30','1','2023-11-16 20:34:40',_binary '\0'),(2479,'示例联系人查询','infra:demo01-contact:query',3,1,2478,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-15 14:42:30','','2023-11-15 14:42:30',_binary '\0'),(2480,'示例联系人创建','infra:demo01-contact:create',3,2,2478,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-15 14:42:30','','2023-11-15 14:42:30',_binary '\0'),(2481,'示例联系人更新','infra:demo01-contact:update',3,3,2478,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-15 14:42:30','','2023-11-15 14:42:30',_binary '\0'),(2482,'示例联系人删除','infra:demo01-contact:delete',3,4,2478,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-15 14:42:30','','2023-11-15 14:42:30',_binary '\0'),(2483,'示例联系人导出','infra:demo01-contact:export',3,5,2478,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-15 14:42:30','','2023-11-15 14:42:30',_binary '\0'),(2484,'树表（增删改查）','',2,2,1070,'demo02-category','fa:tree','infra/demo/demo02/index','Demo02Category',0,_binary '',_binary '',_binary '','','2023-11-16 12:18:27','1','2023-11-16 20:35:01',_binary '\0'),(2485,'示例分类查询','infra:demo02-category:query',3,1,2484,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:18:27','','2023-11-16 12:18:27',_binary '\0'),(2486,'示例分类创建','infra:demo02-category:create',3,2,2484,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:18:27','','2023-11-16 12:18:27',_binary '\0'),(2487,'示例分类更新','infra:demo02-category:update',3,3,2484,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:18:27','','2023-11-16 12:18:27',_binary '\0'),(2488,'示例分类删除','infra:demo02-category:delete',3,4,2484,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:18:27','','2023-11-16 12:18:27',_binary '\0'),(2489,'示例分类导出','infra:demo02-category:export',3,5,2484,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:18:27','','2023-11-16 12:18:27',_binary '\0'),(2490,'主子表（标准）','',2,10,1070,'demo03-normal','fa:battery-3','infra/demo/demo03/normal/index','Demo03StudentNormal',0,_binary '',_binary '',_binary '','','2023-11-16 12:53:37','1','2023-11-16 23:10:03',_binary '\0'),(2491,'学生查询','infra:demo03-student:query',3,1,2490,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:53:37','','2023-11-16 12:53:37',_binary '\0'),(2492,'学生创建','infra:demo03-student:create',3,2,2490,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:53:37','','2023-11-16 12:53:37',_binary '\0'),(2493,'学生更新','infra:demo03-student:update',3,3,2490,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:53:37','','2023-11-16 12:53:37',_binary '\0'),(2494,'学生删除','infra:demo03-student:delete',3,4,2490,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:53:37','','2023-11-16 12:53:37',_binary '\0'),(2495,'学生导出','infra:demo03-student:export',3,5,2490,'','','',NULL,0,_binary '',_binary '',_binary '','','2023-11-16 12:53:37','','2023-11-16 12:53:37',_binary '\0'),(2497,'主子表（ERP）','',2,11,1070,'demo03-erp','ep:calendar','infra/demo/demo03/erp/index','Demo03StudentERP',0,_binary '',_binary '',_binary '','','2023-11-16 15:50:59','1','2023-11-17 13:19:56',_binary '\0'),(2525,'WebSocket','',2,5,2,'websocket','ep:connection','infra/webSocket/index','InfraWebSocket',0,_binary '',_binary '',_binary '','1','2023-11-23 19:41:55','1','2024-04-23 00:02:00',_binary '\0'),(2739,'消息中心','',1,7,1,'messages','ep:chat-dot-round','','',0,_binary '',_binary '',_binary '','1','2024-04-22 23:54:30','1','2024-04-23 09:36:35',_binary '\0'),(2740,'监控中心','',1,10,2,'monitors','ep:monitor','','',0,_binary '',_binary '',_binary '','1','2024-04-23 00:04:44','1','2024-04-23 00:04:44',_binary '\0');
/*!40000 ALTER TABLE `system_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_notice`
--

DROP TABLE IF EXISTS `system_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_notice` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
                                 `title` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
                                 `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
                                 `type` tinyint(4) NOT NULL COMMENT '公告类型（1通知 2公告）',
                                 `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
                                 `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                 `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_notice`
--

LOCK TABLES `system_notice` WRITE;
/*!40000 ALTER TABLE `system_notice` DISABLE KEYS */;
INSERT INTO `system_notice` VALUES (1,'芋道的公众','<p>新版本内容133</p>',1,0,'admin','2021-01-05 17:03:48','1','2022-05-04 21:00:20',_binary '\0',1),(2,'维护通知：2018-07-01 系统凌晨维护','<p><img src=\"http://test.yudao.iocoder.cn/b7cb3cf49b4b3258bf7309a09dd2f4e5.jpg\" alt=\"\" data-href=\"\" style=\"\"/>11112222<img src=\"http://test.yudao.iocoder.cn/fe44fc7bdb82ca421184b2eebbaee9e2148d4a1827479a4eb4521e11d2a062ba.png\" alt=\"image\" data-href=\"http://test.yudao.iocoder.cn/fe44fc7bdb82ca421184b2eebbaee9e2148d4a1827479a4eb4521e11d2a062ba.png\" style=\"\"/></p>',2,1,'admin','2021-01-05 17:03:48','1','2024-09-24 20:48:09',_binary '\0',1),(4,'我是测试标题','<p>哈哈哈哈123</p>',1,0,'110','2022-02-22 01:01:25','110','2022-02-22 01:01:46',_binary '\0',121);
/*!40000 ALTER TABLE `system_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_notify_message`
--

DROP TABLE IF EXISTS `system_notify_message`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_notify_message` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                                         `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                         `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                         `template_id` bigint(20) NOT NULL COMMENT '模版编号',
                                         `template_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
                                         `template_nickname` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版发送人名称',
                                         `template_content` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版内容',
                                         `template_type` int(11) NOT NULL COMMENT '模版类型',
                                         `template_params` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版参数',
                                         `read_status` bit(1) NOT NULL COMMENT '是否已读',
                                         `read_time` datetime DEFAULT NULL COMMENT '阅读时间',
                                         `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                         `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站内信消息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_notify_message`
--

LOCK TABLES `system_notify_message` WRITE;
/*!40000 ALTER TABLE `system_notify_message` DISABLE KEYS */;
INSERT INTO `system_notify_message` VALUES (2,1,2,1,'test','123','我是 1，我开始 2 了',1,'{\"name\":\"1\",\"what\":\"2\"}',_binary '','2023-02-10 00:47:04','1','2023-01-28 11:44:08','1','2023-02-10 00:47:04',_binary '\0',1),(3,1,2,1,'test','123','我是 1，我开始 2 了',1,'{\"name\":\"1\",\"what\":\"2\"}',_binary '','2023-02-10 00:47:04','1','2023-01-28 11:45:04','1','2023-02-10 00:47:04',_binary '\0',1),(4,103,2,2,'register','系统消息','你好，欢迎 哈哈 加入大家庭！',2,'{\"name\":\"哈哈\"}',_binary '\0',NULL,'1','2023-01-28 21:02:20','1','2023-01-28 21:02:20',_binary '\0',1),(5,1,2,1,'test','123','我是 芋艿，我开始 写代码 了',1,'{\"name\":\"芋艿\",\"what\":\"写代码\"}',_binary '','2023-02-10 00:47:04','1','2023-01-28 22:21:42','1','2023-02-10 00:47:04',_binary '\0',1),(6,1,2,1,'test','123','我是 芋艿，我开始 写代码 了',1,'{\"name\":\"芋艿\",\"what\":\"写代码\"}',_binary '','2023-01-29 10:52:06','1','2023-01-28 22:22:07','1','2023-01-29 10:52:06',_binary '\0',1),(7,1,2,1,'test','123','我是 2，我开始 3 了',1,'{\"name\":\"2\",\"what\":\"3\"}',_binary '','2023-01-29 10:52:06','1','2023-01-28 23:45:21','1','2023-01-29 10:52:06',_binary '\0',1),(8,1,2,2,'register','系统消息','你好，欢迎 123 加入大家庭！',2,'{\"name\":\"123\"}',_binary '','2023-01-29 10:52:06','1','2023-01-28 23:50:21','1','2023-01-29 10:52:06',_binary '\0',1),(9,247,1,4,'brokerage_withdraw_audit_approve','system','您在2023-09-28 08:35:46提现￥0.09元的申请已通过审核',2,'{\"reason\":null,\"createTime\":\"2023-09-28 08:35:46\",\"price\":\"0.09\"}',_binary '\0',NULL,'1','2023-09-28 16:36:22','1','2023-09-28 16:36:22',_binary '\0',1),(10,247,1,4,'brokerage_withdraw_audit_approve','system','您在2023-09-30 20:59:40提现￥1.00元的申请已通过审核',2,'{\"reason\":null,\"createTime\":\"2023-09-30 20:59:40\",\"price\":\"1.00\"}',_binary '\0',NULL,'1','2023-10-03 12:11:34','1','2023-10-03 12:11:34',_binary '\0',1);
/*!40000 ALTER TABLE `system_notify_message` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_notify_template`
--

DROP TABLE IF EXISTS `system_notify_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_notify_template` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
                                          `code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版编码',
                                          `nickname` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送人名称',
                                          `content` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模版内容',
                                          `type` tinyint(4) NOT NULL COMMENT '类型',
                                          `params` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数数组',
                                          `status` tinyint(4) NOT NULL COMMENT '状态',
                                          `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                          `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站内信模板表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_notify_template`
--

LOCK TABLES `system_notify_template` WRITE;
/*!40000 ALTER TABLE `system_notify_template` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_notify_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_oauth2_access_token`
--

DROP TABLE IF EXISTS `system_oauth2_access_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_oauth2_access_token` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                              `user_id` bigint(20) NOT NULL COMMENT '用户编号',
                                              `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                              `user_info` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户信息',
                                              `access_token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问令牌',
                                              `refresh_token` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '刷新令牌',
                                              `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                              `scopes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
                                              `expires_time` datetime NOT NULL COMMENT '过期时间',
                                              `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                              `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              KEY `idx_access_token` (`access_token`) USING BTREE,
                                              KEY `idx_refresh_token` (`refresh_token`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 访问令牌';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_oauth2_access_token`
--

LOCK TABLES `system_oauth2_access_token` WRITE;
/*!40000 ALTER TABLE `system_oauth2_access_token` DISABLE KEYS */;
INSERT INTO `system_oauth2_access_token` VALUES (10113,1,2,'{\"nickname\":\"芋道源码\",\"deptId\":\"103\"}','adfa16b7f4a344efbe306b725c0a5542','de1e5c2da0de4f448bf0a4a5640cb0e2','default',NULL,'2024-12-26 15:28:49',NULL,'2024-12-26 14:58:49','1','2024-12-26 15:13:23',_binary '',1),(10114,1,2,'{\"nickname\":\"芋道源码\",\"deptId\":\"103\"}','f1f41097b56d4763ae3ff510c8199859','ce23e3e363524c01817bf06506073071','default',NULL,'2024-12-26 15:43:29',NULL,'2024-12-26 15:13:29','1','2024-12-26 15:23:11',_binary '',1),(10115,1,2,'{\"nickname\":\"芋道源码\",\"deptId\":\"103\"}','d7178a98b8da45e1a6b6bde579707247','ce0964206cc844538a29af70abafd0e3','default',NULL,'2024-12-26 15:55:05',NULL,'2024-12-26 15:25:05',NULL,'2024-12-26 15:25:05',_binary '\0',1),(10116,1,2,'{\"nickname\":\"芋道源码\",\"deptId\":\"103\"}','3280e62f291349a79f80c16e06faba60','ef4187bfb43a4e2082cc5d4256aadff4','default',NULL,'2024-12-26 16:01:45',NULL,'2024-12-26 15:31:45','1','2024-12-26 15:44:31',_binary '',1),(10117,1,2,'{\"nickname\":\"芋道源码\",\"deptId\":\"103\"}','059d6cd3667140d39affd95db84fd08b','3aca7e6f9bf8424db92733353dada2aa','default',NULL,'2024-12-26 16:14:33',NULL,'2024-12-26 15:44:33',NULL,'2024-12-26 16:15:25',_binary '',1),(10118,1,2,'{\"nickname\":\"贾三胖\",\"deptId\":\"103\"}','d5fde39c800340d896667728a79b61f7','3aca7e6f9bf8424db92733353dada2aa','default',NULL,'2024-12-26 16:45:25',NULL,'2024-12-26 16:15:25','1','2024-12-26 16:35:54',_binary '',1),(10119,1,2,'{\"nickname\":\"贾三胖\",\"deptId\":\"103\"}','95044be8a050491e9277a08bd826fe7a','878e0f0abc484c2b9685474670f5083f','default',NULL,'2024-12-26 17:06:01',NULL,'2024-12-26 16:36:01','1','2024-12-26 16:39:40',_binary '',1),(10120,1,2,'{\"nickname\":\"贾三胖\",\"deptId\":\"103\"}','43ca50db76854b658c81f399d97e95ca','90f3f3d4a6394a1c8cb79a4e1125d495','default',NULL,'2024-12-26 17:10:21',NULL,'2024-12-26 16:40:21','1','2024-12-26 16:42:23',_binary '',1);
/*!40000 ALTER TABLE `system_oauth2_access_token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_oauth2_approve`
--

DROP TABLE IF EXISTS `system_oauth2_approve`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_oauth2_approve` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                         `user_id` bigint(20) NOT NULL COMMENT '用户编号',
                                         `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                         `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                         `scope` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '授权范围',
                                         `approved` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否接受',
                                         `expires_time` datetime NOT NULL COMMENT '过期时间',
                                         `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                         `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 批准表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_oauth2_approve`
--

LOCK TABLES `system_oauth2_approve` WRITE;
/*!40000 ALTER TABLE `system_oauth2_approve` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_oauth2_approve` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_oauth2_client`
--

DROP TABLE IF EXISTS `system_oauth2_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_oauth2_client` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                        `secret` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
                                        `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
                                        `logo` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用图标',
                                        `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用描述',
                                        `status` tinyint(4) NOT NULL COMMENT '状态',
                                        `access_token_validity_seconds` int(11) NOT NULL COMMENT '访问令牌的有效期',
                                        `refresh_token_validity_seconds` int(11) NOT NULL COMMENT '刷新令牌的有效期',
                                        `redirect_uris` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '可重定向的 URI 地址',
                                        `authorized_grant_types` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权类型',
                                        `scopes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
                                        `auto_approve_scopes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动通过的授权范围',
                                        `authorities` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限',
                                        `resource_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源',
                                        `additional_information` varchar(4096) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '附加信息',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 客户端表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_oauth2_client`
--

LOCK TABLES `system_oauth2_client` WRITE;
/*!40000 ALTER TABLE `system_oauth2_client` DISABLE KEYS */;
INSERT INTO `system_oauth2_client` VALUES (1,'default','admin123','芋道源码','http://test.yudao.iocoder.cn/a5e2e244368878a366b516805a4aabf1.png','我是描述',0,1800,2592000,'[\"https://www.iocoder.cn\",\"https://doc.iocoder.cn\"]','[\"password\",\"authorization_code\",\"implicit\",\"refresh_token\"]','[\"user.read\",\"user.write\"]','[]','[\"user.read\",\"user.write\"]','[]','{}','1','2022-05-11 21:47:12','1','2024-02-22 16:31:52',_binary '\0'),(40,'test','test2','biubiu','http://test.yudao.iocoder.cn/277a899d573723f1fcdfb57340f00379.png','啦啦啦啦',0,1800,43200,'[\"https://www.iocoder.cn\"]','[\"password\",\"authorization_code\",\"implicit\"]','[\"user_info\",\"projects\"]','[\"user_info\"]','[]','[]','{}','1','2022-05-12 00:28:20','1','2023-12-02 21:01:01',_binary '\0'),(41,'yudao-sso-demo-by-code','test','基于授权码模式，如何实现 SSO 单点登录？','http://test.yudao.iocoder.cn/fe4ed36596adad5120036ef61a6d0153654544d44af8dd4ad3ffe8f759933d6f.png',NULL,0,1800,43200,'[\"http://127.0.0.1:18080\"]','[\"authorization_code\",\"refresh_token\"]','[\"user.read\",\"user.write\"]','[]','[]','[]',NULL,'1','2022-09-29 13:28:31','1','2022-09-29 13:28:31',_binary '\0'),(42,'yudao-sso-demo-by-password','test','基于密码模式，如何实现 SSO 单点登录？','http://test.yudao.iocoder.cn/604bdc695e13b3b22745be704d1f2aa8ee05c5f26f9fead6d1ca49005afbc857.jpeg',NULL,0,1800,43200,'[\"http://127.0.0.1:18080\"]','[\"password\",\"refresh_token\"]','[\"user.read\",\"user.write\"]','[]','[]','[]',NULL,'1','2022-10-04 17:40:16','1','2022-10-04 20:31:21',_binary '\0');
/*!40000 ALTER TABLE `system_oauth2_client` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_oauth2_code`
--

DROP TABLE IF EXISTS `system_oauth2_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_oauth2_code` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                      `user_id` bigint(20) NOT NULL COMMENT '用户编号',
                                      `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                      `code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权码',
                                      `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                      `scopes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '授权范围',
                                      `expires_time` datetime NOT NULL COMMENT '过期时间',
                                      `redirect_uri` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '可重定向的 URI 地址',
                                      `state` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '状态',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=147 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 授权码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_oauth2_code`
--

LOCK TABLES `system_oauth2_code` WRITE;
/*!40000 ALTER TABLE `system_oauth2_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_oauth2_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_oauth2_refresh_token`
--

DROP TABLE IF EXISTS `system_oauth2_refresh_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_oauth2_refresh_token` (
                                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                               `user_id` bigint(20) NOT NULL COMMENT '用户编号',
                                               `refresh_token` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '刷新令牌',
                                               `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                               `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                               `scopes` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权范围',
                                               `expires_time` datetime NOT NULL COMMENT '过期时间',
                                               `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                               `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1659 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth2 刷新令牌';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_oauth2_refresh_token`
--

LOCK TABLES `system_oauth2_refresh_token` WRITE;
/*!40000 ALTER TABLE `system_oauth2_refresh_token` DISABLE KEYS */;
INSERT INTO `system_oauth2_refresh_token` VALUES (1652,1,'de1e5c2da0de4f448bf0a4a5640cb0e2',2,'default',NULL,'2025-01-25 14:58:49',NULL,'2024-12-26 14:58:49',NULL,'2024-12-26 07:13:22',_binary '',1),(1653,1,'ce23e3e363524c01817bf06506073071',2,'default',NULL,'2025-01-25 15:13:29',NULL,'2024-12-26 15:13:29',NULL,'2024-12-26 07:23:11',_binary '',1),(1654,1,'ce0964206cc844538a29af70abafd0e3',2,'default',NULL,'2025-01-25 15:25:05',NULL,'2024-12-26 15:25:05',NULL,'2024-12-26 15:25:05',_binary '\0',1),(1655,1,'ef4187bfb43a4e2082cc5d4256aadff4',2,'default',NULL,'2025-01-25 15:31:45',NULL,'2024-12-26 15:31:45',NULL,'2024-12-26 07:44:31',_binary '',1),(1656,1,'3aca7e6f9bf8424db92733353dada2aa',2,'default',NULL,'2025-01-25 15:44:33',NULL,'2024-12-26 15:44:33',NULL,'2024-12-26 08:35:53',_binary '',1),(1657,1,'878e0f0abc484c2b9685474670f5083f',2,'default',NULL,'2025-01-25 16:36:01',NULL,'2024-12-26 16:36:01',NULL,'2024-12-26 08:39:39',_binary '',1),(1658,1,'90f3f3d4a6394a1c8cb79a4e1125d495',2,'default',NULL,'2025-01-25 16:40:21',NULL,'2024-12-26 16:40:21',NULL,'2024-12-26 08:42:23',_binary '',1);
/*!40000 ALTER TABLE `system_oauth2_refresh_token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_operate_log`
--

DROP TABLE IF EXISTS `system_operate_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_operate_log` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
                                      `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
                                      `user_id` bigint(20) NOT NULL COMMENT '用户编号',
                                      `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户类型',
                                      `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作模块类型',
                                      `sub_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作名',
                                      `biz_id` bigint(20) NOT NULL COMMENT '操作数据模块编号',
                                      `action` varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作内容',
                                      `extra` varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拓展字段',
                                      `request_method` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求方法名',
                                      `request_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求地址',
                                      `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户 IP',
                                      `user_agent` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 UA',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9067 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志记录 V2 版本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_operate_log`
--

LOCK TABLES `system_operate_log` WRITE;
/*!40000 ALTER TABLE `system_operate_log` DISABLE KEYS */;
INSERT INTO `system_operate_log` VALUES (9063,'',1,2,'SYSTEM 用户','更新用户',1,'更新了用户【芋道源码】: 【用户邮箱】从【<EMAIL>】修改为【<EMAIL>】；【手机号码】从【18818260277】修改为【***********】；【用户昵称】从【芋道源码】修改为【贾三胖】','','PUT','/admin-api/system/user/update','0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 15:50:15',NULL,'2024-12-26 15:50:15',_binary '\0',1),(9064,'',1,2,'SYSTEM 用户','重置用户密码',131,'将用户【呵呵】的密码从【$2a$04$jyH9h6.gaw8mpOjPfHIpx.8as2Rzfcmdlj5rlJFwgCw4rsv/MTb2K】重置为【$2a$04$cdU6S6oVXGY3cAc.cGAOdu7pIKnCMG7TJJTfvkMq7/a8cax2bpqIq】','','PUT','/admin-api/system/user/update-password','0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 16:39:34',NULL,'2024-12-26 16:39:34',_binary '\0',1),(9065,'',1,2,'SYSTEM 用户','创建用户',140,'创建了用户【贾三胖】','','POST','/admin-api/system/user/create','0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 16:41:43',NULL,'2024-12-26 16:41:43',_binary '\0',1),(9066,'',1,2,'SYSTEM 用户','重置用户密码',140,'将用户【贾三胖】的密码从【$2a$04$hjj28oxbTrMUKZSD8jqP8O6WAUNIedHLqS.Z/RrJQQJSaRCLYyy4C】重置为【$2a$04$yCPOKuhpBPdzD1hCIbkLUuE7.6wM66NNntcQVdVOUu8AdAhGo4/G2】','','PUT','/admin-api/system/user/update-password','0:0:0:0:0:0:0:1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',NULL,'2024-12-26 16:42:04',NULL,'2024-12-26 16:42:04',_binary '\0',1);
/*!40000 ALTER TABLE `system_operate_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_post`
--

DROP TABLE IF EXISTS `system_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_post` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
                               `code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位编码',
                               `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位名称',
                               `sort` int(11) NOT NULL COMMENT '显示顺序',
                               `status` tinyint(4) NOT NULL COMMENT '状态（0正常 1停用）',
                               `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                               `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                               `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_post`
--

LOCK TABLES `system_post` WRITE;
/*!40000 ALTER TABLE `system_post` DISABLE KEYS */;
INSERT INTO `system_post` VALUES (1,'ceo','董事长',1,0,'','admin','2021-01-06 17:03:48','1','2023-02-11 15:19:04',_binary '\0',1),(2,'se','项目经理',2,0,'','admin','2021-01-05 17:03:48','1','2023-11-15 09:18:20',_binary '\0',1),(4,'user','普通员工',4,0,'111','admin','2021-01-05 17:03:48','1','2023-12-02 10:04:37',_binary '\0',1),(5,'HR','人力资源',5,0,'','1','2024-03-24 20:45:40','1','2024-03-24 20:45:40',_binary '\0',1);
/*!40000 ALTER TABLE `system_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_role`
--

DROP TABLE IF EXISTS `system_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_role` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
                               `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
                               `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色权限字符串',
                               `sort` int(11) NOT NULL COMMENT '显示顺序',
                               `data_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
                               `data_scope_dept_ids` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '数据范围(指定部门数组)',
                               `status` tinyint(4) NOT NULL COMMENT '角色状态（0正常 1停用）',
                               `type` tinyint(4) NOT NULL COMMENT '角色类型',
                               `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                               `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                               `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=154 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_role`
--

LOCK TABLES `system_role` WRITE;
/*!40000 ALTER TABLE `system_role` DISABLE KEYS */;
INSERT INTO `system_role` VALUES (1,'超级管理员','super_admin',1,1,'',0,1,'超级管理员','admin','2021-01-05 17:03:48','','2022-02-22 05:08:21',_binary '\0',1),(2,'普通角色','common',2,2,'',0,1,'普通角色','admin','2021-01-05 17:03:48','','2022-02-22 05:08:20',_binary '\0',1),(3,'CRM 管理员','crm_admin',2,1,'',0,1,'CRM 专属角色','1','2024-02-24 10:51:13','1','2024-02-24 02:51:32',_binary '\0',1),(101,'测试账号','test',0,1,'[]',0,2,'','','2021-01-06 13:49:35','1','2024-08-11 10:41:10',_binary '\0',1),(109,'租户管理员','tenant_admin',0,1,'',0,1,'系统自动生成','1','2022-02-22 00:56:14','1','2022-02-22 00:56:14',_binary '\0',121),(111,'租户管理员','tenant_admin',0,1,'',0,1,'系统自动生成','1','2022-03-07 21:37:58','1','2022-03-07 21:37:58',_binary '\0',122),(153,'某角色','tt',4,1,'',0,2,'','1','2024-08-17 14:09:35','1','2024-08-17 14:09:35',_binary '\0',1);
/*!40000 ALTER TABLE `system_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_role_menu`
--

DROP TABLE IF EXISTS `system_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_role_menu` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增编号',
                                    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                    `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
                                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5793 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_role_menu`
--

LOCK TABLES `system_role_menu` WRITE;
/*!40000 ALTER TABLE `system_role_menu` DISABLE KEYS */;
INSERT INTO `system_role_menu` VALUES (263,109,1,'1','2022-02-22 00:56:14','1','2022-02-22 00:56:14',_binary '\0',121),(434,2,1,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(454,2,1093,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(455,2,1094,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(460,2,1100,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(467,2,1107,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(477,2,100,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(478,2,101,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(479,2,102,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(481,2,103,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(483,2,104,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(485,2,105,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(488,2,107,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(490,2,108,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(492,2,109,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(498,2,1138,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(523,2,1224,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(524,2,1225,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(541,2,500,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(543,2,501,'1','2022-02-22 13:09:12','1','2022-02-22 13:09:12',_binary '\0',1),(675,2,2,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(689,2,1077,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(690,2,1078,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(692,2,1083,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(693,2,1084,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(699,2,1090,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(703,2,106,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(704,2,110,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(705,2,111,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(706,2,112,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(707,2,113,'1','2022-02-22 13:16:57','1','2022-02-22 13:16:57',_binary '\0',1),(1296,110,1,'110','2022-02-23 00:23:55','110','2022-02-23 00:23:55',_binary '\0',121),(1578,111,1,'1','2022-03-07 21:37:58','1','2022-03-07 21:37:58',_binary '\0',122),(1641,101,2,'1','2022-04-01 22:21:24','1','2022-04-01 22:21:24',_binary '\0',1),(1642,101,1031,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1643,101,1032,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1644,101,1033,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1645,101,1034,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1646,101,1035,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1647,101,1050,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1648,101,1051,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1649,101,1052,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1650,101,1053,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1651,101,1054,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1652,101,1056,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1653,101,1057,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1654,101,1058,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1655,101,1059,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1656,101,1060,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1657,101,1066,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1658,101,1067,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1659,101,1070,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1664,101,1075,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1666,101,1077,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1667,101,1078,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1668,101,1082,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1669,101,1083,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1670,101,1084,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1671,101,1085,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1672,101,1086,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1673,101,1087,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1674,101,1088,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1675,101,1089,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1679,101,1237,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1680,101,1238,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1681,101,1239,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1682,101,1240,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1683,101,1241,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1684,101,1242,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1685,101,1243,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1687,101,106,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1688,101,110,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1689,101,111,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1690,101,112,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1691,101,113,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1692,101,114,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1693,101,115,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1694,101,116,'1','2022-04-01 22:21:37','1','2022-04-01 22:21:37',_binary '\0',1),(1729,109,100,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1730,109,101,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1731,109,1063,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1732,109,1064,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1733,109,1001,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1734,109,1065,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1735,109,1002,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1736,109,1003,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1737,109,1004,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1738,109,1005,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1739,109,1006,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1740,109,1007,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1741,109,1008,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1742,109,1009,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1743,109,1010,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1744,109,1011,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1745,109,1012,'1','2022-09-21 22:08:51','1','2022-09-21 22:08:51',_binary '\0',121),(1746,111,100,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1747,111,101,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1748,111,1063,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1749,111,1064,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1750,111,1001,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1751,111,1065,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1752,111,1002,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1753,111,1003,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1754,111,1004,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1755,111,1005,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1756,111,1006,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1757,111,1007,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1758,111,1008,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1759,111,1009,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1760,111,1010,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1761,111,1011,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1762,111,1012,'1','2022-09-21 22:08:52','1','2022-09-21 22:08:52',_binary '\0',122),(1763,109,100,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1764,109,101,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1765,109,1063,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1766,109,1064,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1767,109,1001,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1768,109,1065,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1769,109,1002,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1770,109,1003,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1771,109,1004,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1772,109,1005,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1773,109,1006,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1774,109,1007,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1775,109,1008,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1776,109,1009,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1777,109,1010,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1778,109,1011,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1779,109,1012,'1','2022-09-21 22:08:53','1','2022-09-21 22:08:53',_binary '\0',121),(1780,111,100,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1781,111,101,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1782,111,1063,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1783,111,1064,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1784,111,1001,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1785,111,1065,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1786,111,1002,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1787,111,1003,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1788,111,1004,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1789,111,1005,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1790,111,1006,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1791,111,1007,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1792,111,1008,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1793,111,1009,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1794,111,1010,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1795,111,1011,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1796,111,1012,'1','2022-09-21 22:08:54','1','2022-09-21 22:08:54',_binary '\0',122),(1797,109,100,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1798,109,101,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1799,109,1063,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1800,109,1064,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1801,109,1001,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1802,109,1065,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1803,109,1002,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1804,109,1003,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1805,109,1004,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1806,109,1005,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1807,109,1006,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1808,109,1007,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1809,109,1008,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1810,109,1009,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1811,109,1010,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1812,109,1011,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1813,109,1012,'1','2022-09-21 22:08:55','1','2022-09-21 22:08:55',_binary '\0',121),(1814,111,100,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1815,111,101,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1816,111,1063,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1817,111,1064,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1818,111,1001,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1819,111,1065,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1820,111,1002,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1821,111,1003,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1822,111,1004,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1823,111,1005,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1824,111,1006,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1825,111,1007,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1826,111,1008,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1827,111,1009,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1828,111,1010,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1829,111,1011,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1830,111,1012,'1','2022-09-21 22:08:56','1','2022-09-21 22:08:56',_binary '\0',122),(1831,109,103,'1','2022-09-21 22:43:23','1','2022-09-21 22:43:23',_binary '\0',121),(1832,109,1017,'1','2022-09-21 22:43:23','1','2022-09-21 22:43:23',_binary '\0',121),(1833,109,1018,'1','2022-09-21 22:43:23','1','2022-09-21 22:43:23',_binary '\0',121),(1834,109,1019,'1','2022-09-21 22:43:23','1','2022-09-21 22:43:23',_binary '\0',121),(1835,109,1020,'1','2022-09-21 22:43:23','1','2022-09-21 22:43:23',_binary '\0',121),(1836,111,103,'1','2022-09-21 22:43:24','1','2022-09-21 22:43:24',_binary '\0',122),(1837,111,1017,'1','2022-09-21 22:43:24','1','2022-09-21 22:43:24',_binary '\0',122),(1838,111,1018,'1','2022-09-21 22:43:24','1','2022-09-21 22:43:24',_binary '\0',122),(1839,111,1019,'1','2022-09-21 22:43:24','1','2022-09-21 22:43:24',_binary '\0',122),(1840,111,1020,'1','2022-09-21 22:43:24','1','2022-09-21 22:43:24',_binary '\0',122),(1841,109,1036,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',121),(1842,109,1037,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',121),(1843,109,1038,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',121),(1844,109,1039,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',121),(1845,109,107,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',121),(1846,111,1036,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',122),(1847,111,1037,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',122),(1848,111,1038,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',122),(1849,111,1039,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',122),(1850,111,107,'1','2022-09-21 22:48:13','1','2022-09-21 22:48:13',_binary '\0',122),(1991,2,1024,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1992,2,1025,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1993,2,1026,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1994,2,1027,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1995,2,1028,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1996,2,1029,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1997,2,1030,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1998,2,1031,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(1999,2,1032,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2000,2,1033,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2001,2,1034,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2002,2,1035,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2003,2,1036,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2004,2,1037,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2005,2,1038,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2006,2,1039,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2007,2,1040,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2008,2,1042,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2009,2,1043,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2010,2,1045,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2011,2,1046,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2012,2,1048,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2013,2,1050,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2014,2,1051,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2015,2,1052,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2016,2,1053,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2017,2,1054,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2018,2,1056,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2019,2,1057,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2020,2,1058,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2021,2,2083,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2022,2,1059,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2023,2,1060,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2024,2,1063,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2025,2,1064,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2026,2,1065,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2027,2,1066,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2028,2,1067,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2029,2,1070,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2034,2,1075,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2036,2,1082,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2037,2,1085,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2038,2,1086,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2039,2,1087,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2040,2,1088,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2041,2,1089,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2042,2,1091,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2043,2,1092,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2044,2,1095,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2045,2,1096,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2046,2,1097,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2047,2,1098,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2048,2,1101,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2049,2,1102,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2050,2,1103,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2051,2,1104,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2052,2,1105,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2053,2,1106,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2054,2,1108,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2055,2,1109,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2072,2,114,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2073,2,1139,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2074,2,115,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2075,2,1140,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2076,2,116,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2077,2,1141,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2078,2,1142,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2079,2,1143,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2099,2,1226,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2100,2,1227,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2101,2,1228,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2102,2,1229,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2103,2,1237,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2104,2,1238,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2105,2,1239,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2106,2,1240,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2107,2,1241,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2108,2,1242,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2109,2,1243,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2117,2,1255,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2118,2,1256,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2119,2,1257,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2120,2,1258,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2121,2,1259,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2122,2,1260,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2123,2,1261,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2124,2,1263,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2125,2,1264,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2126,2,1265,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2127,2,1266,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2128,2,1267,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2129,2,1001,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2130,2,1002,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2131,2,1003,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2132,2,1004,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2133,2,1005,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2134,2,1006,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2135,2,1007,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2136,2,1008,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2137,2,1009,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2138,2,1010,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2139,2,1011,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2140,2,1012,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2141,2,1013,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2142,2,1014,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2143,2,1015,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2144,2,1016,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2145,2,1017,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2146,2,1018,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2147,2,1019,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2148,2,1020,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2149,2,1021,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2150,2,1022,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2151,2,1023,'1','2023-01-25 08:42:52','1','2023-01-25 08:42:52',_binary '\0',1),(2188,101,1024,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2189,101,1,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2190,101,1025,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2191,101,1026,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2192,101,1027,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2193,101,1028,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2194,101,1029,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2195,101,1030,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2196,101,1036,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2197,101,1037,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2198,101,1038,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2199,101,1039,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2200,101,1040,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2201,101,1042,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2202,101,1043,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2203,101,1045,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2204,101,1046,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2205,101,1048,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2206,101,2083,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2207,101,1063,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2208,101,1064,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2209,101,1065,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2210,101,1093,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2211,101,1094,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2212,101,1095,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2213,101,1096,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2214,101,1097,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2215,101,1098,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2216,101,1100,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2217,101,1101,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2218,101,1102,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2219,101,1103,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2220,101,1104,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2221,101,1105,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2222,101,1106,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2223,101,2130,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2224,101,1107,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2225,101,2131,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2226,101,1108,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2227,101,2132,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2228,101,1109,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2229,101,2133,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2230,101,2134,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2232,101,2135,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2234,101,2136,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2236,101,2137,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2238,101,2138,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2240,101,2139,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2242,101,2140,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2243,101,2141,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2244,101,2142,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2245,101,2143,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2246,101,2144,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2247,101,2145,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2248,101,2146,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2249,101,2147,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2250,101,100,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2251,101,2148,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2252,101,101,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2253,101,2149,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2254,101,102,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2255,101,2150,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2256,101,103,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2257,101,2151,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2258,101,104,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2259,101,2152,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2260,101,105,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2261,101,107,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2262,101,108,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2263,101,109,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2264,101,1138,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2265,101,1139,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2266,101,1140,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2267,101,1141,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2268,101,1142,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2269,101,1143,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2270,101,1224,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2271,101,1225,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2272,101,1226,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2273,101,1227,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2274,101,1228,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2275,101,1229,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2282,101,1261,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2283,101,1263,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2284,101,1264,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2285,101,1265,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2286,101,1266,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2287,101,1267,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2288,101,1001,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2289,101,1002,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2290,101,1003,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2291,101,1004,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2292,101,1005,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2293,101,1006,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2294,101,1007,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2295,101,1008,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2296,101,1009,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2297,101,1010,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2298,101,1011,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2299,101,1012,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2300,101,500,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2301,101,1013,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2302,101,501,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2303,101,1014,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2304,101,1015,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2305,101,1016,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2306,101,1017,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2307,101,1018,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2308,101,1019,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2309,101,1020,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2310,101,1021,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2311,101,1022,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2312,101,1023,'1','2023-02-09 23:49:46','1','2023-02-09 23:49:46',_binary '\0',1),(2929,109,1224,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2930,109,1225,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2931,109,1226,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2932,109,1227,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2933,109,1228,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2934,109,1229,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2935,109,1138,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2936,109,1139,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2937,109,1140,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2938,109,1141,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2939,109,1142,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2940,109,1143,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',121),(2941,111,1224,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2942,111,1225,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2943,111,1226,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2944,111,1227,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2945,111,1228,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2946,111,1229,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2947,111,1138,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2948,111,1139,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2949,111,1140,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2950,111,1141,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2951,111,1142,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2952,111,1143,'1','2023-12-02 23:19:40','1','2023-12-02 23:19:40',_binary '\0',122),(2993,109,2,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(2994,109,1031,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(2995,109,1032,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(2996,109,1033,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(2997,109,1034,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(2998,109,1035,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(2999,109,1050,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3000,109,1051,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3001,109,1052,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3002,109,1053,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3003,109,1054,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3004,109,1056,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3005,109,1057,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3006,109,1058,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3007,109,1059,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3008,109,1060,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3009,109,1066,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3010,109,1067,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3011,109,1070,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3012,109,1075,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3014,109,1077,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3015,109,1078,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3016,109,1082,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3017,109,1083,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3018,109,1084,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3019,109,1085,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3020,109,1086,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3021,109,1087,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3022,109,1088,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3023,109,1089,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3024,109,1090,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3025,109,1091,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3026,109,1092,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3027,109,106,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3028,109,110,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3029,109,111,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3030,109,112,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3031,109,113,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3032,109,114,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3033,109,115,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3034,109,116,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3035,109,2472,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3036,109,2478,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3037,109,2479,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3038,109,2480,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3039,109,2481,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3040,109,2482,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3041,109,2483,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3042,109,2484,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3043,109,2485,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3044,109,2486,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3045,109,2487,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3046,109,2488,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3047,109,2489,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3048,109,2490,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3049,109,2491,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3050,109,2492,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3051,109,2493,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3052,109,2494,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3053,109,2495,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3054,109,2497,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3055,109,1237,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3056,109,1238,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3057,109,1239,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3058,109,1240,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3059,109,1241,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3060,109,1242,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3061,109,1243,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3062,109,2525,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3063,109,1255,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3064,109,1256,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3065,109,1257,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3066,109,1258,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3067,109,1259,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3068,109,1260,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',121),(3069,111,2,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3070,111,1031,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3071,111,1032,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3072,111,1033,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3073,111,1034,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3074,111,1035,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3075,111,1050,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3076,111,1051,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3077,111,1052,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3078,111,1053,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3079,111,1054,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3080,111,1056,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3081,111,1057,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3082,111,1058,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3083,111,1059,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3084,111,1060,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3085,111,1066,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3086,111,1067,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3087,111,1070,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3088,111,1075,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3090,111,1077,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3091,111,1078,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3092,111,1082,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3093,111,1083,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3094,111,1084,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3095,111,1085,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3096,111,1086,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3097,111,1087,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3098,111,1088,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3099,111,1089,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3100,111,1090,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3101,111,1091,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3102,111,1092,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3103,111,106,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3104,111,110,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3105,111,111,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3106,111,112,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3107,111,113,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3108,111,114,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3109,111,115,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3110,111,116,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3111,111,2472,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3112,111,2478,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3113,111,2479,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3114,111,2480,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3115,111,2481,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3116,111,2482,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3117,111,2483,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3118,111,2484,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3119,111,2485,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3120,111,2486,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3121,111,2487,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3122,111,2488,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3123,111,2489,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3124,111,2490,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3125,111,2491,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3126,111,2492,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3127,111,2493,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3128,111,2494,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3129,111,2495,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3130,111,2497,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3131,111,1237,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3132,111,1238,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3133,111,1239,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3134,111,1240,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3135,111,1241,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3136,111,1242,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3137,111,1243,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3138,111,2525,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3139,111,1255,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3140,111,1256,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3141,111,1257,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3142,111,1258,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3143,111,1259,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3144,111,1260,'1','2023-12-02 23:41:02','1','2023-12-02 23:41:02',_binary '\0',122),(3221,109,102,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',121),(3222,109,1013,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',121),(3223,109,1014,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',121),(3224,109,1015,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',121),(3225,109,1016,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',121),(3226,111,102,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',122),(3227,111,1013,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',122),(3228,111,1014,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',122),(3229,111,1015,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',122),(3230,111,1016,'1','2023-12-30 11:42:36','1','2023-12-30 11:42:36',_binary '\0',122),(5777,101,2739,'1','2024-04-30 09:38:37','1','2024-04-30 09:38:37',_binary '\0',1),(5778,101,2740,'1','2024-04-30 09:38:37','1','2024-04-30 09:38:37',_binary '\0',1),(5779,2,2739,'1','2024-07-07 20:39:38','1','2024-07-07 20:39:38',_binary '\0',1),(5780,2,2740,'1','2024-07-07 20:39:38','1','2024-07-07 20:39:38',_binary '\0',1),(5789,109,2739,'1','2024-07-13 22:37:24','1','2024-07-13 22:37:24',_binary '\0',121),(5790,109,2740,'1','2024-07-13 22:37:24','1','2024-07-13 22:37:24',_binary '\0',121),(5791,111,2739,'1','2024-07-13 22:37:24','1','2024-07-13 22:37:24',_binary '\0',122),(5792,111,2740,'1','2024-07-13 22:37:24','1','2024-07-13 22:37:24',_binary '\0',122);
/*!40000 ALTER TABLE `system_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_sms_channel`
--

DROP TABLE IF EXISTS `system_sms_channel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_sms_channel` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                      `signature` varchar(12) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信签名',
                                      `code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道编码',
                                      `status` tinyint(4) NOT NULL COMMENT '开启状态',
                                      `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                      `api_key` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信 API 的账号',
                                      `api_secret` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 的秘钥',
                                      `callback_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信发送回调 URL',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信渠道';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_sms_channel`
--

LOCK TABLES `system_sms_channel` WRITE;
/*!40000 ALTER TABLE `system_sms_channel` DISABLE KEYS */;
INSERT INTO `system_sms_channel` VALUES (2,'Ballcat','ALIYUN',0,'你要改哦，只有我可以用！！！！','LTAI5tCnKso2uG3kJ5gRav88','******************************',NULL,'','2021-03-31 11:53:10','1','2024-08-04 08:53:26',_binary '\0'),(4,'测试渠道','DEBUG_DING_TALK',0,'123','696b5d8ead48071237e4aa5861ff08dbadb2b4ded1c688a7b7c9afc615579859','SEC5c4e5ff888bc8a9923ae47f59e7ccd30af1f14d93c55b4e2c9cb094e35aeed67',NULL,'1','2021-04-13 00:23:14','1','2022-03-27 20:29:49',_binary '\0'),(7,'mock腾讯云','TENCENT',0,'','1 2','2 3','','1','2024-09-30 08:53:45','1','2024-09-30 08:55:01',_binary '\0');
/*!40000 ALTER TABLE `system_sms_channel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_sms_code`
--

DROP TABLE IF EXISTS `system_sms_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_sms_code` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                   `mobile` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
                                   `code` varchar(6) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '验证码',
                                   `create_ip` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建 IP',
                                   `scene` tinyint(4) NOT NULL COMMENT '发送场景',
                                   `today_index` tinyint(4) NOT NULL COMMENT '今日发送的第几条',
                                   `used` tinyint(4) NOT NULL COMMENT '是否使用',
                                   `used_time` datetime DEFAULT NULL COMMENT '使用时间',
                                   `used_ip` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用 IP',
                                   `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                   `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `idx_mobile` (`mobile`) USING BTREE COMMENT '手机号'
) ENGINE=InnoDB AUTO_INCREMENT=639 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='手机验证码';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_sms_code`
--

LOCK TABLES `system_sms_code` WRITE;
/*!40000 ALTER TABLE `system_sms_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_sms_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_sms_log`
--

DROP TABLE IF EXISTS `system_sms_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_sms_log` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                  `channel_id` bigint(20) NOT NULL COMMENT '短信渠道编号',
                                  `channel_code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信渠道编码',
                                  `template_id` bigint(20) NOT NULL COMMENT '模板编号',
                                  `template_code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
                                  `template_type` tinyint(4) NOT NULL COMMENT '短信类型',
                                  `template_content` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信内容',
                                  `template_params` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信参数',
                                  `api_template_id` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信 API 的模板编号',
                                  `mobile` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
                                  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
                                  `user_type` tinyint(4) DEFAULT NULL COMMENT '用户类型',
                                  `send_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发送状态',
                                  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                  `api_send_code` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送结果的编码',
                                  `api_send_msg` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送失败的提示',
                                  `api_request_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送返回的唯一请求 ID',
                                  `api_serial_no` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短信 API 发送返回的序号',
                                  `receive_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '接收状态',
                                  `receive_time` datetime DEFAULT NULL COMMENT '接收时间',
                                  `api_receive_code` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API 接收结果的编码',
                                  `api_receive_msg` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API 接收结果的说明',
                                  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1147 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_sms_log`
--

LOCK TABLES `system_sms_log` WRITE;
/*!40000 ALTER TABLE `system_sms_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_sms_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_sms_template`
--

DROP TABLE IF EXISTS `system_sms_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_sms_template` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                       `type` tinyint(4) NOT NULL COMMENT '模板类型',
                                       `status` tinyint(4) NOT NULL COMMENT '开启状态',
                                       `code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板编码',
                                       `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
                                       `content` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板内容',
                                       `params` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数数组',
                                       `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                       `api_template_id` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信 API 的模板编号',
                                       `channel_id` bigint(20) NOT NULL COMMENT '短信渠道编号',
                                       `channel_code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短信渠道编码',
                                       `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_sms_template`
--

LOCK TABLES `system_sms_template` WRITE;
/*!40000 ALTER TABLE `system_sms_template` DISABLE KEYS */;
INSERT INTO `system_sms_template` VALUES (2,1,0,'test_01','测试验证码短信','正在进行登录操作{operation}，您的验证码是{code}','[\"operation\",\"code\"]','测试备注','4383920',4,'DEBUG_DING_TALK','','2021-03-31 10:49:38','1','2024-08-18 11:57:18',_binary '\0'),(3,1,0,'test_02','公告通知','您的验证码{code}，该验证码5分钟内有效，请勿泄漏于他人！','[\"code\"]',NULL,'SMS_207945135',2,'ALIYUN','','2021-03-31 11:56:30','1','2021-04-10 01:22:02',_binary '\0'),(6,3,0,'test-01','测试模板','哈哈哈 {name}','[\"name\"]','f哈哈哈','4383920',4,'DEBUG_DING_TALK','1','2021-04-10 01:07:21','1','2024-08-18 11:57:07',_binary '\0'),(7,3,0,'test-04','测试下','老鸡{name}，牛逼{code}','[\"name\",\"code\"]','哈哈哈哈','suibian',7,'DEBUG_DING_TALK','1','2021-04-13 00:29:53','1','2024-09-30 00:56:24',_binary '\0'),(8,1,0,'user-sms-login','前台用户短信登录','您的验证码是{code}','[\"code\"]',NULL,'4372216',4,'DEBUG_DING_TALK','1','2021-10-11 08:10:00','1','2024-08-18 11:57:06',_binary '\0'),(9,2,0,'bpm_task_assigned','【工作流】任务被分配','您收到了一条新的待办任务：{processInstanceName}-{taskName}，申请人：{startUserNickname}，处理链接：{detailUrl}','[\"processInstanceName\",\"taskName\",\"startUserNickname\",\"detailUrl\"]',NULL,'suibian',4,'DEBUG_DING_TALK','1','2022-01-21 22:31:19','1','2022-01-22 00:03:36',_binary '\0'),(10,2,0,'bpm_process_instance_reject','【工作流】流程被不通过','您的流程被审批不通过：{processInstanceName}，原因：{reason}，查看链接：{detailUrl}','[\"processInstanceName\",\"reason\",\"detailUrl\"]',NULL,'suibian',4,'DEBUG_DING_TALK','1','2022-01-22 00:03:31','1','2022-05-01 12:33:14',_binary '\0'),(11,2,0,'bpm_process_instance_approve','【工作流】流程被通过','您的流程被审批通过：{processInstanceName}，查看链接：{detailUrl}','[\"processInstanceName\",\"detailUrl\"]',NULL,'suibian',4,'DEBUG_DING_TALK','1','2022-01-22 00:04:31','1','2022-03-27 20:32:21',_binary '\0'),(12,2,0,'demo','演示模板','我就是测试一下下','[]',NULL,'biubiubiu',4,'DEBUG_DING_TALK','1','2022-04-10 23:22:49','1','2024-08-18 11:57:04',_binary '\0'),(14,1,0,'user-update-mobile','会员用户 - 修改手机','您的验证码{code}，该验证码 5 分钟内有效，请勿泄漏于他人！','[\"code\"]','','null',4,'DEBUG_DING_TALK','1','2023-08-19 18:58:01','1','2023-08-19 11:34:04',_binary '\0'),(15,1,0,'user-update-password','会员用户 - 修改密码','您的验证码{code}，该验证码 5 分钟内有效，请勿泄漏于他人！','[\"code\"]','','null',4,'DEBUG_DING_TALK','1','2023-08-19 18:58:01','1','2023-08-19 11:34:18',_binary '\0'),(16,1,0,'user-reset-password','会员用户 - 重置密码','您的验证码{code}，该验证码 5 分钟内有效，请勿泄漏于他人！','[\"code\"]','','null',4,'DEBUG_DING_TALK','1','2023-08-19 18:58:01','1','2023-12-02 22:35:27',_binary '\0'),(17,2,0,'bpm_task_timeout','【工作流】任务审批超时','您收到了一条超时的待办任务：{processInstanceName}-{taskName}，处理链接：{detailUrl}','[\"processInstanceName\",\"taskName\",\"detailUrl\"]','','X',4,'DEBUG_DING_TALK','1','2024-08-16 21:59:15','1','2024-08-16 21:59:34',_binary '\0');
/*!40000 ALTER TABLE `system_sms_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_social_client`
--

DROP TABLE IF EXISTS `system_social_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_social_client` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
                                        `social_type` tinyint(4) NOT NULL COMMENT '社交平台的类型',
                                        `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                        `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                        `client_secret` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
                                        `agent_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理编号',
                                        `status` tinyint(4) NOT NULL COMMENT '状态',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交客户端表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_social_client`
--

LOCK TABLES `system_social_client` WRITE;
/*!40000 ALTER TABLE `system_social_client` DISABLE KEYS */;
INSERT INTO `system_social_client` VALUES (1,'钉钉',20,2,'dingvrnreaje3yqvzhxg','i8E6iZyDvZj51JIb0tYsYfVQYOks9Cq1lgryEjFRqC79P3iJcrxEwT6Qk2QvLrLI',NULL,0,'','2023-10-18 11:21:18','1','2023-12-20 21:28:26',_binary '',1),(2,'钉钉（王土豆）',20,2,'dingtsu9hpepjkbmthhw','FP_bnSq_HAHKCSncmJjw5hxhnzs6vaVDSZZn3egj6rdqTQ_hu5tQVJyLMpgCakdP',NULL,0,'','2023-10-18 11:21:18','','2023-12-20 21:28:26',_binary '',121),(3,'微信公众号',31,1,'wx5b23ba7a5589ecbb','2a7b3b20c537e52e74afd395eb85f61f',NULL,0,'','2023-10-18 16:07:46','1','2023-12-20 21:28:23',_binary '',1),(43,'微信小程序',34,1,'wx63c280fe3248a3e7','6f270509224a7ae1296bbf1c8cb97aed',NULL,0,'','2023-10-19 13:37:41','1','2023-12-20 21:28:25',_binary '',1);
/*!40000 ALTER TABLE `system_social_client` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_social_user`
--

DROP TABLE IF EXISTS `system_social_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_social_user` (
                                      `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键(自增策略)',
                                      `type` tinyint(4) NOT NULL COMMENT '社交平台的类型',
                                      `openid` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '社交 openid',
                                      `token` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '社交 token',
                                      `raw_token_info` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始 Token 数据，一般是 JSON 格式',
                                      `nickname` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
                                      `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户头像',
                                      `raw_user_info` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始用户数据，一般是 JSON 格式',
                                      `code` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '最后一次的认证 code',
                                      `state` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一次的认证 state',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_social_user`
--

LOCK TABLES `system_social_user` WRITE;
/*!40000 ALTER TABLE `system_social_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_social_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_social_user_bind`
--

DROP TABLE IF EXISTS `system_social_user_bind`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_social_user_bind` (
                                           `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键(自增策略)',
                                           `user_id` bigint(20) NOT NULL COMMENT '用户编号',
                                           `user_type` tinyint(4) NOT NULL COMMENT '用户类型',
                                           `social_type` tinyint(4) NOT NULL COMMENT '社交平台的类型',
                                           `social_user_id` bigint(20) NOT NULL COMMENT '社交用户的编号',
                                           `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                           `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交绑定表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_social_user_bind`
--

LOCK TABLES `system_social_user_bind` WRITE;
/*!40000 ALTER TABLE `system_social_user_bind` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_social_user_bind` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_tenant`
--

DROP TABLE IF EXISTS `system_tenant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_tenant` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '租户编号',
                                 `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名',
                                 `contact_user_id` bigint(20) DEFAULT NULL COMMENT '联系人的用户编号',
                                 `contact_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人',
                                 `contact_mobile` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系手机',
                                 `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
                                 `website` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '绑定域名',
                                 `package_id` bigint(20) NOT NULL COMMENT '租户套餐编号',
                                 `expire_time` datetime NOT NULL COMMENT '过期时间',
                                 `account_count` int(11) NOT NULL COMMENT '账号数量',
                                 `creator` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建者',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=162 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_tenant`
--

LOCK TABLES `system_tenant` WRITE;
/*!40000 ALTER TABLE `system_tenant` DISABLE KEYS */;
INSERT INTO `system_tenant` VALUES (1,'三胖科技',NULL,'三胖','***********',0,'www.iocoder.cn',0,'2099-02-19 17:14:16',9999,'1','2021-01-05 17:03:47','1','2024-12-26 07:48:54',_binary '\0'),(122,'测试租户',113,'芋道','15601691300',0,'test.iocoder.cn',111,'2099-02-19 17:14:16',50,'1','2022-03-07 21:37:58','1','2024-12-26 08:43:05',_binary '\0');
/*!40000 ALTER TABLE `system_tenant` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_tenant_package`
--

DROP TABLE IF EXISTS `system_tenant_package`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_tenant_package` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '套餐编号',
                                         `name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套餐名',
                                         `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
                                         `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
                                         `menu_ids` varchar(4096) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的菜单编号',
                                         `creator` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户套餐表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_tenant_package`
--

LOCK TABLES `system_tenant_package` WRITE;
/*!40000 ALTER TABLE `system_tenant_package` DISABLE KEYS */;
INSERT INTO `system_tenant_package` VALUES (111,'普通套餐',0,'小功能','[1,2,5,1031,1032,1033,1034,1035,1036,1037,1038,1039,1050,1051,1052,1053,1054,1056,1057,1058,1059,1060,1063,1064,1065,1066,1067,1070,1075,1077,1078,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1118,1119,1120,100,101,102,103,106,107,110,111,112,113,1138,114,1139,115,1140,116,1141,1142,1143,2713,2714,2715,2716,2717,2718,2720,1185,2721,1186,2722,1187,2723,1188,2724,1189,2725,1190,2726,1191,2727,2472,1192,2728,1193,2729,1194,2730,1195,2731,1196,2732,1197,2733,2478,1198,2734,2479,1199,2735,2480,1200,2481,1201,2482,1202,2483,2739,2484,2740,2485,2486,2487,1207,2488,1208,2489,1209,2490,1210,2491,1211,2492,1212,2493,1213,2494,2495,1215,1216,2497,1217,1218,1219,1220,1221,1222,1224,1225,1226,1227,1228,1229,1237,1238,1239,1240,1241,1242,1243,2525,1255,1256,1001,1257,1002,1258,1003,1259,1004,1260,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020]','1','2022-02-22 00:54:00','1','2024-07-13 22:37:24',_binary '\0');
/*!40000 ALTER TABLE `system_tenant_package` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_user_post`
--

DROP TABLE IF EXISTS `system_user_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_user_post` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                    `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户ID',
                                    `post_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '岗位ID',
                                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=127 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户岗位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_user_post`
--

LOCK TABLES `system_user_post` WRITE;
/*!40000 ALTER TABLE `system_user_post` DISABLE KEYS */;
INSERT INTO `system_user_post` VALUES (112,1,1,'admin','2022-05-02 07:25:24','admin','2022-05-02 07:25:24',_binary '\0',1),(113,100,1,'admin','2022-05-02 07:25:24','admin','2022-05-02 07:25:24',_binary '\0',1),(115,104,1,'1','2022-05-16 19:36:28','1','2022-05-16 19:36:28',_binary '\0',1),(116,117,2,'1','2022-07-09 17:40:26','1','2022-07-09 17:40:26',_binary '\0',1),(117,118,1,'1','2022-07-09 17:44:44','1','2022-07-09 17:44:44',_binary '\0',1),(119,114,5,'1','2024-03-24 20:45:51','1','2024-03-24 20:45:51',_binary '\0',1),(123,115,1,'1','2024-04-04 09:37:14','1','2024-04-04 09:37:14',_binary '\0',1),(124,115,2,'1','2024-04-04 09:37:14','1','2024-04-04 09:37:14',_binary '\0',1),(125,1,2,'1','2024-07-13 22:31:39','1','2024-07-13 22:31:39',_binary '\0',1),(126,140,1,'1','2024-12-26 16:41:43','1','2024-12-26 16:41:43',_binary '\0',1);
/*!40000 ALTER TABLE `system_user_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_user_role`
--

DROP TABLE IF EXISTS `system_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_user_role` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增编号',
                                    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                    `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
                                    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_user_role`
--

LOCK TABLES `system_user_role` WRITE;
/*!40000 ALTER TABLE `system_user_role` DISABLE KEYS */;
INSERT INTO `system_user_role` VALUES (1,1,1,'','2022-01-11 13:19:45','','2022-05-12 12:35:17',_binary '\0',1),(2,2,2,'','2022-01-11 13:19:45','','2022-05-12 12:35:13',_binary '\0',1),(4,100,101,'','2022-01-11 13:19:45','','2022-05-12 12:35:13',_binary '\0',1),(5,100,1,'','2022-01-11 13:19:45','','2022-05-12 12:35:12',_binary '\0',1),(6,100,2,'','2022-01-11 13:19:45','','2022-05-12 12:35:11',_binary '\0',1),(10,103,1,'1','2022-01-11 13:19:45','1','2022-01-11 13:19:45',_binary '\0',1),(14,110,109,'1','2022-02-22 00:56:14','1','2022-02-22 00:56:14',_binary '\0',121),(15,111,110,'110','2022-02-23 13:14:38','110','2022-02-23 13:14:38',_binary '\0',121),(16,113,111,'1','2022-03-07 21:37:58','1','2022-03-07 21:37:58',_binary '\0',122),(18,1,2,'1','2022-05-12 20:39:29','1','2022-05-12 20:39:29',_binary '\0',1),(20,104,101,'1','2022-05-28 15:43:57','1','2022-05-28 15:43:57',_binary '\0',1),(22,115,2,'1','2022-07-21 22:08:30','1','2022-07-21 22:08:30',_binary '\0',1),(35,112,1,'1','2024-03-15 20:00:24','1','2024-03-15 20:00:24',_binary '\0',1),(36,118,1,'1','2024-03-17 09:12:08','1','2024-03-17 09:12:08',_binary '\0',1),(38,114,101,'1','2024-03-24 22:23:03','1','2024-03-24 22:23:03',_binary '\0',1),(46,117,1,'1','2024-10-02 10:16:11','1','2024-10-02 10:16:11',_binary '\0',1),(47,140,1,'1','2024-12-26 16:42:17','1','2024-12-26 16:42:17',_binary '\0',1);
/*!40000 ALTER TABLE `system_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_users`
--

DROP TABLE IF EXISTS `system_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_users` (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
                                `username` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
                                `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
                                `nickname` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
                                `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
                                `post_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '岗位编号数组',
                                `email` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户邮箱',
                                `mobile` varchar(11) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '手机号码',
                                `sex` tinyint(4) DEFAULT '0' COMMENT '用户性别',
                                `avatar` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像地址',
                                `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
                                `login_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
                                `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
                                `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_users`
--

LOCK TABLES `system_users` WRITE;
/*!40000 ALTER TABLE `system_users` DISABLE KEYS */;
INSERT INTO `system_users` VALUES (1,'admin','$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm','贾三胖','管理员',103,'[1,2]','<EMAIL>','***********',2,'http://test.yudao.iocoder.cn/bf2002b38950c904243be7c825d3f82e29f25a44526583c3fde2ebdff3a87f75.png',0,'0:0:0:0:0:0:0:1','2024-12-26 16:40:21','admin','2021-01-05 17:03:47',NULL,'2024-12-26 16:40:21',_binary '\0',1),(100,'yudao','$2a$10$11U48RhyJ5pSBYWSn12AD./ld671.ycSzJHbyrtpeoMeYiw31eo8a','芋道','不要吓我',104,'[1]','<EMAIL>','15601691300',1,'',0,'127.0.0.1','2022-07-09 23:03:33','','2021-01-07 09:07:17','1','2024-09-22 14:55:06',_binary '\0',1),(103,'yuanma','$2a$04$fUBSmjKCPYAUmnMzOb6qE.eZCGPhHi1JmAKclODbfS/O7fHOl2bH6','源码',NULL,106,NULL,'<EMAIL>','15601701300',0,'',0,'0:0:0:0:0:0:0:1','2024-08-11 17:48:12','','2021-01-13 23:50:35',NULL,'2024-08-11 17:48:12',_binary '\0',1),(104,'test','$2a$04$jDFLttgfik0QqJKAbfhMa.2A9xXoZmAIxakdFJUzkX.MgBKT6ddo6','测试号',NULL,107,'[1,2]','<EMAIL>','15601691200',1,'',0,'0:0:0:0:0:0:0:1','2024-09-17 15:05:43','','2021-01-21 02:13:53',NULL,'2024-09-17 15:05:43',_binary '\0',1),(107,'admin107','$2a$10$dYOOBKMO93v/.ReCqzyFg.o67Tqk.bbc2bhrpyBGkIw9aypCtr2pm','芋艿',NULL,NULL,NULL,'','15601691300',0,'',0,'',NULL,'1','2022-02-20 22:59:33','1','2022-02-27 08:26:51',_binary '\0',118),(108,'admin108','$2a$10$y6mfvKoNYL1GXWak8nYwVOH.kCWqjactkzdoIDgiKl93WN3Ejg.Lu','芋艿',NULL,NULL,NULL,'','15601691300',0,'',0,'',NULL,'1','2022-02-20 23:00:50','1','2022-02-27 08:26:53',_binary '\0',119),(109,'admin109','$2a$10$JAqvH0tEc0I7dfDVBI7zyuB4E3j.uH6daIjV53.vUS6PknFkDJkuK','芋艿',NULL,NULL,NULL,'','15601691300',0,'',0,'',NULL,'1','2022-02-20 23:11:50','1','2022-02-27 08:26:56',_binary '\0',120),(110,'admin110','$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm','小王',NULL,NULL,NULL,'','15601691300',0,'',0,'0:0:0:0:0:0:0:1','2024-07-20 22:23:17','1','2022-02-22 00:56:14',NULL,'2024-07-20 22:23:17',_binary '\0',121),(111,'test','$2a$10$mRMIYLDtRHlf6.9ipiqH1.Z.bh/R9dO9d5iHiGYPigi6r5KOoR2Wm','测试用户',NULL,NULL,'[]','','',0,'',0,'0:0:0:0:0:0:0:1','2023-12-30 11:42:17','110','2022-02-23 13:14:33',NULL,'2023-12-30 11:42:17',_binary '\0',121),(112,'newobject','$2a$04$dB0z8Q819fJWz0hbaLe6B.VfHCjYgWx6LFfET5lyz3JwcqlyCkQ4C','新对象',NULL,100,'[]','','15601691235',1,'',0,'0:0:0:0:0:0:0:1','2024-03-16 23:11:38','1','2022-02-23 19:08:03',NULL,'2024-03-16 23:11:38',_binary '\0',1),(113,'aoteman','$2a$10$0acJOIk2D25/oC87nyclE..0lzeu9DtQ/n3geP4fkun/zIVRhHJIO','芋道',NULL,NULL,NULL,'','15601691300',0,'',0,'127.0.0.1','2022-03-19 18:38:51','1','2022-03-07 21:37:58',NULL,'2022-03-19 18:38:51',_binary '\0',122),(114,'hrmgr','$2a$10$TR4eybBioGRhBmDBWkqWLO6NIh3mzYa8KBKDDB5woiGYFVlRAi.fu','hr 小姐姐',NULL,NULL,'[5]','','15601691236',1,'',0,'0:0:0:0:0:0:0:1','2024-03-24 22:21:05','1','2022-03-19 21:50:58',NULL,'2024-03-24 22:21:05',_binary '\0',1),(115,'aotemane','$2a$04$GcyP0Vyzb2F2Yni5PuIK9ueGxM0tkZGMtDwVRwrNbtMvorzbpNsV2','阿呆','11222',102,'[1,2]','<EMAIL>','15601691229',2,'',0,'',NULL,'1','2022-04-30 02:55:43','1','2024-04-04 09:37:14',_binary '\0',1),(117,'admin123','$2a$04$sEtimsHu9YCkYY4/oqElHem2Ijc9ld20eYO6lN.g/21NfLUTDLB9W','测试号02','1111',100,'[2]','','15601691234',1,'',0,'0:0:0:0:0:0:0:1','2024-10-02 10:16:20','1','2022-07-09 17:40:26',NULL,'2024-10-02 10:16:20',_binary '\0',1),(118,'goudan','$2a$04$OB1SuphCdiLVRpiYRKeqH.8NYS7UIp5vmIv1W7U4w6toiFeOAATVK','狗蛋',NULL,103,'[1]','','15601691239',1,'',0,'0:0:0:0:0:0:0:1','2024-03-17 09:10:27','1','2022-07-09 17:44:43','1','2024-09-06 21:40:43',_binary '\0',1),(131,'hh','$2a$04$cdU6S6oVXGY3cAc.cGAOdu7pIKnCMG7TJJTfvkMq7/a8cax2bpqIq','呵呵',NULL,100,'[]','<EMAIL>','15601882312',1,'',0,'',NULL,'1','2024-04-27 08:45:56','1','2024-12-26 16:39:34',_binary '\0',1),(139,'wwbwwb','$2a$04$aOHoFbQU6zfBk/1Z9raF/ugTdhjNdx7culC1HhO0zvoczAnahCiMq','小秃头',NULL,NULL,NULL,'','',0,'',0,'0:0:0:0:0:0:0:1','2024-09-10 21:03:58',NULL,'2024-09-10 21:03:58',NULL,'2024-09-10 21:03:58',_binary '\0',1),(140,'admin001','$2a$04$yCPOKuhpBPdzD1hCIbkLUuE7.6wM66NNntcQVdVOUu8AdAhGo4/G2','贾三胖','',115,'[1]','','',2,'',0,'',NULL,'1','2024-12-26 16:41:43','1','2024-12-26 16:42:04',_binary '\0',1);
/*!40000 ALTER TABLE `system_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yudao_demo01_contact`
--

DROP TABLE IF EXISTS `yudao_demo01_contact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yudao_demo01_contact` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字',
                                        `sex` tinyint(1) NOT NULL COMMENT '性别',
                                        `birthday` datetime NOT NULL COMMENT '出生年',
                                        `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '简介',
                                        `avatar` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='示例联系人表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yudao_demo01_contact`
--

LOCK TABLES `yudao_demo01_contact` WRITE;
/*!40000 ALTER TABLE `yudao_demo01_contact` DISABLE KEYS */;
INSERT INTO `yudao_demo01_contact` VALUES (1,'土豆',2,'2023-11-07 00:00:00','<p>天蚕土豆！呀</p>','http://127.0.0.1:48080/admin-api/infra/file/4/get/46f8fa1a37db3f3960d8910ff2fe3962ab3b2db87cf2f8ccb4dc8145b8bdf237.jpeg','1','2023-11-15 23:34:30','1','2023-11-15 23:47:39',_binary '\0',1);
/*!40000 ALTER TABLE `yudao_demo01_contact` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yudao_demo02_category`
--

DROP TABLE IF EXISTS `yudao_demo02_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yudao_demo02_category` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                         `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字',
                                         `parent_id` bigint(20) NOT NULL COMMENT '父级编号',
                                         `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                         `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='示例分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yudao_demo02_category`
--

LOCK TABLES `yudao_demo02_category` WRITE;
/*!40000 ALTER TABLE `yudao_demo02_category` DISABLE KEYS */;
INSERT INTO `yudao_demo02_category` VALUES (1,'土豆',0,'1','2023-11-15 23:34:30','1','2023-11-16 20:24:23',_binary '\0',1),(2,'番茄',0,'1','2023-11-16 20:24:00','1','2023-11-16 20:24:15',_binary '\0',1),(3,'怪怪',0,'1','2023-11-16 20:24:32','1','2023-11-16 20:24:32',_binary '\0',1),(4,'小番茄',2,'1','2023-11-16 20:24:39','1','2023-11-16 20:24:39',_binary '\0',1),(5,'大番茄',2,'1','2023-11-16 20:24:46','1','2023-11-16 20:24:46',_binary '\0',1),(6,'11',3,'1','2023-11-24 19:29:34','1','2023-11-24 19:29:34',_binary '\0',1);
/*!40000 ALTER TABLE `yudao_demo02_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yudao_demo03_course`
--

DROP TABLE IF EXISTS `yudao_demo03_course`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yudao_demo03_course` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                       `student_id` bigint(20) NOT NULL COMMENT '学生编号',
                                       `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字',
                                       `score` tinyint(4) NOT NULL COMMENT '分数',
                                       `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生课程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yudao_demo03_course`
--

LOCK TABLES `yudao_demo03_course` WRITE;
/*!40000 ALTER TABLE `yudao_demo03_course` DISABLE KEYS */;
INSERT INTO `yudao_demo03_course` VALUES (2,2,'语文',66,'1','2023-11-16 23:21:49','1','2024-09-17 10:55:30',_binary '',1),(3,2,'数学',22,'1','2023-11-16 23:21:49','1','2024-09-17 10:55:30',_binary '',1),(6,5,'体育',23,'1','2023-11-16 23:22:46','1','2023-11-16 15:44:40',_binary '',1),(7,5,'计算机',11,'1','2023-11-16 23:22:46','1','2023-11-16 15:44:40',_binary '',1),(8,5,'体育',23,'1','2023-11-16 23:22:46','1','2023-11-16 15:47:09',_binary '',1),(9,5,'计算机',11,'1','2023-11-16 23:22:46','1','2023-11-16 15:47:09',_binary '',1),(10,5,'体育',23,'1','2023-11-16 23:22:46','1','2024-09-17 10:55:28',_binary '',1),(11,5,'计算机',11,'1','2023-11-16 23:22:46','1','2024-09-17 10:55:28',_binary '',1),(12,2,'电脑',33,'1','2023-11-17 00:20:42','1','2023-11-16 16:20:45',_binary '',1),(13,9,'滑雪',12,'1','2023-11-17 13:13:20','1','2024-09-17 10:55:26',_binary '',1),(14,9,'滑雪',12,'1','2023-11-17 13:13:20','1','2024-09-17 10:55:49',_binary '',1),(15,5,'体育',23,'1','2023-11-16 23:22:46','1','2024-09-17 18:55:29',_binary '\0',1),(16,5,'计算机',11,'1','2023-11-16 23:22:46','1','2024-09-17 18:55:29',_binary '\0',1),(17,2,'语文',66,'1','2023-11-16 23:21:49','1','2024-09-17 18:55:31',_binary '\0',1),(18,2,'数学',22,'1','2023-11-16 23:21:49','1','2024-09-17 18:55:31',_binary '\0',1),(19,9,'滑雪',12,'1','2023-11-17 13:13:20','1','2024-09-17 18:55:50',_binary '\0',1);
/*!40000 ALTER TABLE `yudao_demo03_course` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yudao_demo03_grade`
--

DROP TABLE IF EXISTS `yudao_demo03_grade`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yudao_demo03_grade` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                      `student_id` bigint(20) NOT NULL COMMENT '学生编号',
                                      `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字',
                                      `teacher` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '班主任',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生班级表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yudao_demo03_grade`
--

LOCK TABLES `yudao_demo03_grade` WRITE;
/*!40000 ALTER TABLE `yudao_demo03_grade` DISABLE KEYS */;
INSERT INTO `yudao_demo03_grade` VALUES (7,2,'三年 2 班','周杰伦','1','2023-11-16 23:21:49','1','2024-09-17 18:55:31',_binary '\0',1),(8,5,'华为','遥遥领先','1','2023-11-16 23:22:46','1','2024-09-17 18:55:29',_binary '\0',1),(9,9,'小图','小娃111','1','2023-11-17 13:10:23','1','2024-09-17 18:55:50',_binary '\0',1);
/*!40000 ALTER TABLE `yudao_demo03_grade` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yudao_demo03_student`
--

DROP TABLE IF EXISTS `yudao_demo03_student`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yudao_demo03_student` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
                                        `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字',
                                        `sex` tinyint(4) NOT NULL COMMENT '性别',
                                        `birthday` datetime NOT NULL COMMENT '出生日期',
                                        `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '简介',
                                        `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yudao_demo03_student`
--

LOCK TABLES `yudao_demo03_student` WRITE;
/*!40000 ALTER TABLE `yudao_demo03_student` DISABLE KEYS */;
INSERT INTO `yudao_demo03_student` VALUES (2,'小白',1,'2023-11-16 00:00:00','<p>厉害</p>','1','2023-11-16 23:21:49','1','2024-09-17 18:55:31',_binary '\0',1),(5,'大黑',2,'2023-11-13 00:00:00','<p>你在教我做事?</p>','1','2023-11-16 23:22:46','1','2024-09-17 18:55:29',_binary '\0',1),(9,'小花',1,'2023-11-07 00:00:00','<p>哈哈哈</p>','1','2023-11-17 00:04:47','1','2024-09-17 18:55:50',_binary '\0',1);
/*!40000 ALTER TABLE `yudao_demo03_student` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-12-26  9:33:16
