ALTER TABLE `order`
    MODIFY COLUMN `platform_product_id` BIGINT NOT NULL COMMENT '平台商品ID',
    MODIFY COLUMN `merchant_id` BIGINT NOT NULL COMMENT '商户ID',
    MODIFY COLUMN `supplier_id` BIGINT COMMENT '供应商ID',
    MODIFY COLUMN `product_type` TINYINT NOT NULL COMMENT '商品类型（直充/卡密）',
    MODIFY COLUMN `fapiao_type` TINYINT COMMENT '发票类型';


ALTER TABLE `upstream_order`
    MODIFY COLUMN `supplier_id` BIGINT NOT NULL COMMENT '供应商ID';