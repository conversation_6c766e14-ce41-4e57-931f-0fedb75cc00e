SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_SCHEMA = DATABASE()
                 AND TABLE_NAME = 'order'
                 AND COLUMN_NAME = 'loop_rule_id');

SET @sqlstmt := IF(@exist > 0,
                   'ALTER TABLE `order` DROP COLUMN `loop_rule_id`;',
                   'SELECT ''Column loop_rule_id does not exist, skipping.''');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

ALTER TABLE `order` ADD COLUMN `loop_rule_id` INT NULL DEFAULT NULL COMMENT '轮询规则编号';


ALTER TABLE `order_log` MODIFY `create_time` DATETIME(3) NULL COMMENT '创建时间';