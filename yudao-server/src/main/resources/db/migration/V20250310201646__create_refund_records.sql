DROP TABLE IF EXISTS `refund_records`;
CREATE TABLE refund_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '退款ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_number VARCHAR(20) NOT NULL COMMENT '退款单号',
    amount DECIMAL(10, 2) NOT NULL COMMENT '退款金额',
    quantity INT NOT NULL DEFAULT 1 COMMENT '退款数量',
    create_time DATETIME NOT NULL DEFAULT current_timestamp COMMENT '退款时间',
    status VARCHAR(20) NOT NULL COMMENT '退款状态',
    remarks TEXT COMMENT '备注',
    update_time DATETIME ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
    creator VARCHAR(255) COMMENT '创建者',
    updater VARCHAR(255) COMMENT '更新者',
    deleted bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
);
