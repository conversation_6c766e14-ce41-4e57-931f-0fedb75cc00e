delete from system_dict_type where type = 'allowed_channels' and id > 0;
delete from system_dict_data where dict_type = 'allowed_channels' and id > 0;
INSERT INTO `system_dict_type` (
  `name`, `type`, `status`, `remark`,
  `creator`, `create_time`, `updater`, `update_time`, `deleted`, `deleted_time`
)
VALUES
(
  '允许渠道', -- 字典名称
  'allowed_channels', -- 字典类型
  0, -- 状态（0正常）
  '商品的允许销售渠道字典', -- 备注
  'system', -- 创建者
  '2025-01-12 00:00:00', -- 创建时间
  'system', -- 更新者
  '2025-01-12 00:00:00', -- 更新时间
  b'0', -- 是否删除（0 未删除）
  NULL -- 删除时间
);
INSERT INTO `system_dict_data` (
  `dict_type`, `label`, `value`, `sort`, `status`,
  `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`
)
VALUES
('allowed_channels', '不限', '1', 1, 0, '不限销售渠道', 'system', '2025-01-12 00:00:00', 'system', '2025-01-12 00:00:00', b'0'),
('allowed_channels', '异业', '2', 2, 0, '异业销售渠道', 'system', '2025-01-12 00:00:00', 'system', '2025-01-12 00:00:00', b'0'),
('allowed_channels', '金融', '3', 3, 0, '金融销售渠道', 'system', '2025-01-12 00:00:00', 'system', '2025-01-12 00:00:00', b'0'),
('allowed_channels', '电商', '4', 4, 0, '电商销售渠道', 'system', '2025-01-12 00:00:00', 'system', '2025-01-12 00:00:00', b'0'),
('allowed_channels', '运营商', '5', 5, 0, '运营商销售渠道', 'system', '2025-01-12 00:00:00', 'system', '2025-01-12 00:00:00', b'0');
DROP TABLE IF EXISTS product_allowed_channels;
CREATE TABLE `product_allowed_channels` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `channel_value` varchar(100) NOT NULL COMMENT '渠道值（对应字典的value字段）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_product_id` (`product_id`),
  KEY `idx_channel_value` (`channel_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品与允许渠道关系表';

