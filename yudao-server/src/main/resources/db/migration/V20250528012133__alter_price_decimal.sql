alter table merchant
    modify balance decimal(28, 8) default 0.00 null comment '余额';

alter table merchant_product
    modify price decimal(25, 5) null comment '商户商品价格';

alter table merchant_product_change_notify_log
    modify change_price decimal(25, 5) default 0.00 null comment '变更价格';

alter table merchant_recharge_request
    modify before_amount_of_money decimal(25, 5) null comment '修改前金额';

alter table merchant_recharge_request
    modify after_amount_of_money decimal(25, 5) null comment '修改后金额';

alter table platform_product
    modify price decimal(25, 5) null comment '商品价格';

alter table price_change_record
    modify before_price decimal(25, 5) default 0.00 not null comment '变更前价格';

alter table price_change_record
    modify after_price decimal(25, 5) default 0.00 not null comment '变更后价格';

alter table price_change_record
    modify change_price decimal(25, 5) default 0.00 not null comment '变更金额';

alter table refund_records
    modify amount decimal(28, 8) not null comment '退款金额';

alter table supplier_balance
    modify balance decimal(28, 8) default 0.00000 null comment '余额';

alter table supplier_product
    modify price decimal(25, 5) default 0.00 null comment '结算价格';

alter table upstream_order
    modify unit_sale_price decimal(28, 8) not null comment '上游售价';

alter table upstream_order
    modify order_amount decimal(28, 8) not null comment '订单金额（密价 * 销售数量）';

alter table upstream_order
    modify total_cost decimal(28, 8) not null comment '上游成本金额';

alter table `order`
    modify unit_sale_price decimal(28, 8) not null comment '售价（密价）';

alter table `order`
    modify total_cost decimal(28, 8) null comment '总成本';

alter table `order`
    modify order_amount decimal(28, 8) not null comment '订单金额（密价 * 销售数量）';