DELETE
FROM `system_menu`
WHERE name = '批充批采'
  AND path = '/batch_charge';


INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('批充批采', '', 1, 5, 0, '/batch_charge', 'fa:buysellads', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

select id
into @parent_id
from `system_menu`
where name = '批充批采';

DELETE
FROM `system_menu`
WHERE name = '商品列表'
  AND path = 'product';

INSERT INTO `system_menu` (name, permission, type, sort, `parent_id`, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('商品列表', 'batch-charge:product:query', 2, 1, @parent_id, 'product', 'ep:list', 'batchCharge/Product/Index',
        'ProductList', 0, true, true, true, '1', current_timestamp, '1', current_timestamp, false);
