drop table if exists product_category;
create table product_category
(
    id            BIGINT auto_increment primary key comment '类目编号',
    name          <PERSON><PERSON><PERSON><PERSON>(100) not null comment '类目名称',
    type          VA<PERSON>HAR(64) comment '类目类型',
    parent_id     BIGINT                                 default null comment '父类目编号',
    path          VARCHAR(255)                           default '' comment '类目路径',
    sort          INT                                    default 0 comment '排序值',
    `creator`     varchar(64) collate utf8mb4_unicode_ci default '' comment '创建者',
    `create_time` datetime     not null                  default current_timestamp comment '创建时间',
    `updater`     varchar(64) collate utf8mb4_unicode_ci default '' comment '更新者',
    `update_time` datetime     not null                  default current_timestamp on update current_timestamp comment '更新时间',
    `deleted`     bit(1)       not null                  default b'0' comment '是否删除'
) engine = InnoDB
  default charset = utf8mb4
  collate = utf8mb4_unicode_ci comment ='商品类目表';