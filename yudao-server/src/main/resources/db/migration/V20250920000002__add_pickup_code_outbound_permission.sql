-- 添加新的充值类型字典数据
INSERT INTO `system_dict_data` (`dict_type`, `label`, `value`, `sort`, `status`, `remark`, `creator`)
VALUES ('recharge_type', '提货码出库', 'PICKUP_CODE_OUTBOUND', 5, 0, '提货码出库扣款', '1');

-- 添加提货码出库权限菜单项
SELECT id INTO @voucher_pickup_code_parent_id FROM `system_menu` WHERE component_name = 'VoucherPickupCode' LIMIT 1;

-- 添加提货码出库权限菜单项
INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('提货码出库', 'product:voucher-pickup-code:outbound', 3, 5, @voucher_pickup_code_parent_id, '', '',
        '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);
