-- Create indexes (idempotent approach using stored procedure)

DELIMITER //

DROP PROCEDURE IF EXISTS CreateIndexIfNotExists//

CREATE PROCEDURE CreateIndexIfNotExists(
    IN table_name VARCHAR(128),
    IN index_name VARCHAR(128), 
    IN index_columns TEXT
)
BEGIN
    DECLARE index_count INT DEFAULT 0;
    
    SELECT COUNT(1) INTO index_count
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = table_name
      AND INDEX_NAME = index_name;
    
    IF index_count = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', index_columns, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END//

DELIMITER ;

-- Create indexes using the procedure
CALL CreateIndexIfNotExists('platform_product', 'idx_product_category_status_name', 'category_id, status, name, id');
CALL CreateIndexIfNotExists('platform_product', 'idx_product_category_id', 'category_id');
CALL CreateIndexIfNotExists('platform_product', 'idx_product_status', 'status');
CALL CreateIndexIfNotExists('platform_product', 'idx_product_name', 'name');
CALL CreateIndexIfNotExists('platform_product', 'idx_product_with_loop_list', 'with_loop_list');

-- Clean up
DROP PROCEDURE IF EXISTS CreateIndexIfNotExists;