SET @exist := (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_SCHEMA = DATABASE()
                 AND TABLE_NAME = 'platform_product'
                 AND COLUMN_NAME = 'loop_rule_fields');

SET @sqlstmt := IF(@exist > 0,
                   'ALTER TABLE `platform_product` DROP COLUMN `loop_rule_fields`;',
                   'SELECT ''Column loop_rule_fields does not exist, skipping.''');

PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;


alter table platform_product
    add column loop_rule_fields json comment '轮询规则可选字段';
