-- 修改商户商品表的授权方式字段，支持多选
-- 将原来的单个整数改为JSON数组格式

-- 1. 添加新的临时字段
ALTER TABLE `merchant_product` 
ADD COLUMN `auth_types` JSON COMMENT '授权方式列表（JSON数组）' AFTER `auth_type`;

-- 2. 将现有的auth_type数据迁移到auth_types字段
UPDATE `merchant_product` 
SET `auth_types` = JSON_ARRAY(auth_type) 
WHERE auth_type IS NOT NULL;

-- 3. 删除旧的auth_type字段
ALTER TABLE `merchant_product` 
DROP COLUMN `auth_type`;

-- 4. 将auth_types字段重命名为auth_type
ALTER TABLE `merchant_product` 
CHANGE COLUMN `auth_types` `auth_type` JSON COMMENT '授权方式列表（JSON数组，支持多选）';
