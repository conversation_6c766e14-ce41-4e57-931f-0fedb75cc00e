DELETE
FROM `system_menu`
WHERE name = '订单管理' AND path = '/order';
-- 获取订单管理的父菜单ID
INSERT INTO `system_menu` (name, permission, type, sort, parent_id, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('订单管理', '', 1, 1, 0, '/order', 'ep:document', '', '', 0, true, true, true, '1',
        current_timestamp, '1', current_timestamp, false);

select id
into @parent_id
from `system_menu`
where name = '订单管理';

DELETE
FROM `system_menu`
WHERE name = '订单列表' AND path = 'order';

INSERT INTO `system_menu` (name, permission, type, sort, `parent_id`, path, icon, component,
                           component_name, status, visible, keep_alive, always_show, creator, create_time,
                           updater, update_time, deleted)
VALUES ('订单列表', 'order:system-order:query', 2, 1, @parent_id, 'order', 'ep:list', 'order/SystemOrder/Index',
        'OrderList', 0, true, true, true, '1', current_timestamp, '1', current_timestamp, false);
