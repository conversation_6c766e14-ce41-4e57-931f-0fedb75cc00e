DROP TABLE IF EXISTS merchant_product;
CREATE TABLE merchant_product
(
    `id`                  bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `platform_product_id` bigint   NOT NULL COMMENT '平台商品ID',
    `auth_type`           tinyint  NOT NULL COMMENT '授权类型: 1-直充, 2-批充',
    `price`               decimal(10, 2)    DEFAULT 0.00 COMMENT '商户商品价格',
    `create_time`         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`             varchar(64)       DEFAULT '' COMMENT '创建者',
    `updater`             varchar(64)       DEFAULT '' COMMENT '更新者',
    `deleted`             bit(1)   NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                   `idx_platform_product_id` (`platform_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商户商品表';