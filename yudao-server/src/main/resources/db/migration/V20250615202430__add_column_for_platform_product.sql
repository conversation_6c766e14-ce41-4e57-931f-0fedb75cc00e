alter table `platform_product`
    add column `allow_negative_profit` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否允许负利润' AFTER `multi_charge`;

alter table `merchant_product`
    add column `allow_negative_profit` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否允许负利润';

select id
into @parent_id
from `system_menu`
where name = '平台商品列表';

INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`,
                           `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`,
                           `updater`, `update_time`, `deleted`)
VALUES ('负利润配置', 'platform-product:edit-allow-negative-profit', 3, 4, @parent_id, '', '', '', '', 0, b'1', b'1',
        b'1', '1', '2025-05-28 20:28:47', '1', '2025-05-28 20:28:53', b'0');
