delete
from system_dict_type
where type = 'product_type'
  and id > 0;

delete
from system_dict_data
where dict_type = 'product_type'
  and id > 0;

INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`)
VALUES ('商品类型', -- 字典名称
        'product_type', -- 字典类型
        0, -- 状态（0正常）
        '商品类型', -- 备注
        1);

INSERT INTO `system_dict_data` (`dict_type`, `label`, `value`, `sort`, `status`,
                                `remark`, `creator`)
VALUES ('product_type', '直充', '1', 1, 0, '直充', '1'),
       ('product_type', '卡密', '2', 2, 0, '卡密', '1'),
       ('product_type', '实物', '3', 3, 0, '实物', '1');


delete
from system_dict_type
where type = 'supplier_product_type'
  and id > 0;

delete
from system_dict_data
where dict_type = 'supplier_product_type'
  and id > 0;

INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`)
VALUES ('供应商商品类型', -- 字典名称
        'supplier_product_type', -- 字典类型
        0, -- 状态（0正常）
        '供应商商品类型', -- 备注
        1);

INSERT INTO `system_dict_data` (`dict_type`, `label`, `value`, `sort`, `status`,
                                `remark`, `creator`)
VALUES ('supplier_product_type', '直充', '1', 1, 0, '直充', '1'),
       ('supplier_product_type', '卡密', '2', 2, 0, '卡密', '1');
