name: quanyi-system

services:
  admin-backend:
    container_name: admin-backend
    image: admin-backend
    ports:
      - "48080:48080"
    restart: always
    networks:
      - quanyi_network
    environment:
      TOPIC_ID: ${ADMIN_TOPIC_ID:-2785deb1-8abb-4543-a96e-9613e14f7db3}
      SPRING_PROFILES_ACTIVE: ${ACTIVE_PROFILES:-dev}
      JAVA_OPTS: |
        -Xms2048m
        -Xmx2048m
        -Djava.security.egd=file:/dev/./urandom
      ARGS:
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.flyway.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.flyway.user=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.flyway.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-quanyi-redis}
        ${REDIS_PASSWORD_ARG}
    logging:
      driver: "json-file"
      options:
        max-size: "2g"
        max-file: "3"

networks:
  quanyi_network:
    name: quanyi_network
    external: true