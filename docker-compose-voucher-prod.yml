name: quanyi-voucher-api

services:
  voucher-api-58080:
    container_name: voucher-api-58080
    image: admin-backend
    ports:
      - "58080:58080"
    restart: always
    networks:
      - quanyi_network
    environment:
      TOPIC_ID: ${VOUCHER_TOPIC_ID:-de82fd64-778b-4bae-a76c-16bb0b310dca}
      SPRING_PROFILES_ACTIVE: ${ACTIVE_PROFILES:-dev}
      JAVA_OPTS: |
        -Xms2048m
        -Xmx2048m
        -Djava.security.egd=file:/dev/./urandom
      ARGS:
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-quanyi-redis}
        ${REDIS_PASSWORD_ARG}
    logging:
      driver: "json-file"
      options:
        max-size: "2g"
        max-file: "3"

  voucher-api-58031:
    container_name: voucher-api-58031
    image: admin-backend
    ports:
      - "58031:58080"
    restart: always
    networks:
      - quanyi_network
    environment:
      TOPIC_ID: ${VOUCHER_TOPIC_ID:-87a8ef58-c58b-464e-8405-2f64c5e23cce}
      SPRING_PROFILES_ACTIVE: ${ACTIVE_PROFILES:-dev}
      JAVA_OPTS: |
        -Xms2048m
        -Xmx2048m
        -Djava.security.egd=file:/dev/./urandom
      ARGS:
        --spring.datasource.dynamic.datasource.master.url=${MASTER_DATASOURCE_URL:-************************************************************************************************************************************************}
        --spring.datasource.dynamic.datasource.master.username=${MASTER_DATASOURCE_USERNAME:-root}
        --spring.datasource.dynamic.datasource.master.password=${MASTER_DATASOURCE_PASSWORD:-123456}
        --spring.data.redis.host=${REDIS_HOST:-quanyi-redis}
        ${REDIS_PASSWORD_ARG}
    logging:
      driver: "json-file"
      options:
        max-size: "2g"
        max-file: "3"

networks:
  quanyi_network:
    name: quanyi_network
    external: true