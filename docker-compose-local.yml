version: "3.4"

name: quanyi-system

services:
  quanyi-mysql:
    container_name: quanyi-mysql
    image: mysql:8
    ports:
      - "3306:3306"
    restart: always
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-quanyi_system}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-123456}
    volumes:
      - mysql:/var/lib/mysql/

  quanyi-redis:
    container_name: quanyi-redis
    image: redis:6-alpine
    ports:
      - "6379:6379"
    restart: always
    volumes:
      - redis:/data

volumes:
  mysql:
    driver: local
  redis:
    driver: local
